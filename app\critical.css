/* app/critical.css - CONSOLIDATED CRITICAL STYLES */

/* ✅ Your custom CSS variables and styles */
:root {
    --background: #ffffff;
    --foreground: #0b0b0b;
    --border: #e5e7eb;
    --hero-yellow: #f6a03c;
    --hero-yellow-light: #f7ab4f;
    
    /* Dark mode overrides for your app */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --primary: 187 96% 42%;
    --muted-foreground: 215 20.2% 65.1%;
  }
  
  /* ✅ Essential body and scroll styles */
  body {
    background: rgb(1, 6, 11); /* Your dark theme */
    color: #e2e8f0;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    font-family: system-ui, -apple-system, sans-serif;
  }
  
  /* ✅ Your custom scrollbar styles */
  *::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }
  
  *::-webkit-scrollbar-track {
    background: rgba(11, 22, 36, 0.3);
    border-radius: 10px;
  }
  
  *::-webkit-scrollbar-thumb {
    background-color: var(--hero-yellow);
    border-radius: 10px;
    box-shadow: 0 0 6px rgba(246, 160, 60, 0.5);
  }
  
  *::-webkit-scrollbar-thumb:hover {
    background-color: var(--hero-yellow-light);
    box-shadow: 0 0 8px rgba(246, 160, 60, 0.7);
  }
  
  /* ✅ Essential HTML/layout styles */
  html { 
    scroll-behavior: smooth; 
    overflow-x: hidden;
  }
  
  /* ✅ Critical layout utilities */
  .min-h-screen { min-height: 100vh; }
  .relative { position: relative; }
  .absolute { position: absolute; }
  .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
  .w-full { width: 100%; }
  .h-full { height: 100%; }
  .z-0 { z-index: 0; }
  .z-10 { z-index: 10; }
  .pointer-events-none { pointer-events: none; }
  
  /* ✅ Critical text utilities */
  .text-white { color: #ffffff; }
  .text-slate-200 { color: #e2e8f0; }
  .font-sans { font-family: var(--font-sans), system-ui, sans-serif; }
  .antialiased { 
    -webkit-font-smoothing: antialiased; 
    -moz-osx-font-smoothing: grayscale; 
  }
  
  /* ✅ Critical spacing */
  .pt-10 { padding-top: 2.5rem; }
  .mx-auto { margin-left: auto; margin-right: auto; }
  .text-center { text-align: center; }
  .mb-6 { margin-bottom: 1.5rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mb-8 { margin-bottom: 2rem; }
  
  /* ✅ Hero section styles */
  .hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.1;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
    font-weight: 600;
    color: #e2e8f0;
    margin-bottom: 1.5rem;
    line-height: 1.1;
  }
  
  /* ✅ Button styles using your variables */
  .btn-primary {
    background-color: var(--hero-yellow);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
    transition: all 0.2s ease;
  }
  
  .btn-primary:hover {
    background-color: var(--hero-yellow-light);
    transform: translateY(-1px);
  }
  
  /* ✅ Container */
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  /* ✅ Navigation essentials */
  .nav {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 50;
    background: rgba(1, 6, 11, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
  }
  
  .nav-link {
    color: #e2e8f0;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s;
  }
  
  .nav-link:hover {
    color: var(--hero-yellow);
  }
  
  /* ✅ Responsive design */
  @media (min-width: 640px) {
    .container { padding: 0 2rem; }
    .nav-container { padding: 0 2rem; }
    .hero-title { font-size: 3rem; }
    .hero-subtitle { font-size: 1.5rem; }
  }
  
  @media (min-width: 768px) {
    .hero-title { font-size: 3.75rem; }
    .hero-subtitle { font-size: 1.875rem; }
  }
  
  @media (min-width: 1024px) {
    .container { padding: 0 4rem; }
    .nav-container { padding: 0 4rem; }
  }