import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { trackAnalysisComplete } from '@/lib/ga-events';

interface AnalyzedSection {
  category: string;
  confidence: string;
  key_information: string[];
  unique_selling_points: string[];
  relevant_keywords: string[];
  alignment_with_expectations: string;
  potential_impact: string;
  clarity_and_organization: string;
  formatting_and_style: string;
  missing_information: string[];
  potential_concerns: string[];
  improvement_suggestions: string[];
  effectiveness_rating: number;
  explanation: string;
  summary: string;
  consistency: string;
  job_target_alignment: string;
  ats_performance: string;
  ats_optimization_tips: string[];
}

interface ResumeAnalysisProps {
  data: {
    analyzed_sections: AnalyzedSection[];
  };
  analysisStartTime?: number;
}

const ResumeAnalysis: React.FC<ResumeAnalysisProps> = ({
  data,
  analysisStartTime
}) => {
  const [hasTrackedCompletion, setHasTrackedCompletion] = useState(false);

  // Track analysis completion when results are first displayed
  useEffect(() => {
    if (data?.analyzed_sections?.length > 0 && !hasTrackedCompletion) {
      const analysisTime = analysisStartTime
        ? Date.now() - analysisStartTime
        : undefined;

      trackAnalysisComplete({
        analysis_type: 'resume_analysis',
        analysis_time_ms: analysisTime,
        feature_used: 'resume_analyzer',
        user_journey: 'dashboard'
      });

      setHasTrackedCompletion(true);
    }
  }, [data, hasTrackedCompletion, analysisStartTime]);

  return (
    <div className="space-y-6">
      {data.analyzed_sections.map((section, index) => (
        <Card key={index} className="shadow-md">
          <CardHeader className="bg-primary/10">
            <CardTitle className="text-lg font-semibold text-primary">
              {section.category}
            </CardTitle>
            <div className="text-sm text-gray-500">
              Confidence: {section.confidence}
            </div>
          </CardHeader>
          <CardContent className="pt-4 space-y-4">
            {Object.entries(section).map(([key, value]) => {
              if (typeof value === 'string' && value.trim() !== '') {
                return (
                  <div key={key}>
                    <h4 className="font-semibold mb-2 capitalize">
                      {key.replace(/_/g, ' ')}:
                    </h4>
                    <p className="text-sm text-gray-700">{value}</p>
                  </div>
                );
              } else if (Array.isArray(value) && value.length > 0) {
                return (
                  <div key={key}>
                    <h4 className="font-semibold mb-2 capitalize">
                      {key.replace(/_/g, ' ')}:
                    </h4>
                    <ul className="list-disc pl-5 space-y-2">
                      {value.map((item, idx) => (
                        <li key={idx} className="text-sm text-gray-700">
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              }
              return null;
            })}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ResumeAnalysis;
