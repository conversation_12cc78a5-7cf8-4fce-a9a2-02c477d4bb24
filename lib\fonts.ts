// lib/fonts.ts - Component-specific font loading
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>_Condensed
} from 'next/font/google';

// ✅ Heavy display fonts - load only when needed
export const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
  display: 'swap',
  weight: ['700', '800', '900']
});

export const anton = <PERSON>({
  subsets: ['latin'],
  variable: '--font-anton',
  weight: ['400'],
  display: 'swap'
});

export const bebasNeue = Bebas_Neue({
  subsets: ['latin'],
  variable: '--font-bebas-neue',
  weight: ['400'],
  display: 'swap'
});

export const oswald = <PERSON>({
  subsets: ['latin'],
  variable: '--font-oswald',
  weight: ['500', '600', '700'],
  display: 'swap'
});

export const robotoCondensed = Roboto_Condensed({
  subsets: ['latin'],
  variable: '--font-roboto-condensed',
  weight: ['400', '700'],
  display: 'swap'
});

// ✅ Usage example for components that need specific fonts:
/*
// components/home/<USER>
import { montserrat } from '@/lib/fonts';

export default function Hero() {
  return (
    <div className={`${montserrat.variable} font-montserrat`}>
      <h1 className="font-montserrat text-8xl">Your Hero Title</h1>
    </div>
  );
}

// components/home/<USER>
import { robotoCondensed } from '@/lib/fonts';

export default function Features() {
  return (
    <div className={`${robotoCondensed.variable} font-roboto-condensed`}>
      <h2 className="font-roboto-condensed text-4xl">Features</h2>
    </div>
  );
}
*/
