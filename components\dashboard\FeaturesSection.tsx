import React from 'react';
import { FeatureCard } from './FeatureCard';
import { Job, Resume, ServiceType } from '@/app/types/globalTypes';
import { SERVICE_CREDIT_COSTS } from '@/lib/credit-costs';
import { SHOW_MARKET_TRENDS } from '@/lib/featureFlags';
import { FEATURE_TO_SERVICE_TYPE } from '@/app/types/featureTypes';

interface FeaturesSectionProps {
  features: {
    title: string;
    description: string;
    icon: string;
    disabled: boolean;
    hasStoredResults: boolean;
    isCareerMatching: boolean;
    isResumeSelected: boolean;
    requiresJob?: boolean;
    requiresResume?: boolean;
  }[];
  onActivate: (featureTitle: string) => Promise<void>;
  isResumeSelected: boolean;
  activeFeature: string | null;
  selectedJob: Job | null;
  selectedResume: Resume | null;
}

// Combined service type mapping
const COMBINED_SERVICE_TYPES: Record<string, ServiceType> = {
  ...(FEATURE_TO_SERVICE_TYPE as unknown as Record<string, ServiceType>),
  'Recruitment Agencies': 'RECRUITMENT_AGENCIES',
  'Salary Predictor': 'SALARY_PREDICTOR',
  'Gamified Search': 'GAMIFIED_SEARCH'
};

export const FeaturesSection: React.FC<FeaturesSectionProps> = ({
  features,
  ...props
}) => {
  // Add a key to force re-render when features change
  const [updateKey, setUpdateKey] = React.useState(0);

  // Force re-render when features change
  React.useEffect(() => {
    setUpdateKey((prev) => prev + 1);
    console.log('FeaturesSection: Features updated, forcing re-render');
  }, [features]);

  // Listen for storage events to update UI
  React.useEffect(() => {
    const handleStorageEvent = () => {
      console.log('FeaturesSection: Storage event detected, forcing re-render');
      setUpdateKey((prev) => prev + 1);
    };

    window.addEventListener('storage', handleStorageEvent);
    return () => window.removeEventListener('storage', handleStorageEvent);
  }, []);

  // Filter out Market Trends if the feature flag is false
  const visibleFeatures = features.filter((feature) => {
    if (feature.title === 'Market Trends') {
      return SHOW_MARKET_TRENDS;
    }
    return true;
  });

  return (
    <>
      <h2 className="text-2xl font-roboto-condensed font-semibold mb-6 mt-8 text-white">
        Available{' '}
        <span className="text-yellow-400 text-shadow-yellow">Features</span>
      </h2>
      <div
        key={updateKey}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {visibleFeatures.map((feature, index) => {
          const serviceType = COMBINED_SERVICE_TYPES[feature.title];
          const credits = serviceType
            ? SERVICE_CREDIT_COSTS[serviceType]
            : undefined;

          return (
            <FeatureCard
              key={index}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              onActivate={() => {
                console.log('Feature activated:', feature.title);
                props.onActivate(feature.title);
              }}
              disabled={feature.disabled}
              hasStoredResults={feature.hasStoredResults}
              isCareerMatching={feature.isCareerMatching}
              isResumeSelected={props.isResumeSelected}
              isPending={props.activeFeature === feature.title}
              selectedJobId={
                props.selectedJob ? String(props.selectedJob.id) : null
              }
              resumeId={
                props.selectedResume ? String(props.selectedResume.id) : null
              }
              credits={credits}
            />
          );
        })}
      </div>
    </>
  );
};
