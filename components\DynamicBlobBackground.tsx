'use client';

import { useEffect, useState, useRef } from 'react';
import { usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';

// ✅ STABLE BLOB COMPONENT - Prevents constant re-renders
function BlobBackgroundInner() {
  const pathname = usePathname();
  const [blobPositions, setBlobPositions] = useState<number[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const isCalculatingRef = useRef(false);
  const lastPathnameRef = useRef(pathname);

  const updateBlobLayout = () => {
    if (isCalculatingRef.current) return;

    isCalculatingRef.current = true;

    // Single measurement with proper TypeScript typing
    const parentWrapper = document.querySelector(
      '.relative.min-h-screen'
    ) as HTMLElement;
    const mainContent = document.querySelector('main') as HTMLElement;

    let contentHeight = window.innerHeight; // Fallback

    if (parentWrapper) {
      contentHeight = Math.max(
        parentWrapper.scrollHeight,
        parentWrapper.offsetHeight,
        window.innerHeight
      );
    } else if (mainContent) {
      contentHeight = Math.max(
        mainContent.scrollHeight,
        mainContent.offsetHeight,
        window.innerHeight
      );
    }

    // Calculate blob positions
    const blobCount = 10;
    const padding = 100;
    const availableHeight = contentHeight - padding * 2;

    const positions = Array.from({ length: blobCount }, (_, index) => {
      const percentage = index / (blobCount - 1);
      return padding + availableHeight * percentage;
    });

    setBlobPositions(positions);
    setIsLoaded(true);

    // Trigger grow + fade-in animation
    setTimeout(() => {
      setIsVisible(true);
    }, 100);

    isCalculatingRef.current = false;
  };

  // Only recalculate when pathname actually changes
  useEffect(() => {
    if (lastPathnameRef.current !== pathname) {
      lastPathnameRef.current = pathname;
      setIsLoaded(false);
      setIsVisible(false);

      const initTimeout = setTimeout(() => {
        updateBlobLayout();
      }, 500);

      return () => {
        clearTimeout(initTimeout);
      };
    }
  }, [pathname]);

  // Initial load only
  useEffect(() => {
    if (!isLoaded && lastPathnameRef.current === pathname) {
      const initTimeout = setTimeout(() => {
        updateBlobLayout();
      }, 1000);

      return () => {
        clearTimeout(initTimeout);
      };
    }
  }, []); // Empty dependency array - only runs once

  // Minimal event listeners
  useEffect(() => {
    if (!isLoaded) return;

    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        updateBlobLayout();
      }, 300); // Debounced
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
    };
  }, [isLoaded]);

  if (!isLoaded || blobPositions.length === 0) {
    return null;
  }

  const blobs = [
    {
      color: 'rgba(59,130,246,0.20)',
      size: 600,
      animation: 'animate-blob',
      delay: '0s',
      position: 'left-[10vw]',
      fadeDelay: '0ms'
    },
    {
      color: 'rgba(168,85,247,0.20)',
      size: 700,
      animation: 'animate-blob-roam',
      delay: '0s',
      position: 'right-[15vw]',
      fadeDelay: '800ms'
    },
    {
      color: 'rgba(234,179,8,0.15)',
      size: 650,
      animation: 'animate-blob-wander',
      delay: '0s',
      position: 'left-[50vw]',
      fadeDelay: '1600ms'
    },
    {
      color: 'rgba(6,182,212,0.20)',
      size: 600,
      animation: 'animate-blob-pulse',
      delay: '0s',
      position: 'right-[20vw]',
      fadeDelay: '2400ms'
    },
    {
      color: 'rgba(139,69,19,0.12)',
      size: 550,
      animation: 'animate-blob',
      delay: '0s',
      position: 'left-[25vw]',
      fadeDelay: '3200ms'
    },
    {
      color: 'rgba(34,197,94,0.18)',
      size: 500,
      animation: 'animate-blob-roam',
      delay: '0s',
      position: 'right-[30vw]',
      fadeDelay: '4000ms'
    },
    {
      color: 'rgba(236,72,153,0.16)',
      size: 580,
      animation: 'animate-blob-wander',
      delay: '0s',
      position: 'left-[15vw]',
      fadeDelay: '4800ms'
    },
    {
      color: 'rgba(99,102,241,0.18)',
      size: 620,
      animation: 'animate-blob-pulse',
      delay: '0s',
      position: 'right-[25vw]',
      fadeDelay: '5600ms'
    },
    {
      color: 'rgba(245,101,101,0.15)',
      size: 540,
      animation: 'animate-blob',
      delay: '0s',
      position: 'left-[40vw]',
      fadeDelay: '6400ms'
    },
    {
      color: 'rgba(168,85,247,0.12)',
      size: 600,
      animation: 'animate-blob-roam',
      delay: '0s',
      position: 'right-[10vw]',
      fadeDelay: '7200ms'
    }
  ];

  return (
    <div
      className="absolute inset-0 w-full pointer-events-none -z-50 overflow-hidden"
      style={{ height: '100%' }}
    >
      <div className="absolute inset-0 w-full h-full overflow-hidden">
        {blobs.map((blob, index) => (
          <div
            key={index}
            className={`absolute rounded-full blur-3xl ${blob.animation} ${blob.position} transition-all duration-3000 ease-out`}
            style={{
              backgroundColor: blob.color,
              animationDelay: blob.delay,
              top: `${blobPositions[index]}px`,
              width: `${blob.size}px`,
              height: `${blob.size}px`,
              willChange: 'transform, opacity',
              transform: isVisible
                ? 'translateZ(0) scale(1)'
                : 'translateZ(0) scale(0.1)',
              opacity: isVisible ? 1 : 0,
              transitionDelay: isVisible ? blob.fadeDelay : '0ms'
            }}
          />
        ))}
      </div>
    </div>
  );
}

export const DynamicBlobBackground = dynamic(
  () => Promise.resolve(BlobBackgroundInner),
  {
    ssr: false,
    loading: () => null
  }
);
