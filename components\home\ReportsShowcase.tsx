'use client';

import React from 'react';
import { ImageCarousel } from '../ImageCarousel';
import SectionHeading, { Highlight } from '../ui/section-heading';

interface ReportType {
  title: string;
  description: string;
  image: string;
  features: string[];
}

const reports: ReportType[] = [
  {
    title: 'AI Interview Analysis',
    description:
      'Detailed feedback on your interview performance with improvement suggestions',
    image: '/images/reports/interview.webp',
    features: [
      'Response Quality Score',
      'Body Language Analysis',
      'Improvement Tips'
    ]
  },
  {
    title: 'ATS CV Analysis',
    description:
      'See how your CV performs against ATS systems and job requirements',
    image: '/images/reports/ats3.webp',
    features: ['Keyword Analysis', 'Format Optimization', 'Missing Skills']
  },
  {
    title: 'AI Career Matching',
    description:
      'Discover alternative career paths that match your skills and experience',
    image: '/images/reports/careerMatching.webp',
    features: [
      'Skill Transferability',
      'Industry Matching',
      'Growth Opportunity'
    ]
  },
  {
    title: 'CV Improvement',
    description:
      "Get actionable suggestions to enhance your CV's impact and effectiveness",
    image: '/images/reports/resumeimprove2.webp',
    features: [
      'Content Optimization',
      'Format Enhancement',
      'Achievement Highlighting'
    ]
  },
  {
    title: 'CV Optimization',
    description:
      'Transform your CV with AI-powered suggestions to stand out to employers',
    image: '/images/reports/resumeimprove1.webp',
    features: [
      'Professional Formatting',
      'Content Enhancement',
      'Achievement Highlighting'
    ]
  },
  {
    title: 'ATS Compatibility Check',
    description:
      'Ensure your CV passes through Applicant Tracking Systems successfully',
    image: '/images/reports/ats1.webp',
    features: ['Parsing Test', 'Keyword Analysis', 'Format Validation']
  },
  {
    title: 'ATS Score Breakdown',
    description:
      'Detailed analysis of how your CV performs against specific ATS criteria',
    image: '/images/reports/ats2.webp',
    features: [
      'Section-by-Section Analysis',
      'Improvement Recommendations',
      'Keyword Optimization'
    ]
  },
  {
    title: 'AI Career Coach',
    description:
      'Get personalized career guidance and advice from our AI-powered coach',
    image: '/images/reports/coach.webp',
    features: [
      'Personalized Guidance',
      'Career Path Planning',
      'Industry Insights'
    ]
  },
  {
    title: 'Cover Letter Generator',
    description:
      'Create customized, professional cover letters tailored to specific job applications',
    image: '/images/reports/coverletter.webp',
    features: [
      'Job-Specific Customization',
      'Professional Templates',
      'Key Skills Highlighting'
    ]
  },
  {
    title: 'Digital Recruitment Agency',
    description:
      'AI-powered job matching and application services to streamline your job search',
    image: '/images/reports/agencies.webp',
    features: [
      'Automated Job Matching',
      'Application Tracking',
      'Interview Scheduling'
    ]
  }
];

export const ReportsShowcase = () => {
  return (
    <section className="relative isolate py-16 overflow-hidden">
      {/* Content */}
      <div className="relative z-10 mx-auto w-full max-w-[1200px] px-4">
        <div className="text-center mb-8 md:mb-12">
          <span className="text-yellow-300 font-semibold">
            POWERFUL ANALYTICS
          </span>

          <SectionHeading className="text-center max-w-4xl mx-auto">
            Personalized <Highlight>Reports</Highlight> & Insights
          </SectionHeading>
          <p className="text-slate-200 max-w-3xl mx-auto text-[16px] md:text-[18px]">
            Make data-driven decisions with our comprehensive analytics and
            actionable insights
          </p>
        </div>

        <ImageCarousel
          items={reports.map((report) => ({
            src: report.image,
            alt: report.title,
            caption: report.description
          }))}
        />
      </div>
    </section>
  );
};
