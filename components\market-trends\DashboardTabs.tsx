import React, { useState } from 'react';
import { MarketData } from '@/app/types/globalTypes';
import { OverviewTab } from './OverviewTab';
import { JobGrowthChart } from './JobGrowthChart';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { SalaryTrendsChart } from './SalaryTrendsChart';
import { SkillDemandChart } from './SkillDemandChart';
import { RemoteWorkChart } from './RemoteWorkChart';

interface DashboardTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  marketData: MarketData;
}

export const DashboardTabs: React.FC<DashboardTabsProps> = ({
  activeTab,
  onTabChange,
  marketData
}) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);

  React.useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 640);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const tabOptions = [
    { value: 'overview', label: 'Overview' },
    { value: 'jobGrowth', label: 'Job Growth' },
    { value: 'salaryTrends', label: 'Salary Trends' },
    { value: 'skillDemand', label: 'Skill Demand' },
    { value: 'remoteWork', label: 'Remote Work' }
  ];

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      {isMobile ? (
        <Select value={activeTab} onValueChange={onTabChange}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select view" />
          </SelectTrigger>
          <SelectContent>
            {tabOptions.map((tab) => (
              <SelectItem key={tab.value} value={tab.value}>
                {tab.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : (
        <TabsList className="grid w-full grid-cols-5 bg-white/10">
          {tabOptions.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className="data-[state=active]:bg-white/20 data-[state=active]:text-[hsl(var(--hero-yellow))] text-slate-300"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
      )}

      <TabsContent value="overview" className="backdrop-blur-sm bg-white/5 p-4">
        <OverviewTab marketData={marketData} />
      </TabsContent>
      <TabsContent
        value="jobGrowth"
        className="backdrop-blur-sm bg-white/5 p-4"
      >
        <JobGrowthChart jobGrowthData={marketData.jobGrowth} />
      </TabsContent>
      <TabsContent
        value="salaryTrends"
        className="backdrop-blur-sm bg-white/5 p-4"
      >
        <SalaryTrendsChart salaryTrends={marketData.salaryTrends} />
      </TabsContent>
      <TabsContent
        value="skillDemand"
        className="backdrop-blur-sm bg-white/5 p-4"
      >
        <SkillDemandChart skillDemand={marketData.skillDemand} />
      </TabsContent>
      <TabsContent
        value="remoteWork"
        className="backdrop-blur-sm bg-white/5 p-4"
      >
        <RemoteWorkChart remoteWorkTrends={marketData.remoteWorkTrends} />
      </TabsContent>
    </Tabs>
  );
};
