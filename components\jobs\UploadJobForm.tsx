import React, { useState, useEffect } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Loader2,
  AlertCircle,
  ArrowRight,
  Upload,
  FileText,
  X,
  Linkedin,
  Briefcase,
  Lightbulb,
  FileSpreadsheet,
  Copy,
  Trash2
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { UploadJobFormProps } from '@/app/types/globalTypes';
import {
  trackFileUploadStart,
  trackFileUploadSuccess,
  trackFileUploadFailure,
  trackFileUploadAbandon,
  trackJobAnalysisStart,
  trackJobAnalysisComplete,
  trackJobAnalysisFailure,
  trackButtonClick,
  trackValidationFailure,
  trackUserInteraction
} from '@/lib/ga-events';

interface JobAPIResponse {
  description?: string;
  job_summary?: string;
  job_title?: string;
  details?: string;
  error?: string;
}

type FormStatus = 'idle' | 'submitting' | 'success' | 'error' | 'processing';
type JobSource = 'linkedin' | 'indeed' | 'unknown';

const SAMPLE_JOB_POSTING = `Marketing Manager - London

We are seeking an experienced Marketing Manager to join our growing team in London. The successful candidate will be responsible for developing and executing comprehensive marketing strategies to drive brand awareness and customer acquisition.

Key Responsibilities:
• Develop and implement integrated marketing campaigns across digital and traditional channels
• Manage social media presence and content marketing initiatives
• Collaborate with sales teams to generate qualified leads
• Analyze marketing performance metrics and ROI
• Oversee brand messaging and visual identity consistency
• Coordinate with external agencies and vendors

Requirements:
• Bachelor's degree in Marketing, Business, or related field
• 3-5 years of experience in marketing management
• Proven track record in digital marketing and campaign management
• Strong analytical skills and experience with marketing analytics tools
• Excellent written and verbal communication skills
• Experience with CRM systems and marketing automation platforms

What we offer:
• Competitive salary £45,000 - £55,000
• Comprehensive benefits package
• Flexible working arrangements
• Professional development opportunities
• Modern office in Central London

To apply, please send your CV and cover letter explaining why you're the perfect fit for this role.`;

const UploadJobForm: React.FC<UploadJobFormProps> = ({
  onJobDataChange,
  onSubmit,
  onComplete,
  initialJob,
  submitButtonText,
  isLoading,
  autoAdvance = false,
  showHeader = true,
  showPreview = true,
  externalDialogOpen,
  onExternalDialogClose,
  autoOpenDialog = false
}) => {
  const [jobDescription, setJobDescription] = useState<string>(
    initialJob?.description ?? ''
  );
  const [jobLink, setJobLink] = useState<string>(initialJob?.job_link ?? '');
  const [status, setStatus] = useState<FormStatus>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [characterCount, setCharacterCount] = useState<number>(0);
  const [isFetchingJob, setIsFetchingJob] = useState<boolean>(false);
  const [showDescriptionInput, setShowDescriptionInput] =
    useState<boolean>(true);
  const [lastFetchedLink, setLastFetchedLink] = useState<string>('');
  const [jobTitle, setJobTitle] = useState<string>('');
  const [isEditorOpen, setIsEditorOpen] = useState<boolean>(false);
  const [fetchStatus, setFetchStatus] = useState<
    'idle' | 'fetching' | 'fetched' | 'error'
  >('idle');
  const [mounted, setMounted] = useState(false);
  const [jobSource, setJobSource] = useState<JobSource>('unknown');
  const [fetchStartTime, setFetchStartTime] = useState<number | null>(null);
  const [fetchTimeElapsed, setFetchTimeElapsed] = useState<number>(0);
  const [fetchTimeout, setFetchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Tracking states
  const [uploadStartTime, setUploadStartTime] = useState<number | null>(null);
  const [analysisStartTime, setAnalysisStartTime] = useState<number | null>(
    null
  );
  const [hasTrackedUploadStart, setHasTrackedUploadStart] = useState(false);
  const [dialogOpenTime, setDialogOpenTime] = useState<number | null>(null);
  const [lastInteractionTime, setLastInteractionTime] = useState<number>(
    Date.now()
  );

  const dialogOpen =
    externalDialogOpen !== undefined ? externalDialogOpen : isEditorOpen;

  const detectJobSource = (url: string): JobSource => {
    const lowerUrl = url.toLowerCase();
    if (lowerUrl.includes('linkedin.com/jobs/view')) {
      return 'linkedin';
    } else if (lowerUrl.includes('indeed.com/viewjob')) {
      return 'indeed';
    }
    return 'unknown';
  };

  const getUserJourney = (): 'onboarding' | 'dashboard' | 'marketing' => {
    const path = window.location.pathname;
    if (path.includes('/onboarding') || path.includes('/upload')) {
      return 'onboarding';
    } else if (path.includes('/dashboard')) {
      return 'dashboard';
    } else {
      return 'marketing';
    }
  };

  const handleDialogClose = (open: boolean) => {
    if (!open && dialogOpenTime) {
      // Track dialog abandonment if user spent less than 10 seconds and didn't submit
      const timeInDialog = Date.now() - dialogOpenTime;
      if (timeInDialog < 10000 && !hasTrackedUploadStart) {
        trackFileUploadAbandon({
          file_type: 'job_description',
          abandon_stage: 'dialog_opened',
          time_spent_ms: timeInDialog,
          user_journey: getUserJourney()
        });
      }
      setDialogOpenTime(null);
    } else if (open && !dialogOpenTime) {
      setDialogOpenTime(Date.now());
      trackUserInteraction({
        interaction_type: 'dialog_open',
        element_type: 'job_upload_modal',
        user_journey: getUserJourney()
      });
    }

    if (externalDialogOpen !== undefined) {
      if (!open && onExternalDialogClose) {
        onExternalDialogClose();
      }
    } else {
      setIsEditorOpen(open);
    }
  };

  const isValidJobUrl = (url: string): boolean => {
    return detectJobSource(url) !== 'unknown';
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && initialJob) {
      setJobDescription(initialJob.description ?? '');
      setJobLink(initialJob.job_link ?? '');
      setJobTitle(initialJob.title ?? '');
      setCharacterCount((initialJob.description ?? '').length);
    }
  }, [initialJob, mounted]);

  useEffect(() => {
    setJobSource(detectJobSource(jobLink));
  }, [jobLink]);

  useEffect(() => {
    setCharacterCount(jobDescription.length);
  }, [jobDescription]);

  useEffect(() => {
    if (autoOpenDialog && mounted) {
      handleDialogClose(true);
    }
  }, [autoOpenDialog, mounted]);

  // Track abandonment on component unmount
  useEffect(() => {
    return () => {
      if (uploadStartTime && !hasTrackedUploadStart) {
        trackFileUploadAbandon({
          file_type: 'job_description',
          abandon_stage: 'component_unmount',
          time_spent_ms: Date.now() - uploadStartTime,
          user_journey: getUserJourney()
        });
      }
    };
  }, [uploadStartTime, hasTrackedUploadStart]);

  // Existing fetch and timeout logic stays the same...
  useEffect(() => {
    if (fetchStatus === 'fetching' && fetchStartTime !== null) {
      const timer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - fetchStartTime) / 1000);
        setFetchTimeElapsed(elapsed);

        if (elapsed > 30 && !fetchTimeout) {
          const timeout = setTimeout(() => {
            if (fetchStatus === 'fetching') {
              setFetchStatus('error');
              setStatus('error');
              setErrorMessage(
                'Fetching job details is taking too long. You can try again or enter the job description manually.'
              );
              setShowDescriptionInput(true);
              setIsFetchingJob(false);

              // Track fetch timeout
              trackFileUploadFailure({
                file_type: 'job_description',
                error_type: 'fetch_timeout',
                error_message: 'Job URL fetch timeout after 30+ seconds',
                file_source: jobSource,
                user_journey: getUserJourney()
              });

              toast({
                title: 'Fetch timeout',
                description:
                  'The operation took too long. Try manual entry instead.',
                variant: 'destructive'
              });
            }
          }, 10000);

          setFetchTimeout(timeout);
        }
      }, 1000);

      return () => {
        clearInterval(timer);
        if (fetchTimeout) {
          clearTimeout(fetchTimeout);
        }
      };
    }

    return () => {
      if (fetchTimeout) {
        clearTimeout(fetchTimeout);
        setFetchTimeout(null);
      }
    };
  }, [fetchStatus, fetchStartTime, fetchTimeout, jobSource]);

  const updateJobData = (newDescription: string, newLink: string) => {
    if (onJobDataChange) {
      onJobDataChange(newDescription, newLink);
    }
  };

  const handleAutoSubmit = async (description: string, link: string) => {
    try {
      setStatus('processing');
      setAnalysisStartTime(Date.now());

      // Track analysis start
      trackJobAnalysisStart({
        analysis_type: 'job_description',
        file_source: jobSource,
        input_method: link ? 'url_fetch' : 'manual_entry',
        character_count: description.length,
        user_journey: getUserJourney()
      });

      const div = document.createElement('div');
      div.innerHTML = description;
      const plainText = div.textContent || div.innerText || '';

      const job = await onSubmit(plainText, link);
      setStatus('success');

      // Track successful analysis
      const analysisTime = analysisStartTime
        ? Date.now() - analysisStartTime
        : 0;
      trackJobAnalysisComplete({
        analysis_type: 'job_description',
        analysis_time_ms: analysisTime,
        file_source: jobSource,
        input_method: link ? 'url_fetch' : 'manual_entry',
        character_count: description.length,
        user_journey: getUserJourney()
      });

      // Close dialog via appropriate method
      if (externalDialogOpen !== undefined && onExternalDialogClose) {
        onExternalDialogClose();
      } else {
        setIsEditorOpen(false);
      }

      toast({
        title: 'Success',
        description: 'Job analyzed successfully!'
      });

      if (onComplete) {
        onComplete(job);
      }

      if (autoAdvance) {
        window.dispatchEvent(new CustomEvent('jobAnalysisComplete'));
      }
    } catch (error) {
      setStatus('error');
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to analyze job';
      setErrorMessage(errorMessage);
      setShowDescriptionInput(true);

      // Track analysis failure
      const analysisTime = analysisStartTime
        ? Date.now() - analysisStartTime
        : 0;
      trackJobAnalysisFailure({
        analysis_type: 'job_description',
        error_type: 'analysis_processing_error',
        error_message: errorMessage,
        analysis_time_ms: analysisTime,
        file_source: jobSource,
        input_method: jobLink ? 'url_fetch' : 'manual_entry',
        character_count: description.length,
        user_journey: getUserJourney()
      });
    }
  };

  const fetchJobDetails = async (
    linkToFetch: string = jobLink
  ): Promise<void> => {
    if (!linkToFetch) {
      const errorMsg = 'Please enter a job URL';
      setErrorMessage(errorMsg);
      setStatus('error');

      trackValidationFailure({
        field_name: 'job_url',
        error_message: errorMsg,
        user_journey: getUserJourney()
      });
      return;
    }

    if (!isValidJobUrl(linkToFetch)) {
      setShowDescriptionInput(true);

      trackValidationFailure({
        field_name: 'job_url',
        error_message:
          'Unsupported URL format - only LinkedIn and Indeed supported',
        validation_value: linkToFetch,
        user_journey: getUserJourney()
      });
      return;
    }

    if (linkToFetch === lastFetchedLink) {
      return;
    }

    try {
      setIsFetchingJob(true);
      setFetchStatus('fetching');
      setErrorMessage('');
      setStatus('idle');
      setLastFetchedLink(linkToFetch);
      setFetchStartTime(Date.now());
      setFetchTimeElapsed(0);

      // Track URL fetch start
      trackFileUploadStart({
        file_type: 'job_description',
        upload_method: 'url_fetch',
        file_source: detectJobSource(linkToFetch),
        user_journey: getUserJourney()
      });

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_URL}/api/fetch-linkedin-job`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ url: linkToFetch }),
          signal: controller.signal
        }
      );

      clearTimeout(timeoutId);

      const data: JobAPIResponse = await response.json();

      if (!response.ok) {
        throw new Error(
          data.details || data.error || 'Failed to fetch job details'
        );
      }

      let description = '';
      if (data.description && data.description !== 'N/A') {
        description = data.description;
      } else if (data.job_summary && data.job_summary !== 'N/A') {
        description = data.job_summary;
      } else {
        throw new Error('No job description found');
      }

      const fetchTime = fetchStartTime ? Date.now() - fetchStartTime : 0;

      // Track successful URL fetch
      trackFileUploadSuccess({
        file_type: 'job_description',
        upload_method: 'url_fetch',
        file_source: detectJobSource(linkToFetch),
        upload_duration_ms: fetchTime,
        file_size_chars: description.length,
        user_journey: getUserJourney()
      });

      setJobDescription(description);
      setJobTitle(data.job_title ?? 'Unknown Title');
      updateJobData(description, linkToFetch);
      setShowDescriptionInput(false);
      setFetchStatus('fetched');
      setFetchStartTime(null);

      await handleAutoSubmit(description, linkToFetch);
    } catch (error: unknown) {
      setStatus('error');
      setShowDescriptionInput(true);
      setFetchStatus('error');

      const fetchTime = fetchStartTime ? Date.now() - fetchStartTime : 0;
      setFetchStartTime(null);

      let errorMessage = 'Failed to fetch job details';
      let errorType = 'fetch_error';

      if (error instanceof DOMException && error.name === 'AbortError') {
        errorMessage =
          'Request timed out. Please try again or enter job details manually.';
        errorType = 'fetch_timeout';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      // Track fetch failure
      trackFileUploadFailure({
        file_type: 'job_description',
        upload_method: 'url_fetch',
        error_type: errorType,
        error_message: errorMessage,
        file_source: detectJobSource(linkToFetch),
        upload_duration_ms: fetchTime,
        user_journey: getUserJourney()
      });

      setErrorMessage(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsFetchingJob(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (jobDescription.trim().length < 30) {
      const errorMsg = 'Please enter a job description (minimum 30 characters)';
      setErrorMessage(errorMsg);
      setStatus('error');

      trackValidationFailure({
        field_name: 'job_description',
        error_message: errorMsg,
        validation_value: jobDescription.length.toString(),
        user_journey: getUserJourney()
      });
      return;
    }

    // Track manual upload start if not already tracked
    if (!hasTrackedUploadStart) {
      trackFileUploadStart({
        file_type: 'job_description',
        upload_method: 'manual_entry',
        file_source: 'manual',
        user_journey: getUserJourney()
      });
      setHasTrackedUploadStart(true);
      setUploadStartTime(Date.now());
    }

    await handleAutoSubmit(jobDescription, jobLink);
  };

  const handleButtonClick = async () => {
    trackButtonClick({
      button_name: 'analyze_job_description',
      button_location: 'job_upload_dialog',
      user_journey: getUserJourney()
    });

    if (jobDescription.trim().length < 30) {
      const errorMsg = 'Please enter a job description (minimum 30 characters)';
      setErrorMessage(errorMsg);
      setStatus('error');

      trackValidationFailure({
        field_name: 'job_description',
        error_message: errorMsg,
        validation_value: jobDescription.length.toString(),
        user_journey: getUserJourney()
      });
      return;
    }

    // Track manual upload start if not already tracked
    if (!hasTrackedUploadStart) {
      trackFileUploadStart({
        file_type: 'job_description',
        upload_method: 'manual_entry',
        file_source: 'manual',
        user_journey: getUserJourney()
      });
      setHasTrackedUploadStart(true);
      setUploadStartTime(Date.now());
    }

    await handleAutoSubmit(jobDescription, jobLink);
  };

  const handleJobDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newValue = e.target.value;
    setJobDescription(newValue);
    setCharacterCount(newValue.length);
    updateJobData(newValue, jobLink);
    setLastInteractionTime(Date.now());

    // Track first meaningful input
    if (!hasTrackedUploadStart && newValue.length >= 10) {
      trackFileUploadStart({
        file_type: 'job_description',
        upload_method: 'manual_entry',
        file_source: 'manual',
        user_journey: getUserJourney()
      });
      setHasTrackedUploadStart(true);
      setUploadStartTime(Date.now());
    }
  };

  const handleUseExample = () => {
    trackUserInteraction({
      interaction_type: 'click',
      element_type: 'use_example_button',
      user_journey: getUserJourney()
    });

    setJobDescription(SAMPLE_JOB_POSTING);
    setCharacterCount(SAMPLE_JOB_POSTING.length);
    updateJobData(SAMPLE_JOB_POSTING, jobLink);

    // Track example usage as upload start
    if (!hasTrackedUploadStart) {
      trackFileUploadStart({
        file_type: 'job_description',
        upload_method: 'example_used',
        file_source: 'example',
        user_journey: getUserJourney()
      });
      setHasTrackedUploadStart(true);
      setUploadStartTime(Date.now());
    }

    toast({
      title: 'Example loaded',
      description:
        'Sample job posting has been added. You can edit it or use as-is.'
    });
  };

  const handleClearAll = () => {
    trackUserInteraction({
      interaction_type: 'click',
      element_type: 'clear_all_button',
      user_journey: getUserJourney()
    });

    // Track abandonment if there was content
    if (hasTrackedUploadStart && uploadStartTime) {
      trackFileUploadAbandon({
        file_type: 'job_description',
        abandon_stage: 'manual_clear',
        time_spent_ms: Date.now() - uploadStartTime,
        user_journey: getUserJourney()
      });
    }

    setJobDescription('');
    setCharacterCount(0);
    updateJobData('', jobLink);
    setHasTrackedUploadStart(false);
    setUploadStartTime(null);

    toast({
      title: 'Cleared',
      description: 'Job description has been cleared.'
    });
  };

  const getHostname = (url: string): string => {
    if (!url || url.trim() === '') {
      return 'No URL provided';
    }

    try {
      const httpsIndex = url.indexOf('https://');
      const httpIndex = url.indexOf('http://');
      const startIndex = Math.max(
        httpsIndex !== -1 ? httpsIndex : -1,
        httpIndex !== -1 ? httpIndex : -1
      );

      let cleanUrl = url;
      if (startIndex !== -1) {
        cleanUrl = url.substring(startIndex);
      }

      if (!cleanUrl.startsWith('http')) {
        cleanUrl = `https://${cleanUrl}`;
      }

      const urlObject = new URL(cleanUrl);
      return urlObject.hostname || 'Invalid URL';
    } catch {
      const domainMatch = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\s]+)/i);
      return domainMatch ? domainMatch[1] : 'Invalid URL';
    }
  };

  const getJobSourceLabel = () => {
    switch (jobSource) {
      case 'linkedin':
        return 'LinkedIn';
      case 'indeed':
        return 'Indeed';
      default:
        return 'Unknown';
    }
  };

  if (!mounted) return null;

  return (
    <div className="space-y-4 pt-8">
      {/* Header section */}
      {showHeader && (
        <div className="flex flex-col space-y-3 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <p className="text-sm text-white">
              Add a job posting to analyze against your CV
            </p>
          </div>
          <Button
            data-upload-job-button
            onClick={() => {
              trackButtonClick({
                button_name: 'add_job_details',
                button_location: 'job_form_header',
                user_journey: getUserJourney()
              });
              handleDialogClose(true);
            }}
            className="w-full sm:w-auto bg-[hsl(var(--hero-yellow))] text-[hsl(var(--foreground))] hover:bg-[hsl(var(--hero-yellow-light))] active:scale-95 transition-all"
          >
            <Upload className="w-4 h-4 mr-2" />
            {jobTitle ? 'Update Job Details' : 'Add Job Details'}
          </Button>
        </div>
      )}

      {/* Job preview section */}
      {showPreview && (
        <div className="w-full">
          {jobTitle ? (
            <div
              onClick={() => {
                trackUserInteraction({
                  interaction_type: 'click',
                  element_type: 'job_preview_card',
                  user_journey: getUserJourney()
                });
                handleDialogClose(true);
              }}
              className="group cursor-pointer rounded-lg backdrop-blur-sm bg-white/10 border border-white/20 p-4 hover:border-[hsl(var(--hero-yellow))] shadow-lg transition-all"
            >
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 p-2 bg-white/10 rounded">
                  <FileText
                    size={40}
                    className="text-[hsl(var(--hero-yellow))]"
                  />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium truncate text-white">
                    {jobTitle}
                  </h3>
                  <div className="flex flex-col sm:flex-row sm:gap-3">
                    <span className="text-sm text-slate-300">
                      {jobLink ? getHostname(jobLink) : 'No URL provided'}
                    </span>
                    <span className="text-sm text-slate-300">
                      {characterCount} characters
                    </span>
                    {jobSource !== 'unknown' && (
                      <span className="text-sm text-[hsl(var(--hero-yellow))] font-medium">
                        {getJobSourceLabel()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div
              onClick={() => {
                trackUserInteraction({
                  interaction_type: 'click',
                  element_type: 'job_upload_placeholder',
                  user_journey: getUserJourney()
                });
                handleDialogClose(true);
              }}
              className="flex flex-col items-center justify-center rounded-lg backdrop-blur-sm bg-white/10 border border-white/20 border-dashed p-6 text-center hover:border-[hsl(var(--hero-yellow))] cursor-pointer shadow-lg transition-all"
            >
              <FileText className="h-10 w-10 text-white mb-3" />
              <p className="text-sm font-medium text-white">
                No job details added yet
              </p>
              <p className="text-xs text-slate-300 mt-1">
                Add a job posting to get started
              </p>
            </div>
          )}
        </div>
      )}

      {/* Job Editor Dialog - Improved Design */}
      <Dialog open={dialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-5xl w-[96vw] max-h-[90vh] p-0 backdrop-blur-md bg-gradient-to-br from-slate-900/95 to-slate-800/95 border border-white/20 shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 pb-4 border-b border-white/10 flex-shrink-0">
            <DialogTitle className="flex items-center gap-3 text-xl font-semibold text-white">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Upload className="h-5 w-5 text-yellow-400" />
              </div>
              {jobTitle ? 'Update Job Details' : 'Add Job Details'}
            </DialogTitle>
          </div>

          {/* Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-6 min-h-0">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Label and character count */}
              <div className="flex justify-between items-center">
                <label className="text-lg font-semibold text-white flex items-center gap-2">
                  <FileText className="h-5 w-5 text-yellow-400" />
                  Job Description
                </label>
                <div className="flex items-center gap-3">
                  <span
                    className={`text-sm px-3 py-1 rounded-full ${
                      characterCount < 30
                        ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                        : 'bg-green-500/20 text-green-400 border border-green-500/30'
                    }`}
                  >
                    {characterCount} characters{' '}
                    {characterCount < 30 && '(minimum 30)'}
                  </span>
                </div>
              </div>

              {/* Textarea */}
              <div className="relative">
                <Textarea
                  value={jobDescription}
                  onChange={handleJobDescriptionChange}
                  className="min-h-[300px] sm:min-h-[350px] bg-white/5 border-white/20 text-white placeholder:text-slate-400 focus:border-yellow-400 focus:ring-yellow-400/30 rounded-xl text-base leading-relaxed resize-none"
                  placeholder="Paste any job posting from LinkedIn, Indeed, company websites, or job boards here...

📋 Tips for best results:
• Copy the complete job description including requirements and responsibilities
• Include company information if available
• Don't worry about formatting - our AI will analyze the content"
                  disabled={isLoading || status === 'processing'}
                />

                {/* Character count overlay for empty state */}
                {characterCount === 0 && (
                  <div className="absolute bottom-4 right-4 text-xs text-slate-500">
                    Start typing or paste a job description...
                  </div>
                )}
              </div>

              {/* Helper section */}
              <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                <div className="flex items-start gap-3 mb-4">
                  <Lightbulb className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-white mb-1">
                      How to get the best results
                    </h4>
                    <p className="text-sm text-slate-300 leading-relaxed">
                      Find a job you want to apply for, copy the entire job
                      posting, and paste it here. Our AI will analyze the
                      requirements and give you personalized optimization tips
                      for your CV.
                    </p>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex flex-wrap gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleUseExample}
                    className="border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10 hover:border-yellow-500/50"
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Use example job posting
                  </Button>

                  {characterCount > 0 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleClearAll}
                      className="border-red-500/30 text-red-400 hover:bg-red-500/10 hover:border-red-500/50"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear all
                    </Button>
                  )}
                </div>
              </div>

              {/* Error message */}
              {errorMessage && (
                <Alert className="border-red-500/50 bg-red-500/10 rounded-xl">
                  <AlertCircle className="h-5 w-5 text-red-400" />
                  <AlertDescription className="text-red-300 ml-2">
                    {errorMessage}
                  </AlertDescription>
                </Alert>
              )}
            </form>
          </div>

          {/* Footer - Fixed at bottom */}
          <div className="border-t border-white/10 p-6 bg-white/5 flex-shrink-0">
            <Button
              type="button"
              onClick={handleButtonClick}
              disabled={
                isLoading || status === 'processing' || characterCount < 30
              }
              className="w-full h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-semibold rounded-xl shadow-lg hover:shadow-xl transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:hover:scale-100"
            >
              {isLoading || status === 'processing' ? (
                <div className="flex items-center gap-3">
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Analyzing your job posting...</span>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <ArrowRight className="h-5 w-5" />
                  <span>{submitButtonText || 'Analyze Job'}</span>
                </div>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UploadJobForm;
