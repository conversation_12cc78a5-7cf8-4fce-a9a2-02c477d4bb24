import Script from 'next/script';

export interface JsonLdProps {
  id: string;
  data: string | object;
}

export default function JsonLd({ id, data }: JsonLdProps) {
  const json = typeof data === 'string' ? data : JSON.stringify(data);

  return (
    <Script
      id={id}
      type="application/ld+json"
      /* `afterInteractive` is the default, so you can omit this line
         if you’d like */
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{ __html: json }}
    />
  );
}
