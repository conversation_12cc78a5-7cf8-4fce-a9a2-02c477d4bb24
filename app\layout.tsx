// app/layout.tsx - Final optimized version
import './globals.css';
import { constructMetadata } from '@/lib/seo-config';
import localFont from 'next/font/local';
import { Inter } from 'next/font/google';
import type { Metadata } from 'next';

// ✅ CRITICAL: Direct imports for server components
import { ClientComponentsWrapper } from '@/components/ClientComponents';
import LayoutWrapper from '@/components/layout/LayoutWrapper';
import { TooltipProvider } from '@/components/ui/tooltip';
import { DynamicLayoutEnhancements } from '@/components/layout/DynamicLayoutEnhancements';
import {
  AnalyticsProvider,
  EnhancedPerformanceMonitor
} from '@/components/analytics/AnalyticsProvider';

// ✅ Font optimization - already optimized
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  weight: ['400', '600'],
  preload: true,
  fallback: ['system-ui', 'arial']
});

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
  weight: '100 900',
  display: 'swap',
  preload: false,
  fallback: ['system-ui', 'arial']
});

export const metadata: Metadata = {
  metadataBase: new URL('https://jobspaceai.com'),
  ...constructMetadata({
    path: '/'
  }),
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png'
  }
};

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  // ✅ Safely handle environment variables
  const clarityProjectId = process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID;
  const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

  return (
    <html lang="en" className="dark dark-theme" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* ✅ OPTIMIZED: Minimal DNS prefetch - only when needed */}
        <link rel="dns-prefetch" href="//ylsypkzjfqwwaheobapi.supabase.co" />
        <link
          rel="preconnect"
          href="https://ylsypkzjfqwwaheobapi.supabase.co"
          crossOrigin="anonymous"
        />

        {/* ✅ Conditional DNS prefetch - only if analytics enabled */}
        {gaId && (
          <>
            <link rel="dns-prefetch" href="//www.googletagmanager.com" />
            <link rel="dns-prefetch" href="//www.google-analytics.com" />
          </>
        )}

        {clarityProjectId && (
          <link rel="dns-prefetch" href="//www.clarity.ms" />
        )}

        {/* ✅ Critical CSS - optimized */}
        <style
          dangerouslySetInnerHTML={{
            __html: `html{scroll-behavior:smooth}body{margin:0;padding:0;overflow-x:hidden;background-color:rgb(1,6,11)}.layout-loading{background:linear-gradient(to bottom,rgb(1,6,11)0%,rgba(1,6,11,0.7)10%,transparent 35%,transparent 75%,rgba(1,6,11,0.7)90%,rgba(1,6,11,1)100%);min-height:100vh}`
          }}
        />

        <meta name="bfcache-eligible" content="true" />
      </head>
      <body
        className={`${inter.variable} ${geistSans.variable} min-h-screen font-sans antialiased dark-theme`}
      >
        {/* ✅ Consolidated Analytics Provider */}
        <AnalyticsProvider
          gaId={gaId}
          clarityId={clarityProjectId}
          enabled={true}
        >
          {/* ✅ Performance monitoring in development */}
          <EnhancedPerformanceMonitor />

          {/* ✅ Main content wrapper */}
          <div
            className="relative min-h-screen"
            style={{ backgroundColor: 'transparent' }}
          >
            <div
              className="absolute inset-0 w-full pointer-events-none z-0"
              style={{
                background:
                  'linear-gradient(to bottom, rgb(1, 6, 11) 0%, rgba(1, 6, 11,0.7) 10%, transparent 35%, transparent 75%, rgba(1, 6, 11,0.7) 90%, rgba(1, 6, 11,1) 100%)'
              }}
            />

            <DynamicLayoutEnhancements />

            <ClientComponentsWrapper gaId={gaId}>
              <TooltipProvider delayDuration={300}>
                <main
                  className="relative z-10 pt-10"
                  style={{ backgroundColor: 'transparent' }}
                >
                  <div
                    style={{
                      backgroundColor: 'transparent',
                      minHeight: '100vh'
                    }}
                  >
                    <LayoutWrapper>{children}</LayoutWrapper>
                  </div>
                </main>
              </TooltipProvider>
            </ClientComponentsWrapper>
          </div>
        </AnalyticsProvider>
      </body>
    </html>
  );
}
