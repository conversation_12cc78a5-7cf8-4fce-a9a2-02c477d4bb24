/* Non-critical styles - loaded after initial render */

/* Custom scrollbar styling */
*::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }
  
  *::-webkit-scrollbar-track {
    background: rgba(11, 22, 36, 0.3);
    border-radius: 10px;
  }
  
  *::-webkit-scrollbar-thumb {
    background-color: hsl(var(--hero-yellow));
    border-radius: 10px;
    box-shadow: 0 0 6px rgba(246, 160, 60, 0.5);
  }
  
  *::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--hero-yellow-light));
    box-shadow: 0 0 8px rgba(246, 160, 60, 0.7);
  }
  
  /* Advanced animations */
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes floatIn {
    0% {
      opacity: 0;
      transform: translateY(20px) rotate(1deg);
    }
    100% {
      opacity: 1;
      transform: translateY(0) rotate(1deg);
    }
  }
  
  @keyframes selectPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(246, 160, 60, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(246, 160, 60, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(246, 160, 60, 0);
    }
  }
  
  @keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
  }
  
  @keyframes colorShift {
    0% { filter: hue-rotate(0deg); }
    25% { filter: hue-rotate(10deg); }
    50% { filter: hue-rotate(0deg); }
    75% { filter: hue-rotate(-10deg); }
    100% { filter: hue-rotate(0deg); }
  }
  
  /* Non-critical animation classes */
  .animate-slideInLeft { animation: slideInLeft 0.8s ease-out forwards; }
  .animate-floatIn { animation: floatIn 1s ease-out forwards; }
  .animate-select-pulse { animation: selectPulse 0.8s cubic-bezier(0.4, 0, 0.6, 1); }
  .animate-progress { animation: progress 2s ease-in-out infinite; }
  
  /* Text shadow utilities */
  .text-shadow-sm { text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); }
  .text-shadow-md { text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5); }
  .text-shadow-yellow { text-shadow: 0 0 8px rgba(246, 160, 60, 0.6); }
  
  /* Glow effect */
  .glow-effect {
    position: relative;
  }
  
  .glow-effect::before {
    content: '';
    position: absolute;
    inset: -10px;
    background: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.15),
      transparent 70%
    );
    z-index: -1;
    border-radius: 24px;
  }
  
  /* Color shift animation for headline text */
  .headline-gradient {
    animation: colorShift 4s ease infinite;
  }
  
  /* Parallax unveil effect for cards */
  .mock {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.2;
    transform: translateY(40px) rotate(-4deg);
  }
  .mock.reveal {
    opacity: 1;
    transform: translateY(0) rotate(-4deg);
    z-index: 20;
  }
  
  /* Audio player styling */
  audio {
    filter: sepia(20%) saturate(70%) grayscale(1) contrast(99%) invert(12%);
    mix-blend-mode: screen;
    width: 100%;
    height: 40px;
    border-radius: 8px;
  }
  
  .audio-player {
    -webkit-appearance: none;
    appearance: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px;
    height: 50px;
  }
  
  .audio-player-clean {
    -webkit-appearance: none;
    appearance: none;
    height: 44px;
    width: 100%;
    background: transparent;
  }
  
  /* Animation delays */
  .animation-delay-2000 { animation-delay: 2s; }
  .animation-delay-4000 { animation-delay: 4s; }
  .animation-delay-6000 { animation-delay: 6s; }