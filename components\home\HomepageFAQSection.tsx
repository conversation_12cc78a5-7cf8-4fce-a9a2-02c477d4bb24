'use client';

import React from 'react';
import { FAQItem } from 'lib/faq';
import { homepageFaqs } from 'lib/homepageFaqs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import SectionHeading, { Highlight } from '../ui/section-heading';

// Homepage-specific FAQs targeting long-tail SEO keywords
const faqs: FAQItem[] = homepageFaqs;

export const HomepageFAQSection = () => {
  return (
    <section className="relative isolate py-16 overflow-hidden">
      <div className="relative z-10 mx-auto w-full max-w-[1200px] px-4">
        <div className="text-center mb-12">
          <span className="text-yellow-300 font-semibold">
            QUESTIONS & ANSWERS
          </span>
          <SectionHeading className="text-center max-w-4xl mx-auto">
            Frequently Asked <Highlight>Questions</Highlight>
          </SectionHeading>
          <p className="text-slate-200 max-w-3xl mx-auto text-[16px] md:text-[18px]">
            Everything you need to know about AI-powered job preparation and how
            JobSpaceAI helps UK professionals land their dream jobs.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Accordion
            type="single"
            collapsible
            className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
          >
            {faqs.map(({ question, answer }, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="border-white/10"
              >
                <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-sm sm:text-lg font-medium text-left">
                  {question}
                </AccordionTrigger>
                <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
                  {answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
};
