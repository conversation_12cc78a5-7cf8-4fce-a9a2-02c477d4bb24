import { createClient } from '@/app/utils/supabase/server';
import { performMockInterview } from '@/app/utils/mockInterview';
import {
  PlanType,
  UltimatePlanResult,
  SkillGap,
  ATSAnalysisResponse,
  CoverLetterResponse,
  SkillImprovementRequest,
  CareerMatchingResponse,
  InterviewQuestionsResult,
  CareerCoachingResponse
} from '@/app/types/globalTypes';

// API Endpoints
const ATS_API_URL = `${process.env.NEXT_PUBLIC_API_URL}/ats_analysis/enhanced`;
const COVER_LETTER_URL = `${process.env.NEXT_PUBLIC_API_URL}/generate_cover_letter`;
const SKILLS_URL = `${process.env.NEXT_PUBLIC_API_URL}/analyze_skills_gap/enhanced`;
const SKILL_IMPROVEMENT_URL = `${process.env.NEXT_PUBLIC_API_URL}/improve_skills`;
const CAREER_MATCHING_URL = `${process.env.NEXT_PUBLIC_API_URL}/analyse_career_matches`;
const RAG_CHAT_URL = `${process.env.NEXT_PUBLIC_API_URL}/rag_chat`;
const MARKET_TRENDS_URL = `${process.env.NEXT_PUBLIC_URL}/api/market-trends`;

export async function POST(req: Request): Promise<Response> {
  console.log('[generate-ultimate-results] Starting request processing');

  // Create streams for streaming the response
  const encoder = new TextEncoder();
  const stream = new TransformStream();
  const writer = stream.writable.getWriter();

  // Create a shared state object to track writer status
  const state = {
    writerClosed: false,
    processingComplete: false,
    error: null as Error | null
  };

  // Helper function to send a chunk of data
  const sendChunk = async (data: Record<string, unknown>) => {
    try {
      if (state.writerClosed || state.error) {
        console.log(
          '[generate-ultimate-results] Writer is closed, cannot send chunk for status:',
          data.status
        );
        return;
      }

      const dataWithTimestamp = {
        ...data,
        _timestamp: new Date().toISOString()
      };

      console.log(
        `[generate-ultimate-results] Sending chunk for status: ${data.status}`
      );
      await writer.write(
        encoder.encode(`data: ${JSON.stringify(dataWithTimestamp)}\n\n`)
      );
      console.log(
        `[generate-ultimate-results] Successfully sent chunk for status: ${data.status}`
      );
    } catch (error) {
      console.error('[generate-ultimate-results] Error sending chunk:', error);
    }
  };

  // Helper function to safely close the writer
  const safeCloseWriter = async () => {
    if (!state.writerClosed) {
      try {
        // Set the flag first to prevent multiple close attempts
        state.writerClosed = true;
        await writer.close();
        console.log('[generate-ultimate-results] Writer closed successfully');
      } catch (error) {
        console.error(
          '[generate-ultimate-results] Error closing writer:',
          error
        );
        // Even if there's an error, we've already marked it as closed
        // so we won't try to close it again
      }
    }
  };

  // Set up response timeout
  const requestTimeout = setTimeout(async () => {
    if (!state.processingComplete) {
      state.error = new Error('Request timeout');
      console.log('[generate-ultimate-results] Request timeout reached');
      await safeCloseWriter();
    }
  }, 300000); // 5 minutes timeout

  // Create the response object
  const response = new Response(stream.readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive'
    }
  });

  // Process the request in the background
  (async () => {
    try {
      await sendChunk({ status: 'connection_established', progress: 5 });
      await processRequest(req, sendChunk, writer, requestTimeout, state);
    } catch (error) {
      console.error(
        '[generate-ultimate-results] Background processing error:',
        error
      );
      if (!state.writerClosed) {
        await sendChunk({
          error: error instanceof Error ? error.message : 'Unknown error',
          status: 'error',
          progress: 100
        });
        await safeCloseWriter();
      }
    } finally {
      state.processingComplete = true;
      clearTimeout(requestTimeout);
    }
  })();

  return response;
}

async function processRequest(
  req: Request,
  sendChunk: (data: Record<string, unknown>) => Promise<void>,
  writer: WritableStreamDefaultWriter<Uint8Array>,
  requestTimeout: NodeJS.Timeout,
  state: {
    writerClosed: boolean;
    processingComplete: boolean;
    error: Error | null;
  }
): Promise<void> {
  // Helper function to safely close the writer
  const safeCloseWriter = async () => {
    if (!state.writerClosed) {
      try {
        // Set the flag first to prevent multiple close attempts
        state.writerClosed = true;
        await writer.close();
        console.log(
          '[generate-ultimate-results] Writer closed successfully in processRequest'
        );
      } catch (error) {
        console.error(
          '[generate-ultimate-results] Error closing writer in processRequest:',
          error
        );
        // Even if there's an error, we've already marked it as closed
        // so we won't try to close it again
      }
    }
  };
  const supabase = await createClient();

  try {
    await sendChunk({ status: 'started', progress: 10 });

    // Parse request body
    const { jobDescription, resumeId, userId, creditsAmount, resumeData } =
      await req.json();

    console.log('[generate-ultimate-results] Processing request with:', {
      hasJobDescription: !!jobDescription,
      resumeId,
      userId,
      creditsAmount,
      hasResumeData: !!resumeData
    });

    if (!jobDescription || !resumeId || !userId || !creditsAmount) {
      throw new Error('Missing required fields');
    }

    // Get resume content
    let resumeContent;
    if (resumeData) {
      console.log('[generate-ultimate-results] Using provided resume data');
      resumeContent = resumeData;
    } else {
      console.log(
        '[generate-ultimate-results] Fetching resume data from database'
      );
      const { data: dbResumeData, error: resumeError } = await supabase
        .from('user_resumes')
        .select('resume, analysis_result')
        .eq('id', resumeId)
        .single();

      if (resumeError) throw resumeError;
      if (!dbResumeData.resume && !dbResumeData.analysis_result) {
        throw new Error('Resume has no content');
      }

      resumeContent = dbResumeData.analysis_result || dbResumeData.resume;
    }

    await sendChunk({ status: 'resume_processed', progress: 20 });

    const requestData = {
      resume: JSON.stringify(resumeContent),
      job_description: jobDescription
    };

    // 1. ATS Analysis
    await sendChunk({ status: 'ats_analysis_started', progress: 25 });
    const atsResponse = await fetch(ATS_API_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData)
    });

    if (!atsResponse.ok)
      throw new Error(`ATS API returned ${atsResponse.status}`);
    const atsResult = (await atsResponse.json()) as ATSAnalysisResponse;

    await sendChunk({
      status: 'ats_analysis_completed',
      progress: 35,
      atsResult
    });

    // 2. Cover Letter
    await sendChunk({ status: 'cover_letter_started', progress: 40 });
    const coverLetterResponse = await fetch(COVER_LETTER_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...requestData,
        company_name: 'Company'
      })
    });

    if (!coverLetterResponse.ok)
      throw new Error('Failed to generate cover letter');
    const coverLetter =
      (await coverLetterResponse.json()) as CoverLetterResponse;

    await sendChunk({
      status: 'cover_letter_completed',
      progress: 50,
      coverLetter
    });

    // 3. Skills Gap Analysis
    await sendChunk({ status: 'skills_gap_started', progress: 55 });
    const skillsResponse = await fetch(SKILLS_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData)
    });

    if (!skillsResponse.ok) throw new Error('Failed to analyze skills gap');
    const skillsGapResult = await skillsResponse.json();
    const typedSkillsGaps: SkillGap[] = skillsGapResult.skills_gaps || [];

    await sendChunk({
      status: 'skills_gap_completed',
      progress: 65,
      skillsGap: typedSkillsGaps
    });

    // 4. Skill Improvements
    await sendChunk({ status: 'skill_improvements_started', progress: 70 });

    // Convert resumeContent to string if it's an object
    const resumeString =
      typeof resumeContent === 'string'
        ? resumeContent
        : JSON.stringify(resumeContent);

    const skillImprovementRequest: SkillImprovementRequest = {
      resume: resumeString,
      job_description: jobDescription,
      job: jobDescription,
      skills_gap: typedSkillsGaps,
      skills: typedSkillsGaps.map((gap) => gap.skill)
    };

    console.log(
      '[generate-ultimate-results] Sending skill improvement request:',
      {
        hasResume: !!skillImprovementRequest.resume,
        hasJobDescription: !!skillImprovementRequest.job_description,
        skillsGapCount: skillImprovementRequest.skills_gap?.length || 0,
        skillsCount: skillImprovementRequest.skills?.length || 0,
        resumeType: typeof skillImprovementRequest.resume // Add this for debugging
      }
    );

    const skillImprovementResponse = await fetch(SKILL_IMPROVEMENT_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(skillImprovementRequest)
    });

    if (!skillImprovementResponse.ok) {
      const errorText = await skillImprovementResponse.text();
      console.error(
        `[generate-ultimate-results] Skill improvement API failed with status ${skillImprovementResponse.status}:`,
        errorText
      );
      throw new Error(`Failed to generate skill improvements: ${errorText}`);
    }

    const skillImprovementResult = await skillImprovementResponse.json();

    // Store the skill resources in skillImprovements
    const skillImprovements = skillImprovementResult.skill_resources || [];

    // Log the skill improvement result for debugging
    console.log('[generate-ultimate-results] Skill improvement result:', {
      hasSkillResources: !!skillImprovementResult.skill_resources,
      resourceCount: skillImprovementResult.skill_resources?.length || 0
    });

    await sendChunk({
      status: 'skill_improvements_completed',
      progress: 75,
      skillImprovements: [{ skill_resources: skillImprovements }] // Wrap in array to match UltimatePlanResult interface
    });

    // 5. Mock Interview
    await sendChunk({ status: 'mock_interview_started', progress: 80 });
    const interviewResult = await performMockInterview(
      resumeContent,
      jobDescription
    );
    const mockInterviewResult: InterviewQuestionsResult = {
      estimated_level: interviewResult.difficulty.overall,
      questions: interviewResult.questions.map((q) => q.question),
      difficulty_levels: interviewResult.questions.map((q) => q.difficulty)
    };

    await sendChunk({
      status: 'mock_interview_completed',
      progress: 85,
      mockInterview: mockInterviewResult
    });

    // 6. Career Matching
    await sendChunk({ status: 'career_matching_started', progress: 85 });
    const careerMatchingResponse = await fetch(CAREER_MATCHING_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ resume: resumeContent })
    });

    if (!careerMatchingResponse.ok)
      throw new Error('Failed to analyze career matches');
    const careerMatches =
      (await careerMatchingResponse.json()) as CareerMatchingResponse;

    await sendChunk({
      status: 'career_matching_completed',
      progress: 90,
      careerMatches
    });

    // 7. Career Coaching
    await sendChunk({ status: 'career_coaching_started', progress: 92 });

    // Convert resumeContent to string if it's an object
    const coachingResumeString =
      typeof resumeContent === 'string'
        ? resumeContent
        : JSON.stringify(resumeContent);

    // Create a properly formatted request for the RAG chat API
    const careerCoachingRequest = {
      resumes: [coachingResumeString], // API expects an array of resumes
      job_description: jobDescription,
      question:
        'Provide career coaching advice based on my resume and the job description. Include specific suggestions for career growth, addressing skill gaps, and positioning myself better for this role.',
      chat_history: [] // Include empty chat history
    };

    console.log(
      '[generate-ultimate-results] Sending career coaching request:',
      {
        hasResumes:
          !!careerCoachingRequest.resumes &&
          careerCoachingRequest.resumes.length > 0,
        hasJobDescription: !!careerCoachingRequest.job_description,
        hasQuestion: !!careerCoachingRequest.question
      }
    );

    const careerCoachingResponse = await fetch(RAG_CHAT_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(careerCoachingRequest)
    });

    if (!careerCoachingResponse.ok) {
      const errorText = await careerCoachingResponse.text();
      console.error(
        `[generate-ultimate-results] Career coaching API failed with status ${careerCoachingResponse.status}:`,
        errorText
      );
      throw new Error(`Failed to get career coaching: ${errorText}`);
    }

    const careerCoachingResult = await careerCoachingResponse.json();
    const careerCoaching = careerCoachingResult as CareerCoachingResponse;

    // Log the career coaching result for debugging
    console.log('[generate-ultimate-results] Career coaching result:', {
      hasAnswer: !!careerCoaching.answer,
      answerLength: careerCoaching.answer?.length || 0,
      hasRelevantSections:
        !!careerCoaching.relevant_sections &&
        careerCoaching.relevant_sections.length > 0,
      hasFollowUpQuestions:
        !!careerCoaching.follow_up_questions &&
        careerCoaching.follow_up_questions.length > 0
    });

    await sendChunk({
      status: 'career_coaching_completed',
      progress: 95,
      careerCoaching
    });

    // 8. Market Trends
    await sendChunk({ status: 'market_trends_started', progress: 97 });

    // Create a properly formatted request for the Market Trends API
    const marketTrendsRequest = {
      jobDescription: jobDescription,
      resumeId: resumeId,
      userId: userId,
      creditsAmount: 0 // Set to 0 since we're already deducting credits for the Ultimate plan
    };

    console.log('[generate-ultimate-results] Sending market trends request:', {
      hasJobDescription: !!marketTrendsRequest.jobDescription,
      resumeId: marketTrendsRequest.resumeId,
      userId: marketTrendsRequest.userId,
      creditsAmount: marketTrendsRequest.creditsAmount
    });

    const marketTrendsResponse = await fetch(MARKET_TRENDS_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(marketTrendsRequest)
    });

    let marketTrends;
    if (!marketTrendsResponse.ok) {
      const errorText = await marketTrendsResponse.text();
      console.error(
        `[generate-ultimate-results] Market trends API failed with status ${marketTrendsResponse.status}:`,
        errorText
      );

      // Instead of throwing an error, create a fallback market trends object
      console.warn(
        '[generate-ultimate-results] Using fallback market trends data'
      );
      marketTrends = {
        jobGrowth: [
          { quarter: 'Q1 2023', growth: 5.2 },
          { quarter: 'Q2 2023', growth: 6.1 },
          { quarter: 'Q3 2023', growth: 7.3 },
          { quarter: 'Q4 2023', growth: 8.2 }
        ],
        salaryTrends: [
          { month: 'Jan', tech: 75000, finance: 85000, healthcare: 65000 },
          { month: 'Feb', tech: 76000, finance: 86000, healthcare: 66000 }
        ],
        skillDemand: [
          { skill: 'React', count: 1200 },
          { skill: 'TypeScript', count: 1000 },
          { skill: 'Node.js', count: 800 }
        ],
        remoteWorkTrends: [
          {
            type: 'Full Remote',
            percentage: 35,
            averageSalary: 85000,
            industryBreakdown: [
              { industry: 'Tech', percentage: 60 },
              { industry: 'Finance', percentage: 25 }
            ]
          }
        ],
        industryBreakdown: [
          { industry: 'Technology', percentage: 45 },
          { industry: 'Finance', percentage: 25 },
          { industry: 'Healthcare', percentage: 20 },
          { industry: 'Others', percentage: 10 }
        ]
      };
    } else {
      marketTrends = await marketTrendsResponse.json();
    }

    // Log the market trends result for debugging
    console.log('[generate-ultimate-results] Market trends result:', {
      hasJobGrowth:
        !!marketTrends.jobGrowth && marketTrends.jobGrowth.length > 0,
      hasSalaryTrends:
        !!marketTrends.salaryTrends && marketTrends.salaryTrends.length > 0,
      hasSkillDemand:
        !!marketTrends.skillDemand && marketTrends.skillDemand.length > 0
    });

    // Final result compilation
    const ultimateResult: UltimatePlanResult = {
      planType: PlanType.ULTIMATE,
      atsResult,
      coverLetter,
      skillsGap: typedSkillsGaps,
      skillImprovements: [{ skill_resources: skillImprovements }], // Wrap in array to match UltimatePlanResult interface
      mockInterview: mockInterviewResult,
      careerMatches,
      careerCoaching, // Added this
      marketTrends
    };

    // Store results in database
    const { error: saveError } = await supabase.rpc(
      'insert_application_result',
      {
        p_user_id: userId,
        p_resume_id: resumeId,
        p_job_description: jobDescription,
        p_results: ultimateResult,
        p_plan_type: String(PlanType.ULTIMATE).toLowerCase()
      }
    );

    if (saveError) {
      console.error(
        '[generate-ultimate-results] Error saving results:',
        saveError
      );
      // Log detailed error information for debugging
      console.error('[generate-ultimate-results] Detailed save error:', {
        code: saveError.code,
        message: saveError.message,
        details: saveError.details,
        hint: saveError.hint
      });

      // Don't throw the error, just log it and continue
      // This allows the results to be returned to the client even if saving fails
      console.warn(
        '[generate-ultimate-results] Continuing despite database save error'
      );
    }

    // Send final completion
    await sendChunk({
      status: 'completed',
      progress: 100,
      results: ultimateResult
    });
  } catch (error) {
    console.error('[generate-ultimate-results] Error:', error);
    if (!state.writerClosed) {
      await sendChunk({
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 'error',
        progress: 100
      });
      await safeCloseWriter();
    }
  } finally {
    await safeCloseWriter();
  }
}
