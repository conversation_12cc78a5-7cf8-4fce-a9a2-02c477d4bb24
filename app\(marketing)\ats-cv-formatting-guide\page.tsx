import type { Metadata } from 'next';
import JsonLd from '@/components/seo/JsonLd';
import { faqLd, FAQItem } from 'lib/faq';
import AtsCvFormattingContent from './metadata/AtsCvFormattingContent';

export const metadata: Metadata = {
  title: 'ATS CV Formatting Guide: 7 Mistakes That Get UK CVs Rejected in 2025',
  description:
    'Learn the 7 critical formatting mistakes that cause 75% of UK CVs to fail ATS scans. Discover how to format your CV for success with UK employers using Applicant Tracking Systems.',
  keywords: [
    'ATS CV formatting',
    'CV formatting mistakes',
    'UK ATS systems',
    'CV formatting guide',
    'applicant tracking systems UK',
    'CV formatting tips',
    'ATS friendly CV',
    'UK CV formatting',
    'CV formatting errors',
    'ATS CV tips'
  ].join(', '),
  alternates: {
    canonical: '/ats-cv-formatting-guide'
  },
  openGraph: {
    title: 'ATS CV Formatting Guide: 7 Mistakes That Get UK CVs Rejected',
    description:
      'Learn the 7 critical formatting mistakes that cause 75% of UK CVs to fail ATS scans and how to fix them.',
    type: 'article'
  }
};

const faqs: FAQItem[] = [
  {
    question: 'What percentage of UK employers use ATS systems in 2025?',
    answer:
      'According to current research, 70% of enterprise businesses in the UK use automated CV screening software, with 79% of organizations integrating AI capabilities into their recruitment processes. Major employers including NHS, Civil Service, Amazon UK, Tesco, and HSBC all rely on ATS systems.'
  },
  {
    question: 'Why do multi-column CV templates fail ATS screening?',
    answer:
      'Multi-column layouts cause ATS systems to read content left-to-right, line-by-line, which jumbles information from different sections together. This creates nonsensical text that prevents proper keyword matching and section categorization, leading to automatic rejection.'
  },
  {
    question: 'Which file format works best for UK ATS systems?',
    answer:
      '.docx format is generally preferred for maximum ATS compatibility, though clean text-based PDFs also work well with modern systems. Avoid scanned PDFs, password-protected files, or alternative formats like .pages or .odt. Always check job posting requirements for specific format preferences.'
  },
  {
    question: 'How can I test if my CV will pass ATS screening?',
    answer:
      "Use our free ATS CV checker to test your CV's compatibility with UK employer systems. The tool analyzes formatting, keyword optimization, and parsing accuracy to provide specific recommendations for improvement before you submit applications."
  },
  {
    question: 'What are the most common ATS formatting mistakes?',
    answer:
      'The 7 most common mistakes are: complex multi-column layouts, graphics and images, creative section headers, tables and text boxes, fancy fonts and formatting, header/footer contact information, and wrong file formats. These account for most ATS parsing failures.'
  },
  {
    question: 'Do graphics and images really make CVs fail ATS systems?',
    answer:
      'Yes, all visual elements—logos, charts, skill bars, photos—become completely invisible to ATS systems, which can only process text. While graphics cause only 1% of parsing failures by volume, 100% of visual content gets stripped during processing, making this mistake particularly devastating for creative professionals.'
  }
];

export default function AtsCvFormattingGuide() {
  return (
    <>
      {/* Enhanced JSON-LD Schema */}
      <JsonLd
        id="main-schema"
        data={{
          '@context': 'https://schema.org',
          '@type': 'Article',
          headline:
            'ATS CV Formatting Guide: 7 Mistakes That Get UK CVs Rejected in 2025',
          description:
            'Learn the 7 critical formatting mistakes that cause 75% of UK CVs to fail ATS scans and how to fix them.',
          image: '/images/ats-cv-formatting-guide-2025-cover.webp',
          author: {
            '@type': 'Person',
            name: 'Career Development Specialist'
          },
          publisher: {
            '@type': 'Organization',
            name: 'JobSpace AI',
            logo: {
              '@type': 'ImageObject',
              url: 'https://jobspaceai.com/logo.png'
            }
          },
          datePublished: '2025-06-16',
          dateModified: '2025-06-16',
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': 'https://jobspaceai.com/ats-cv-formatting-guide'
          }
        }}
      />

      {/* FAQ JSON-LD */}
      <JsonLd id="faq-schema" data={faqLd('/ats-cv-formatting-guide', faqs)} />

      {/* HowTo JSON-LD */}
      <JsonLd
        id="howto-schema"
        data={{
          '@context': 'https://schema.org',
          '@type': 'HowTo',
          name: 'How to Format Your CV for UK ATS Systems',
          description:
            'Step-by-step guide to avoid the 7 deadly CV formatting mistakes that cause ATS rejection',
          step: [
            {
              '@type': 'HowToStep',
              position: 1,
              name: 'Use single-column layout',
              text: 'Replace multi-column templates with reverse-chronological, single-column format'
            },
            {
              '@type': 'HowToStep',
              position: 2,
              name: 'Remove all graphics and images',
              text: 'Replace visual elements with text-based descriptions'
            },
            {
              '@type': 'HowToStep',
              position: 3,
              name: 'Use standard section headers',
              text: 'Replace creative headers with conventional terms like "Work Experience," "Education," "Skills"'
            },
            {
              '@type': 'HowToStep',
              position: 4,
              name: 'Eliminate tables and text boxes',
              text: 'Convert tabular information to linear text format'
            },
            {
              '@type': 'HowToStep',
              position: 5,
              name: 'Test your CV compatibility',
              text: 'Use an ATS checker to verify your formatting works with UK employer systems'
            }
          ]
        }}
      />

      <AtsCvFormattingContent faqs={faqs} />
    </>
  );
}
