'use client';

import { AlertCircle, CheckCircle, Clock, Mail, Inbox } from 'lucide-react';

interface FormAlertProps {
  status?: string | null;
  statusDescription?: string | null;
  error?: string | null;
  errorDescription?: string | null;
  showEmailSentUI?: boolean;
  sentToEmail?: string;
  onTryAgain?: () => void;
}

export default function FormAlert({
  status,
  statusDescription,
  error,
  errorDescription,
  showEmailSentUI = false,
  sentToEmail,
  onTryAgain
}: FormAlertProps) {
  if (!status && !error && !showEmailSentUI) return null;

  return (
    <div className="mb-4 space-y-2">
      {/* Error Alert */}
      {error && errorDescription && (
        <div className="p-4 bg-red-50/20 backdrop-blur-sm border border-red-200/30 rounded-md flex items-start">
          <AlertCircle className="h-5 w-5 text-red-300 mr-2 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-medium text-red-200">{error}</h3>
            <p className="text-sm text-red-100 mt-1">{errorDescription}</p>
          </div>
        </div>
      )}

      {/* Success/Status Alert */}
      {status && statusDescription && (
        <div className="p-4 bg-green-50/20 backdrop-blur-sm border border-green-200/30 rounded-md flex items-start">
          <CheckCircle className="h-5 w-5 text-green-300 mr-2 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-medium text-green-200">{status}</h3>
            <p className="text-sm text-green-100 mt-1">{statusDescription}</p>
            {statusDescription.includes('10 minutes') && (
              <div className="flex items-center mt-2 text-amber-300">
                <Clock className="h-4 w-4 mr-1" />
                <span className="text-xs font-medium">
                  The reset link will expire in 10 minutes
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Special Email Sent UI */}
      {showEmailSentUI && sentToEmail && (
        <div className="p-4 bg-green-50/20 backdrop-blur-sm border border-green-200/30 rounded-md">
          <div className="flex items-start">
            <Mail className="h-5 w-5 text-green-300 mr-2 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="font-medium text-green-200">Email Sent!</h3>
              <p className="text-sm text-green-100 mt-1">
                We&apos;ve sent a password reset link to{' '}
                <strong>{sentToEmail}</strong>.
              </p>
              <div className="mt-3 flex items-start">
                <Inbox className="h-4 w-4 text-amber-300 mr-1 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-amber-300">
                  Please check your spam or junk folder if you don&apos;t see
                  the email in your inbox within a few minutes.
                </p>
              </div>
              {onTryAgain && (
                <div className="mt-4">
                  <button
                    onClick={onTryAgain}
                    className="text-xs bg-transparent border border-white/30 text-white hover:bg-white/10 px-3 py-1.5 rounded transition-colors"
                  >
                    Try again with a different email
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Info Alert for Blue Messages */}
      {status &&
        statusDescription &&
        !status.toLowerCase().includes('success') &&
        !error && (
          <div className="p-4 bg-blue-50/20 backdrop-blur-sm border border-blue-200/30 rounded-md flex items-start">
            <CheckCircle className="h-5 w-5 text-blue-300 mr-2 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="font-medium text-blue-200">{status}</h3>
              <p className="text-sm text-blue-100 mt-1">{statusDescription}</p>
              {statusDescription.includes('10 minutes') && (
                <div className="flex items-center mt-2 text-amber-300">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-xs font-medium">
                    The reset link will expire in 10 minutes
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
    </div>
  );
}
