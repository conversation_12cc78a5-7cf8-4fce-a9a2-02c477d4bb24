import React, { useEffect } from 'react';
import { Resume, Job } from '@/app/types/globalTypes';
import { EnhancedSpeechService } from '@/app/services/EnhancedSpeechService';
import MockInterviewClient from './MockInterviewClient';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';

interface InterviewDialogProps {
  /** Whether the dialog is open */
  isOpen: boolean;

  /** Callback to close the dialog */
  onClose: () => void;

  /** Resume data */
  resume: Resume;

  /** Job data */
  job: Job;

  /** Skip the setup screen and go directly to the interview */
  skipSetup?: boolean;
}

const InterviewDialog: React.FC<InterviewDialogProps> = ({
  isOpen,
  onClose,
  resume,
  job,
  skipSetup = false
}) => {
  // Handle cleanup when dialog closes
  const handleClose = () => {
    // Stop all speech synthesis
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
    }

    // Stop any ongoing speech recognition
    if (window.stopListeningFn) {
      window.stopListeningFn();
    }

    // Call the original onClose
    onClose();
  };

  // Add effect to warm up voices when dialog opens
  useEffect(() => {
    if (isOpen) {
      const initVoices = async () => {
        console.log('Warming up voices for interview...');
        await EnhancedSpeechService.warmUpVoices();
        console.log('Voice warm-up complete');
      };
      initVoices();
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent
        className="flex flex-col p-0 rounded-lg w-[95vw] sm:w-[90vw] max-w-[1300px] h-[90vh] sm:h-auto sm:max-h-[90vh] bg-black border border-gray-800 shadow-lg backdrop-blur-sm"
        aria-describedby="interview-description"
        showCloseButton={false}
      >
        <div className="p-4 flex flex-row items-center justify-between border-b border-gray-800 bg-gradient-to-r from-blue-900/30 to-black">
          <div className="flex items-center gap-3">
            <div className="w-1.5 h-8 bg-[hsl(var(--hero-yellow))] rounded-full shadow-[0_0_8px_rgba(246,160,60,0.5)]"></div>
            <div>
              <h2 className="text-2xl font-bold text-white bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent drop-shadow-[0_0_2px_rgba(255,255,255,0.3)]">
                Mock Interview
              </h2>
              <p className="text-sm text-slate-300 mt-1">
                Practice interview questions for your target role
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="rounded-full w-8 h-8 flex items-center justify-center text-slate-400 hover:text-white hover:bg-gray-800 transition-colors"
            aria-label="Close dialog"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <DialogTitle className="sr-only">Mock Interview</DialogTitle>
        <div id="interview-description" className="sr-only">
          Interactive mock interview based on your resume and job description
        </div>
        <div className="p-6 flex-1 overflow-y-auto backdrop-blur-sm bg-black/90">
          <MockInterviewClient
            onClose={handleClose}
            resume={resume}
            job={job}
            skipSetup={skipSetup}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InterviewDialog;
