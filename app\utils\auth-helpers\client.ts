'use client';
import { createClient } from '@/app/utils/supabase/client';
import { Provider } from '@supabase/supabase-js';
import { SignInResult } from '@/app/types/auth';

export async function handleRequest(
  e: React.FormEvent<HTMLFormElement>,
  signInFunction: (formData: FormData) => Promise<string>,
  options?: { skipEmailValidation?: boolean; skipPasswordValidation?: boolean }
) {
  e.preventDefault();
  console.log('[handleRequest] Starting request handling');

  const form = e.currentTarget;
  const formData = new FormData(form);

  // Validate form data before submitting
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  // Only validate email if not skipped
  if (!options?.skipEmailValidation) {
    if (!email || !email.trim()) {
      throw new Error('Email is required');
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim())) {
      throw new Error('Please enter a valid email address');
    }
  }

  // Only validate password if not skipped
  if (!options?.skipPasswordValidation) {
    if (!password || !password.trim()) {
      throw new Error('Password is required');
    }

    if (password.trim().length < 6) {
      throw new Error('Password must be at least 6 characters');
    }
  }

  // Get next parameter from URL
  const searchParams = new URLSearchParams(window.location.search);
  const next = searchParams.get('next');

  console.log('[handleRequest] URL params:', {
    next,
    currentUrl: window.location.href,
    searchParams: Object.fromEntries(searchParams)
  });

  if (next) {
    console.log('[handleRequest] Adding next parameter:', next);
    formData.append('next', next);
  }

  try {
    const redirectPath = await signInFunction(formData);
    console.log('[handleRequest] Received redirect path:', redirectPath);

    // Check if the path contains an error
    if (redirectPath.includes('error=')) {
      const errorParams = new URLSearchParams(redirectPath.split('?')[1]);
      const errorMessage =
        errorParams.get('error_description') || 'Authentication failed';
      const errorTitle = errorParams.get('error') || 'Error';

      console.error(
        `[handleRequest] Auth error: ${errorTitle} - ${errorMessage}`
      );
      throw new Error(errorMessage);
    }

    // Navigate immediately to avoid getting stuck
    console.log('[handleRequest] Redirecting to:', redirectPath);
    window.location.href = redirectPath;

    // Return the path in case the component wants to do something with it
    return redirectPath;
  } catch (error) {
    console.error('[handleRequest] Error:', error);
    // Re-throw the error so the component can handle it
    throw error;
  }
}

export const signInWithOAuth = async (
  formData: FormData
): Promise<SignInResult> => {
  console.log('[signInWithOAuth] Starting OAuth sign-in');
  console.log('[signInWithOAuth] Form data:', formData);
  const provider = String(formData.get('provider'));
  let nextUrl = String(formData.get('next') || '/dashboard');

  // If nextUrl indicates onboarding, normalize to funnel path
  if (
    nextUrl.startsWith('/onboarding') ||
    nextUrl.startsWith('/application-funnel')
  ) {
    nextUrl = '/application-funnel?step=1';
  }

  // Get the current URL info
  const protocol = window.location.protocol;
  const host = window.location.host;
  const baseUrl = `${protocol}//${host}`;

  console.log('[signInWithOAuth] Configuration:', {
    provider,
    baseUrl,
    nextUrl,
    currentUrl: window.location.href
  });

  try {
    const supabase = createClient();

    const callbackUrl = `${baseUrl}/auth/callback${nextUrl ? `?next=${encodeURIComponent(nextUrl)}` : ''}`;
    console.log('[signInWithOAuth] Callback URL:', callbackUrl);

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: provider as Provider,
      options: {
        redirectTo: callbackUrl,
        queryParams: nextUrl ? { next: nextUrl } : undefined
      }
    });

    console.log('[signInWithOAuth] Supabase response:', { data, error });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('[signInWithOAuth] Error:', error);
    return {
      data: null,
      error: {
        message:
          error instanceof Error ? error.message : 'An unknown error occurred',
        status: 500
      }
    };
  }
};
