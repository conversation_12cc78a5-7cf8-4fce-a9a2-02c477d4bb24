import React from 'react';
import { Article } from '@/app/types/globalTypes';
import JsonLd from '@/components/seo/JsonLd';

interface ArticleSchemaMarkupProps {
  article: Article;
}

const ArticleSchemaMarkup: React.FC<ArticleSchemaMarkupProps> = ({
  article
}) => {
  const articleSchema = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.metaDescription,
    image: article.previewImage || article.schema.image,
    datePublished: article.date,
    dateModified: article.schema.dateModified || article.date,
    author: {
      '@type': 'Person',
      name: article.author
    },
    publisher: {
      '@type': 'Organization',
      name: 'Job Space AI',
      logo: {
        '@type': 'ImageObject',
        url: 'https://jobspaceai.com/logo.png'
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://jobspaceai.com/articles/${article.slug}`
    },
    keywords: article.tags.join(', ')
  };

  return <JsonLd id="article-schema" data={articleSchema} />;
};

export default ArticleSchemaMarkup;
