'use client';

import React, { useCallback, useState, useEffect } from 'react';

import { Button } from 'components/ui/button';
import { useToast } from 'hooks/use-toast';
import UploadResumeClient from 'app/upload-resume/UploadResumeClient';
import UploadJobForm from 'components/jobs/UploadJobForm';
import { useSelectionStore } from 'stores/useSelectionStore';
import type { Resume, Job } from 'app/types/globalTypes';
import { ATSAnalysisDisplay } from 'components/features/ats/ATSAnalysisDisplay';

interface ScanFormProps {
  /** Current user id *if* signed in; otherwise undefined. */
  userId?: string | null;
}

import type { ComprehensiveAnalysisResult } from 'app/types/globalTypes';
import {
  trackATSCheckCompletion,
  trackMarketingPageView,
  trackFileUploadStart,
  trackFileUploadSuccess,
  trackFileUploadFailure,
  trackJobAnalysisStart,
  trackJobAnalysisComplete,
  trackJobAnalysisFailure,
  trackButtonClick,
  trackUserInteraction,
  trackMarketingConversion,
  trackFeatureUsage,
  trackAnalysisStart,
  trackAnalysisComplete,
  trackAnalysisFailure
} from '@/lib/ga-events';
import ATSLoadingProgress from './ATSLoadingProgress';

export default function ScanForm({ userId }: ScanFormProps) {
  const { toast } = useToast();

  const { selectedResume, selectedJob, setSelectedResume, setSelectedJob } =
    useSelectionStore();

  const [atsLoading, setAtsLoading] = useState(false);
  const [atsResult, setAtsResult] =
    useState<ComprehensiveAnalysisResult | null>(null);
  const [atsError, setAtsError] = useState<string | null>(null);
  const [loadingStep, setLoadingStep] = useState(1);

  // Tracking states
  const [formStartTime, setFormStartTime] = useState<number | null>(null);
  const [analysisStartTime, setAnalysisStartTime] = useState<number | null>(
    null
  );
  const [hasTrackedFormStart, setHasTrackedFormStart] = useState(false);
  const [resumeUploadTime, setResumeUploadTime] = useState<number | null>(null);
  const [jobUploadTime, setJobUploadTime] = useState<number | null>(null);

  // Track page view on component mount
  useEffect(() => {
    const startTime = Date.now();
    setFormStartTime(startTime);

    // Track marketing page view
    trackMarketingPageView('ATS Resume Checker', 'marketing');

    // Track user interaction with form
    const handleUserInteraction = () => {
      if (!hasTrackedFormStart) {
        trackUserInteraction({
          interaction_type: 'form_engagement',
          element_type: 'marketing_scan_form',
          user_journey: 'marketing'
        });
        setHasTrackedFormStart(true);
      }
    };

    // Add event listeners for user interaction
    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('scroll', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);

    return () => {
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('scroll', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);

      // Track abandonment if user leaves without completing analysis
      if (!atsResult && formStartTime) {
        const timeSpent = Date.now() - formStartTime;
        trackUserInteraction({
          interaction_type: 'form_abandon',
          element_type: 'marketing_scan_form',
          time_spent_ms: timeSpent,
          user_journey: 'marketing'
        });
      }
    };
  }, [userId, hasTrackedFormStart, atsResult, formStartTime]);

  // Helper to convert analysed job object to descriptive string for ATS API
  function jobToDescriptionString(
    job: ComprehensiveAnalysisResult | Record<string, unknown>
  ): string {
    if (!job) return '';
    const parts: string[] = [];
    if ('job_title' in job && typeof job.job_title === 'string')
      parts.push(job.job_title);
    if ('company_name' in job && typeof job.company_name === 'string')
      parts.push(job.company_name);
    if ('sections' in job && Array.isArray(job.sections)) {
      job.sections.forEach((section: Record<string, unknown>) => {
        if (typeof section.title === 'string') parts.push(section.title);
        if (Array.isArray(section.content)) {
          parts.push(
            ...section.content.filter((c): c is string => typeof c === 'string')
          );
        }
      });
    }
    return parts.join('\n');
  }

  /* --------------------------------------------------------------------- */
  /* Upload callbacks                                                       */
  /* --------------------------------------------------------------------- */

  const handleResumeUploaded = useCallback(
    (resume: Resume) => {
      console.log('handleResumeUploaded called with resume:', resume);

      // Track resume upload success in marketing funnel
      const uploadTime = resumeUploadTime ? Date.now() - resumeUploadTime : 0;
      trackFileUploadSuccess({
        file_type: 'cv_resume',
        upload_method: (resume as { isLocal?: boolean }).isLocal
          ? 'local_file'
          : 'existing_file',
        file_source: 'marketing_form',
        upload_duration_ms: uploadTime,
        file_size_chars: resume.resume?.length || 0, // Use resume content length instead
        user_journey: 'marketing',
        user_type: userId ? 'authenticated' : 'anonymous'
      });

      // Track marketing conversion milestone
      trackMarketingConversion({
        conversion_type: 'resume_uploaded',
        conversion_stage: 'step_1_complete',
        user_type: userId ? 'authenticated' : 'anonymous',
        time_to_conversion_ms: formStartTime ? Date.now() - formStartTime : 0
      });

      // Always set the resume in Zustand store, including local resumes
      setSelectedResume(resume);
    },
    [setSelectedResume, resumeUploadTime, formStartTime, userId]
  );

  const handleJobUploaded = useCallback(
    (job: Job) => {
      console.log('handleJobUploaded called with job:', job);

      // Track job upload success in marketing funnel
      const uploadTime = jobUploadTime ? Date.now() - jobUploadTime : 0;
      trackFileUploadSuccess({
        file_type: 'job_description',
        upload_method: 'manual_entry',
        file_source: 'marketing_form',
        upload_duration_ms: uploadTime,
        file_size_chars: job.description?.length || 0,
        user_journey: 'marketing',
        user_type: userId ? 'authenticated' : 'anonymous'
      });

      // Track marketing conversion milestone
      trackMarketingConversion({
        conversion_type: 'job_uploaded',
        conversion_stage: 'step_2_complete',
        user_type: userId ? 'authenticated' : 'anonymous',
        time_to_conversion_ms: formStartTime ? Date.now() - formStartTime : 0
      });

      if (!job.title || job.title.trim() === '') {
        job.title = 'Uploaded Job';
      }
      setSelectedJob(job);
    },
    [setSelectedJob, jobUploadTime, formStartTime, userId]
  );

  /* --------------------------------------------------------------------- */
  /* Scan trigger with enhanced loading feedback                            */
  /* --------------------------------------------------------------------- */

  const handleScan = async () => {
    if (!selectedResume || !selectedJob) {
      toast({
        title: 'Upload missing data',
        description: 'Please upload both a résumé and a job before scanning.'
      });

      // Track validation failure
      trackUserInteraction({
        interaction_type: 'validation_error',
        element_type: 'scan_button',
        error_message: 'Missing resume or job data',
        user_journey: 'marketing'
      });
      return;
    }

    // Track scan initiation
    trackButtonClick({
      button_name: 'scan_cv_now',
      button_location: 'marketing_scan_form',
      user_journey: 'marketing',
      user_type: userId ? 'authenticated' : 'anonymous'
    });

    const scanStartTime = Date.now();
    setAnalysisStartTime(scanStartTime);
    setAtsLoading(true);
    setAtsError(null);
    setAtsResult(null);
    setLoadingStep(1);

    // Track overall analysis start
    trackAnalysisStart({
      analysis_type: 'ats_scan',
      feature_used: 'marketing_ats_checker',
      user_journey: 'marketing',
      user_type: userId ? 'authenticated' : 'anonymous',
      resume_file_size: selectedResume.resume?.length || 0, // Use resume content length instead
      job_description_length: selectedJob.description?.length || 0
    });

    try {
      console.log('handleScan selectedResume.id:', selectedResume.id);

      // Step 1: Analyze resume
      toast({
        title: 'Starting analysis',
        description: 'Processing your CV...'
      });

      const formData = new FormData();

      if ((selectedResume as { isLocal?: boolean }).isLocal) {
        const base64Data = (selectedResume.resume as string)?.split(',')[1];
        if (base64Data) {
          // Append the file data as a Blob to formData for proper file upload
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: 'application/pdf' });
          console.log('Created Blob:', {
            size: blob.size,
            type: blob.type,
            name: selectedResume.file_name || 'resume.pdf'
          });
          formData.append(
            'file',
            blob,
            selectedResume.file_name || 'resume.pdf'
          );

          formData.append('hasFile', 'true');
          formData.append('fileName', selectedResume.file_name || 'resume.pdf');
          formData.append('fileSize', blob.size.toString());
          // local files are not in the DB yet – send empty string as resumeId to satisfy server requirement
          formData.append('resumeId', '');

          // Log Blob details
          console.log('Blob details:', {
            size: blob.size,
            type: blob.type,
            name: selectedResume.file_name || 'resume.pdf'
          });
        } else {
          formData.append('hasFile', 'false');
          formData.append('fileName', '');
          formData.append('fileSize', '0');
          // local files are not in the DB yet – send empty string as resumeId to satisfy server requirement
          formData.append('resumeId', '');
        }
        formData.append('operation', 'upload');
      } else if (selectedResume.id && selectedResume.id !== 'local') {
        formData.append('resumeId', selectedResume.id);
        formData.append('hasFile', 'false');
        formData.append('fileName', '');
        formData.append('fileSize', '0');
      } else {
        throw new Error('Missing resumeId for non-local resume');
      }

      // Log all formData entries for debugging
      for (const pair of formData.entries()) {
        console.log(`${pair[0]}:`, pair[1]);
      }

      const analyzeResponse = await fetch('/api/analyze-resume', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!analyzeResponse.ok) {
        const errorText = await analyzeResponse.text();
        throw new Error(`Resume analysis failed: ${errorText}`);
      }

      const analysisResult = await analyzeResponse.json();

      if (analysisResult.error) {
        throw new Error(analysisResult.error);
      }

      // Step 2: Analyze job description
      setLoadingStep(2);
      await new Promise((resolve) => setTimeout(resolve, 500)); // Brief pause for UX

      // Track job analysis start
      trackJobAnalysisStart({
        analysis_type: 'job_description',
        file_source: 'marketing_form',
        input_method: 'manual_entry',
        character_count: selectedJob.description?.length || 0,
        user_journey: 'marketing'
      });

      const analyseJobResponse = await fetch('/api/analyse-job', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobDescription: selectedJob.description,
          jobLink: selectedJob.job_link || ''
        })
      });

      if (!analyseJobResponse.ok) {
        const errorText = await analyseJobResponse.text();
        throw new Error(`Job analysis failed: ${errorText}`);
      }

      const analysedJob = await analyseJobResponse.json();

      // Track job analysis success
      trackJobAnalysisComplete({
        analysis_type: 'job_description',
        analysis_time_ms: 500, // Approximate time for this step
        file_source: 'marketing_form',
        input_method: 'manual_entry',
        character_count: selectedJob.description?.length || 0,
        user_journey: 'marketing'
      });

      // Step 3: Run ATS compatibility check
      setLoadingStep(3);
      await new Promise((resolve) => setTimeout(resolve, 500)); // Brief pause for UX

      // Convert analysed job object to descriptive string for ATS API
      const jobDescriptionString = jobToDescriptionString(analysedJob);

      // Then pass the analysis result along with the analysed job's description string to the ATS analysis API
      const atsResponse = await fetch('/api/ats-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          resume: analysisResult.result || analysisResult,
          jobDescription: jobDescriptionString
        })
      });

      if (!atsResponse.ok) {
        const errorText = await atsResponse.text();
        throw new Error(`ATS analysis failed: ${errorText}`);
      }

      const atsResult = await atsResponse.json();

      if (atsResult.error) {
        throw new Error(atsResult.error);
      }

      // Step 4: Generate recommendations (final step)
      setLoadingStep(4);
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Pause to show final step

      setAtsResult(atsResult);

      // Track analysis completion with comprehensive metrics
      const totalAnalysisTime = analysisStartTime
        ? Date.now() - analysisStartTime
        : 0;
      const totalFormTime = formStartTime ? Date.now() - formStartTime : 0;

      // Track ATS completion (existing function)
      trackATSCheckCompletion();

      // Track comprehensive analysis completion
      trackAnalysisComplete({
        analysis_type: 'ats_scan',
        analysis_time_ms: totalAnalysisTime,
        feature_used: 'marketing_ats_checker',
        user_journey: 'marketing',
        user_type: userId ? 'authenticated' : 'anonymous'
      });

      // Track major marketing conversion
      trackMarketingConversion({
        conversion_type: 'ats_analysis_complete',
        conversion_stage: 'final_conversion',
        user_type: userId ? 'authenticated' : 'anonymous',
        time_to_conversion_ms: totalFormTime
      });

      // Track feature usage completion
      trackFeatureUsage({
        feature_name: 'ats_resume_checker',
        feature_category: 'analysis',
        usage_duration_ms: totalAnalysisTime,
        user_journey: 'marketing',
        completion_status: 'completed'
      });

      // Success toast with scroll instruction
      toast({
        title: 'Scan complete! 🎉',
        description:
          'Your ATS analysis is ready. Scroll down to view your results.',
        duration: 5000
      });

      // Auto-scroll to results after a brief delay
      setTimeout(() => {
        const resultsElement = document.querySelector('[data-ats-results]');
        if (resultsElement) {
          resultsElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 1000);
    } catch (err) {
      const errorMessage = (err as Error).message;
      const totalAnalysisTime = analysisStartTime
        ? Date.now() - analysisStartTime
        : 0;

      setAtsError(errorMessage);

      // Track analysis failure with detailed context
      trackAnalysisFailure({
        analysis_type: 'ats_scan',
        error_type: 'processing_error',
        error_message: errorMessage,
        analysis_time_ms: totalAnalysisTime,
        feature_used: 'marketing_ats_checker',
        user_journey: 'marketing',
        user_type: userId ? 'authenticated' : 'anonymous',
        step_failed: loadingStep
      });

      // Track feature usage failure
      trackFeatureUsage({
        feature_name: 'ats_resume_checker',
        feature_category: 'analysis',
        usage_duration_ms: totalAnalysisTime,
        user_journey: 'marketing',
        completion_status: 'failed',
        error_message: errorMessage
      });

      toast({
        variant: 'destructive',
        title: 'Scan failed',
        description: errorMessage,
        duration: 6000
      });
    } finally {
      setAtsLoading(false);
      setLoadingStep(1);
    }
  };

  // Track upload initiation for resume
  const handleResumeUploadStart = () => {
    setResumeUploadTime(Date.now());
    trackFileUploadStart({
      file_type: 'cv_resume',
      upload_method: 'local_file',
      file_source: 'marketing_form',
      user_journey: 'marketing',
      user_type: userId ? 'authenticated' : 'anonymous'
    });
  };

  // Track upload initiation for job
  const handleJobUploadStart = () => {
    setJobUploadTime(Date.now());
    trackFileUploadStart({
      file_type: 'job_description',
      upload_method: 'manual_entry',
      file_source: 'marketing_form',
      user_journey: 'marketing',
      user_type: userId ? 'authenticated' : 'anonymous'
    });
  };

  /* --------------------------------------------------------------------- */
  /* Render                                                                 */
  /* --------------------------------------------------------------------- */

  return (
    <>
      {/* Add the loading progress overlay */}
      <ATSLoadingProgress isLoading={atsLoading} currentStep={loadingStep} />

      <div className="w-full max-w-6xl space-y-10">
        {/* Résumé uploader -------------------------------------------------- */}
        <section>
          <h2 className="mb-2 font-semibold">Upload CV</h2>
          <div
            onFocus={handleResumeUploadStart}
            onClick={handleResumeUploadStart}
          >
            <UploadResumeClient
              /* New optional API: either pass userId OR allowAnonymous */
              userId={userId ?? undefined}
              allowAnonymous={!userId}
              onComplete={handleResumeUploaded}
            />
          </div>
        </section>

        {/* Job uploader ----------------------------------------------------- */}
        <section>
          <h2 className="mb-2 font-semibold">Upload job description</h2>
          <div onFocus={handleJobUploadStart} onClick={handleJobUploadStart}>
            <UploadJobForm
              onJobDataChange={(desc, link) => {
                setSelectedJob({
                  id: 'local',
                  user_id: '',
                  title: null,
                  company: null,
                  description: desc,
                  job_link: link,
                  salary_range: null,
                  location: null,
                  analysis_result: {
                    job_title: '',
                    company_name: '',
                    location: '',
                    job_type: '',
                    salary_range: '',
                    sections: [],
                    job_link: ''
                  },
                  status: 'saved',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                });
              }}
              onSubmit={async (description: string, link: string) => {
                try {
                  const response = await fetch('/api/analyse-job', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      jobDescription: description,
                      jobLink: link
                    })
                  });
                  if (!response.ok) {
                    throw new Error('Failed to analyze job');
                  }
                  const analyzedJob = await response.json();
                  // Construct job object with analyzed data
                  const job = {
                    id: 'local',
                    user_id: '',
                    title: analyzedJob.job_title || 'Uploaded Job',
                    company: analyzedJob.company_name || '',
                    description,
                    job_link: link,
                    salary_range: analyzedJob.salary_range || null,
                    location: analyzedJob.location || null,
                    analysis_result: analyzedJob,
                    status: 'saved',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  };
                  setSelectedJob(job);
                  return job;
                } catch (error) {
                  // Track job analysis failure
                  trackJobAnalysisFailure({
                    analysis_type: 'job_description',
                    error_type: 'analysis_processing_error',
                    error_message: (error as Error).message,
                    analysis_time_ms: 0,
                    file_source: 'marketing_form',
                    input_method: 'manual_entry',
                    character_count: description.length,
                    user_journey: 'marketing'
                  });

                  // Fallback dummy job on error
                  console.error('Failed to analyze job:', error);
                  return {
                    id: 'dummy',
                    user_id: '',
                    title: '',
                    company: '',
                    description,
                    job_link: link,
                    salary_range: null,
                    location: null,
                    analysis_result: {
                      job_title: '',
                      company_name: '',
                      location: '',
                      job_type: '',
                      salary_range: '',
                      description: '',
                      requirements: '',
                      responsibilities: '',
                      benefits: '',
                      sections: [],
                      job_link: ''
                    },
                    status: 'saved',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  };
                }
              }}
              initialJob={selectedJob}
              submitButtonText="Upload & Analyse Job"
              isLoading={false}
              onComplete={handleJobUploaded}
            />
          </div>
        </section>

        {/* Call to action --------------------------------------------------- */}
        <div className="flex justify-center">
          <Button
            onClick={handleScan}
            disabled={
              atsLoading ||
              !selectedResume ||
              !selectedJob ||
              !selectedResume.id ||
              !selectedJob.id ||
              !selectedResume.resume // Added check for resume content presence
            }
            className="px-8 py-4 md:py-5 bg-hero-yellow text-[#111827] font-semibold rounded-full shadow-lg flex items-center justify-center gap-3 hover:bg-hero-yellow-light active:scale-95 text-[16px] md:text-[18px] w-auto"
          >
            {atsLoading && (
              <svg
                className="animate-spin h-5 w-5 text-yellow-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                ></path>
              </svg>
            )}
            {atsLoading ? 'Scanning...' : 'Scan my CV now'}
          </Button>
        </div>

        <p className="mt-2 text-sm text-slate-200 text-center">
          *Fully supports UK date and number formats. CV or résumé accepted.
        </p>

        <p className="mt-2 text-sm text-slate-200 text-center">
          3 free scans/day · Sign in to raise limit and permanently save your
          uploads
        </p>

        {/* ATS Analysis Result Display */}
        {atsResult && !atsLoading && (
          <div data-ats-results className="scroll-mt-8">
            <ATSAnalysisDisplay result={atsResult} error={atsError} />
          </div>
        )}
      </div>
    </>
  );
}
