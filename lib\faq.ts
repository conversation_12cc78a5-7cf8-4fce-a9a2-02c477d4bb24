import { siteConfig } from './seo-config';

export interface FAQItem {
  question: string;
  answer: string;
}

/** returns a minified JSON-LD string */
export function faqLd(url: string, faqs: FAQItem[]) {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${siteConfig.url}${url}`
    },
    inLanguage: 'en-GB',
    mainEntity: faqs.map(({ question, answer }) => ({
      '@type': 'Question',
      name: question,
      acceptedAnswer: { '@type': 'Answer', text: answer }
    }))
  });
}
