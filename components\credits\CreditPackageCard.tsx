import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditPackage } from '@/app/types/globalTypes';
import { getStripe } from '@/lib/stripe-client';
import { Loader2, Package, Shield, Zap, Star, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter, usePathname } from 'next/navigation';
import { trackPlanSelect, trackUserInteraction } from '@/lib/ga-events';

interface CreditPackageCardProps {
  package: CreditPackage;
  userId?: string;
  onPurchaseComplete?: (packageId: string) => Promise<void>;
  isAuthenticated: boolean;
}

export const CreditPackageCard: React.FC<CreditPackageCardProps> = ({
  package: creditPackage,
  userId,
  onPurchaseComplete,
  isAuthenticated
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);

  const handlePurchase = async () => {
    if (!isAuthenticated) {
      // Track unauthenticated purchase attempt
      trackUserInteraction({
        interaction_type: 'purchase_attempt_unauthenticated',
        element_type: 'credit_package_card',
        user_journey: 'pricing',
        package_id: creditPackage.id,
        package_name: creditPackage.name
      });

      router.push(`/signin?next=${encodeURIComponent(pathname)}`);
      return;
    }

    // Track plan selection
    trackPlanSelect(creditPackage.id, creditPackage.name, creditPackage.price);

    try {
      setIsLoading(true);
      setError(null);

      // Track payment initiation
      trackUserInteraction({
        interaction_type: 'payment_initiated',
        element_type: 'credit_package_card',
        user_journey: 'pricing',
        package_id: creditPackage.id,
        package_name: creditPackage.name,
        package_value: creditPackage.price,
        package_credits: creditPackage.credits
      });

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_URL}/api/credits/purchase`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            packageId: creditPackage.id,
            userId,
            returnUrl: window.location.href // Add return URL
          })
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      const stripe = await getStripe();
      if (!stripe) {
        throw new Error('Stripe failed to load');
      }

      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId: data.sessionId
      });

      if (stripeError) {
        throw stripeError;
      }

      // Safely call onPurchaseComplete if it exists
      if (onPurchaseComplete) {
        await onPurchaseComplete(creditPackage.id);
      }
    } catch (err) {
      console.error('Purchase error:', err);

      // Track payment failure
      trackUserInteraction({
        interaction_type: 'payment_failed',
        element_type: 'credit_package_card',
        user_journey: 'pricing',
        error_message: err instanceof Error ? err.message : 'Unknown error',
        package_id: creditPackage.id,
        package_name: creditPackage.name
      });

      setError(
        err instanceof Error ? err.message : 'Failed to process purchase'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const isBestValue =
    creditPackage.savePercent && creditPackage.savePercent >= 20;

  return (
    <Card
      className={cn(
        'w-full h-[380px] relative overflow-visible transition-all duration-300 mt-6',
        isHovered ? 'translate-y-[-4px] shadow-xl' : 'shadow-lg',
        'backdrop-blur-sm bg-white/10 border border-white/20 text-white',
        isBestValue
          ? 'border-yellow-400/30 bg-gradient-to-b from-yellow-400/5 to-transparent'
          : ''
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {isBestValue && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 whitespace-nowrap z-10">
          <div className="relative">
            <div className="absolute inset-0 bg-yellow-400/20 blur-sm rounded-full" />
            <span className="relative px-3 py-1 text-sm font-medium text-yellow-400 bg-hero-bg/90 rounded-full border border-yellow-400/30 flex items-center gap-1.5">
              <Star className="h-3.5 w-3.5" />
              Most Popular
            </span>
          </div>
        </div>
      )}

      <CardContent className="p-6 h-full flex flex-col">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-xl font-roboto-condensed font-bold text-yellow-400">
              {creditPackage.name}
            </h3>
            <p className="text-sm text-slate-200 mt-1">
              {creditPackage.credits} Credits
            </p>
          </div>
          <div className="h-8 w-8 rounded-full bg-hero-yellow/10 flex items-center justify-center">
            <Package className="h-4 w-4 text-yellow-400" />
          </div>
        </div>

        <div className="flex-grow flex flex-col justify-center space-y-8">
          <div className="mb-6">
            <div className="flex items-baseline gap-1 mb-1">
              <span className="text-3xl font-bold text-white">
                £{creditPackage.price}
              </span>
              <span className="text-sm text-slate-300">one-time</span>
            </div>
            {creditPackage.savePercent && (
              <p className="text-sm font-medium text-yellow-400 flex items-center gap-1">
                <Star className="h-3.5 w-3.5" />
                Save {creditPackage.savePercent}%
              </p>
            )}
          </div>

          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-slate-300">
                <Shield className="h-4 w-4 text-slate-300" />
                <span className="text-sm">Secure Payment</span>
              </div>
              <div className="flex items-center gap-2 text-slate-300">
                <Zap className="h-4 w-4 text-slate-300" />
                <span className="text-sm">Instant Credit Delivery</span>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="p-3 rounded-lg bg-destructive/10 text-destructive text-sm mb-4">
            {error}
          </div>
        )}

        <Button
          onClick={handlePurchase}
          disabled={isLoading}
          className={cn(
            'w-full rounded-full font-semibold',
            isBestValue
              ? 'bg-hero-yellow text-[#111827] hover:bg-hero-yellow-light'
              : 'backdrop-blur-sm bg-white/10 border border-white/20 text-white hover:bg-white/20'
          )}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Processing...</span>
            </div>
          ) : (
            <>
              {isAuthenticated ? 'Purchase Credits' : 'Sign in to Purchase'}
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export default CreditPackageCard;
