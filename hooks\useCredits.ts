// hooks/useCredits.ts
import { createClient } from 'app/utils/supabase/client';
import { create } from 'zustand';
import { RealtimeChannel } from '@supabase/supabase-js';

const supabase = createClient();
let creditsChannel: RealtimeChannel | null = null;
let currentUserId: string | null = null;
let visibilityListenerAdded = false;

const handleVisibilityChange = () => {
  if (document.visibilityState === 'hidden') {
    supabase.removeAllChannels();
  }
};

interface CreditsState {
  credits: number;
  isLoading: boolean;
  error: string | null;
  fetchCredits: () => Promise<void>;
  updateCredits: (newAmount: number) => Promise<void>;
  cleanup: () => Promise<void>;
}

interface CreditsRecord {
  amount: number;
  user_id: string;
}

export const useCredits = create<CreditsState>((set) => ({
  credits: 0,
  isLoading: false,
  error: null,
  fetchCredits: async () => {
    set({ isLoading: true, error: null });
    try {
      const {
        data: { user }
      } = await supabase.auth.getUser();

      if (!user) throw new Error('No authenticated user');

      // Clean up if user changed
      if (creditsChannel && currentUserId !== user.id) {
        await supabase.removeChannel(creditsChannel);
        creditsChannel = null;
        currentUserId = null;
      }

      // Create subscription if none exists
      if (!creditsChannel) {
        currentUserId = user.id;
        creditsChannel = supabase.channel(`credits-${user.id}`);
        creditsChannel
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'credits',
              filter: `user_id=eq.${user.id}`
            },
            (payload) => {
              try {
                if (payload.new) {
                  set({ credits: (payload.new as CreditsRecord).amount });
                }
              } catch (err) {
                console.error('Error processing realtime update:', err);
              }
            }
          )
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              console.log('Credits subscription active');
            }
          });
      }

      if (!visibilityListenerAdded) {
        document.addEventListener('visibilitychange', handleVisibilityChange);
        visibilityListenerAdded = true;
      }

      // Fetch initial data
      const { data, error } = await supabase
        .from('credits')
        .select('amount')
        .eq('user_id', user.id)
        .single();

      if (error) throw error;

      set({ credits: data.amount || 0, isLoading: false });
    } catch (error) {
      const msg =
        error && typeof error === 'object' && 'message' in error
          ? (error as { message: string }).message
          : error;
      console.error('Error fetching credits:', msg);
      set({
        error: msg ? String(msg) : 'Failed to fetch credits',
        isLoading: false
      });
    }
  },
  updateCredits: async (newAmount: number) => {
    set({ isLoading: true, error: null });
    try {
      const {
        data: { user }
      } = await supabase.auth.getUser();

      if (!user) throw new Error('No authenticated user');

      const { error } = await supabase
        .from('credits')
        .update({ amount: newAmount })
        .eq('user_id', user.id);

      if (error) throw error;

      set({ credits: newAmount, isLoading: false });
    } catch (error) {
      console.error('Error updating credits:', error);
      set({
        error:
          error instanceof Error ? error.message : 'Failed to update credits',
        isLoading: false
      });
    }
  },

  cleanup: async () => {
    if (creditsChannel) {
      await supabase.removeChannel(creditsChannel);
      creditsChannel = null;
      currentUserId = null;
    }
    if (visibilityListenerAdded) {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      visibilityListenerAdded = false;
    }
  }
}));
