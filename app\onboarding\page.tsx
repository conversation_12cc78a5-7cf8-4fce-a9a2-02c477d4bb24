'use client';

import React, { useEffect } from 'react';
import { User, Gift, CreditCard, Shield } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import CTAButton from '@/components/onboarding/CTAButton';
import { trackPageView, trackUserInteraction } from '@/lib/ga-events';
import { useTimeOnPage } from '@/hooks/useTimeOnPage';

const OnboardingPage = () => {
  // Track time on page
  useTimeOnPage({ pageName: 'onboarding_page' });

  // Track landing page view
  useEffect(() => {
    trackPageView('Onboarding Landing Page', 'onboarding');

    // Track landing page view with additional context
    trackUserInteraction({
      interaction_type: 'page_view',
      element_type: 'landing_page',
      user_journey: 'onboarding',
      page_name: 'onboarding_landing'
    });
  }, []);
  return (
    <div className="relative min-h-screen py-12 px-4 overflow-hidden">
      {/* Background with animated blobs */}
      <div className="absolute inset-0 -z-10 pointer-events-none">
        {/* Subtle background features */}
        <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-50"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000 opacity-50"></div>
        <div className="absolute top-2/3 left-1/2 w-[550px] h-[550px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000 opacity-50"></div>
        <div className="absolute top-1/3 right-1/4 w-[450px] h-[450px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000 opacity-50"></div>
      </div>

      <div className="max-w-4xl mx-auto relative z-10">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] text-white text-[clamp(2.5rem,8vw,4.5rem)] mb-4">
            Your{' '}
            <span className="text-yellow-400 text-shadow-yellow">
              AI Career Coach
            </span>
          </h1>
          <p className="text-slate-200 text-lg mb-8">
            Get personalized AI guidance to land your dream job
          </p>
        </div>

        {/* Quick Start Section */}
        <Card className="mb-8 backdrop-blur-sm bg-white/10 border-white/20 shadow-lg">
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4 text-white">
              Start Your Journey:
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-start gap-3">
                <User className="w-5 h-5 text-[#F6A03C] mt-1" />
                <div>
                  <h3 className="font-medium text-white">Free Credits</h3>
                  <p className="text-sm text-slate-300">
                    10 credits to start improving your applications
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Gift className="w-5 h-5 text-[#F6A03C] mt-1" />
                <div>
                  <h3 className="font-medium text-white">Immediate Access</h3>
                  <p className="text-sm text-slate-300">
                    Get AI feedback in minutes, not days
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Primary CTA - Above the Fold */}
        <div className="text-center mb-12">
          <CTAButton />
          <p className="text-slate-400 text-sm mt-3">
            No credit card required • Start with 10 free credits
          </p>
        </div>

        {/* Quick Benefits Summary */}
        <div className="text-center mb-12">
          <div className="flex flex-wrap justify-center gap-6 text-sm text-slate-300">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-[#F6A03C] rounded-full"></div>
              <span>CV optimization</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-[#F6A03C] rounded-full"></div>
              <span>Interview practice</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-[#F6A03C] rounded-full"></div>
              <span>Skills analysis</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-[#F6A03C] rounded-full"></div>
              <span>Bank-level security</span>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="relative mb-12">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-white/20"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="bg-slate-900 px-6 text-slate-400">Learn More</span>
          </div>
        </div>

        {/* Process Overview */}
        <Card className="mb-12 backdrop-blur-sm bg-white/10 border-white/20 shadow-lg">
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-6 text-white">
              How It Works:
            </h2>
            <div className="grid gap-6">
              <div className="flex gap-4 items-start">
                <div className="w-8 h-8 rounded-full bg-[#F6A03C]/20 flex items-center justify-center text-[#F6A03C] font-semibold">
                  1
                </div>
                <div>
                  <h3 className="font-medium mb-1 text-white">
                    Quick Account Setup
                  </h3>
                  <p className="text-slate-300">
                    Create your account in seconds to begin
                  </p>
                </div>
              </div>
              <div className="flex gap-4 items-start">
                <div className="w-8 h-8 rounded-full bg-[#F6A03C]/20 flex items-center justify-center text-[#F6A03C] font-semibold">
                  2
                </div>
                <div>
                  <h3 className="font-medium mb-1 text-white">
                    Add Job Details
                  </h3>
                  <p className="text-slate-300">
                    Share the role you want to apply for
                  </p>
                </div>
              </div>
              <div className="flex gap-4 items-start">
                <div className="w-8 h-8 rounded-full bg-[#F6A03C]/20 flex items-center justify-center text-[#F6A03C] font-semibold">
                  3
                </div>
                <div>
                  <h3 className="font-medium mb-1 text-white">
                    Upload Your CV
                  </h3>
                  <p className="text-slate-300">
                    Let our AI analyze your experience and skills
                  </p>
                </div>
              </div>
              <div className="flex gap-4 items-start">
                <div className="w-8 h-8 rounded-full bg-[#F6A03C]/20 flex items-center justify-center text-[#F6A03C] font-semibold">
                  4
                </div>
                <div>
                  <h3 className="font-medium mb-1 text-white">
                    Choose Your Plan
                  </h3>
                  <p className="text-slate-300">
                    Select the support level that matches your needs
                  </p>
                </div>
              </div>
              <div className="flex gap-4 items-start">
                <div className="w-8 h-8 rounded-full bg-[#F6A03C]/20 flex items-center justify-center text-[#F6A03C] font-semibold">
                  5
                </div>
                <div>
                  <h3 className="font-medium mb-1 text-white">
                    Get Expert AI Insights
                  </h3>
                  <p className="text-slate-300">
                    Receive tailored feedback and interview guidance
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* What You Get Section */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-white">
            What You Get:
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="backdrop-blur-sm bg-white/10 border-white/20 shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300">
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <div>
                    <h3 className="font-medium text-white">
                      Application Success
                    </h3>
                    <ul className="text-sm text-slate-300 list-disc ml-4 mt-2 space-y-1">
                      <li>CV optimization</li>
                      <li>Skills gap analysis</li>
                      <li>Keyword recommendations</li>
                      <li>Application strategies</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="backdrop-blur-sm bg-white/10 border-white/20 shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300">
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <div>
                    <h3 className="font-medium text-white">
                      Interview Preparation
                    </h3>
                    <ul className="text-sm text-slate-300 list-disc ml-4 mt-2 space-y-1">
                      <li>Practice with AI</li>
                      <li>Role-specific questions</li>
                      <li>Answer templates</li>
                      <li>Feedback on responses</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-12">
          <Card className="backdrop-blur-sm bg-white/10 border-white/20 shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <CreditCard className="w-5 h-5 text-[#F6A03C] mt-1" />
                <div>
                  <h3 className="font-medium text-white">Simple Pricing</h3>
                  <p className="text-sm text-slate-300">
                    Start with 10 free credits, then choose from flexible credit
                    packages
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="backdrop-blur-sm bg-white/10 border-white/20 shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Shield className="w-5 h-5 text-[#F6A03C] mt-1" />
                <div>
                  <h3 className="font-medium text-white">
                    Secure &amp; Private
                  </h3>
                  <p className="text-sm text-slate-300">
                    Your data is protected with bank-level security
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Secondary CTA Button */}
        <div className="text-center">
          <CTAButton />
          <p className="text-slate-400 text-sm mt-3">
            Ready to accelerate your career? Start your journey today.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;
