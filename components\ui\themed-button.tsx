// components/ui/themed-button.tsx
'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowR<PERSON>, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BaseButtonProps {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  showArrow?: boolean;
  fullWidth?: boolean;
}

interface ButtonProps extends BaseButtonProps {
  onClick?: (e?: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
}

interface LinkButtonProps extends BaseButtonProps {
  href: string;
  prefetch?: boolean;
}

const buttonVariants = {
  primary: {
    base: 'bg-gradient-to-r from-hero-yellow to-yellow-400 text-[#111827] hover:from-yellow-300 hover:to-yellow-400 shadow-lg',
    disabled: 'opacity-50 cursor-not-allowed'
  },
  secondary: {
    base: 'backdrop-blur-sm bg-white/10 border border-white/20 text-white hover:bg-white/20 shadow-lg',
    disabled: 'opacity-50 cursor-not-allowed'
  },
  outline: {
    base: 'border-2 border-hero-yellow text-hero-yellow hover:bg-hero-yellow hover:text-[#111827] bg-transparent',
    disabled: 'opacity-50 cursor-not-allowed'
  },
  ghost: {
    base: 'text-white hover:bg-white/10 bg-transparent',
    disabled: 'opacity-50 cursor-not-allowed'
  },
  danger: {
    base: 'bg-red-600 text-white hover:bg-red-700 shadow-lg',
    disabled: 'opacity-50 cursor-not-allowed'
  }
};

const sizeVariants = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg'
};

const getButtonClasses = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  fullWidth = false,
  className = ''
}: Partial<BaseButtonProps>) => {
  const variantClasses = buttonVariants[variant];
  const sizeClasses = sizeVariants[size];

  return cn(
    // Base button styles
    'inline-flex items-center justify-center gap-2 font-semibold rounded-full',
    'transition-all duration-300 transform active:scale-95',
    'focus:outline-none focus:ring-2 focus:ring-hero-yellow/50 focus:ring-offset-2 focus:ring-offset-transparent',

    // Variant styles
    disabled ? variantClasses.disabled : variantClasses.base,

    // Size styles
    sizeClasses,

    // Width
    fullWidth ? 'w-full' : '',

    // Custom classes
    className
  );
};

// Regular Button Component
export const ThemedButton: React.FC<ButtonProps> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  loading = false,
  showArrow = false,
  ...props
}) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && onClick) {
      onClick(e);
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      className={getButtonClasses({ disabled: disabled || loading, ...props })}
    >
      {loading && <Loader2 className="w-4 h-4 animate-spin" />}
      {children}
      {showArrow && !loading && (
        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
      )}
    </button>
  );
};

// Link Button Component
export const ThemedLinkButton: React.FC<LinkButtonProps> = ({
  children,
  href,
  prefetch = false,
  showArrow = false,
  ...props
}) => {
  return (
    <Link
      href={href}
      prefetch={prefetch}
      className={cn(getButtonClasses(props), 'group')}
    >
      {children}
      {showArrow && (
        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
      )}
    </Link>
  );
};

// Hybrid Button Component (can be either Link or Button based on props)
interface HybridButtonProps extends BaseButtonProps {
  href?: string;
  onClick?: (e?: React.MouseEvent) => void;
  type?: 'button' | 'submit' | 'reset';
  prefetch?: boolean;
}

export const HybridButton: React.FC<HybridButtonProps> = ({
  children,
  href,
  onClick,
  type = 'button',
  prefetch = false,
  disabled = false,
  loading = false,
  showArrow = false,
  ...props
}) => {
  const handleClick = (e: React.MouseEvent) => {
    if (!disabled && !loading && onClick) {
      onClick(e);
    }
  };

  // If href is provided, render as Link
  if (href) {
    return (
      <Link
        href={href}
        prefetch={prefetch}
        className={cn(
          getButtonClasses({ disabled: disabled || loading, ...props }),
          'group'
        )}
        onClick={onClick}
      >
        {loading && <Loader2 className="w-4 h-4 animate-spin" />}
        {children}
        {showArrow && !loading && (
          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
        )}
      </Link>
    );
  }

  // Otherwise, render as button
  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      className={getButtonClasses({ disabled: disabled || loading, ...props })}
    >
      {loading && <Loader2 className="w-4 h-4 animate-spin" />}
      {children}
      {showArrow && !loading && (
        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
      )}
    </button>
  );
};

// Specialized CTA Button for consistency
export const CTAButton: React.FC<LinkButtonProps> = ({
  children,
  size = 'lg',
  variant = 'primary',
  showArrow = true,
  ...props
}) => {
  return (
    <ThemedLinkButton
      size={size}
      variant={variant}
      showArrow={showArrow}
      {...props}
    >
      {children}
    </ThemedLinkButton>
  );
};

// Secondary Action Button
export const SecondaryButton: React.FC<LinkButtonProps> = ({
  children,
  size = 'lg',
  variant = 'secondary',
  showArrow = true,
  ...props
}) => {
  return (
    <ThemedLinkButton
      size={size}
      variant={variant}
      showArrow={showArrow}
      {...props}
    >
      {children}
    </ThemedLinkButton>
  );
};

// Navigation dots for carousels
export const NavDot: React.FC<{
  active: boolean;
  onClick: () => void;
  'aria-label': string;
}> = ({ active, onClick, 'aria-label': ariaLabel }) => {
  return (
    <button
      onClick={onClick}
      aria-label={ariaLabel}
      className={cn(
        'w-2 h-2 rounded-full transition-all duration-300',
        active
          ? 'bg-hero-yellow scale-100'
          : 'bg-hero-yellow/40 scale-75 hover:scale-90'
      )}
    />
  );
};

// Navigation arrow buttons
export const NavArrow: React.FC<{
  direction: 'left' | 'right';
  onClick: () => void;
  disabled?: boolean;
  'aria-label': string;
}> = ({ direction, onClick, disabled = false, 'aria-label': ariaLabel }) => {
  const Icon =
    direction === 'left'
      ? () => <ArrowRight className="w-6 h-6 rotate-180" />
      : () => <ArrowRight className="w-6 h-6" />;

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      className={cn(
        'bg-hero-yellow p-3 rounded-full shadow-lg transition-all duration-300',
        'hover:bg-hero-yellow-light hover:scale-110 active:scale-95',
        'text-[#111827] focus:outline-none focus:ring-2 focus:ring-hero-yellow/50',
        disabled && 'opacity-50 cursor-not-allowed hover:scale-100'
      )}
    >
      <Icon />
    </button>
  );
};

export default ThemedButton;
