'use client';

import { useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'next/script';

// Use the global Window interface from types.d.ts

interface GoogleAdsConversionProps {
  conversionId: string;
  conversionLabel: string;
  conversionValue?: number;
  transactionId?: string;
  currency?: string;
}

/**
 * Google Ads Conversion Tracking Component
 *
 * This component should be included on your success/thank you page
 * to track successful purchases as conversions in Google Ads.
 *
 * @param conversionId - Your Google Ads conversion ID
 * @param conversionLabel - Your Google Ads conversion label
 * @param conversionValue - The value of the conversion (optional)
 * @param transactionId - Unique transaction ID (optional)
 * @param currency - Currency code (default: 'GBP')
 */
export function GoogleAdsConversion({
  conversionId,
  conversionLabel,
  conversionValue,
  transactionId,
  currency = 'GBP'
}: GoogleAdsConversionProps) {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      // Define the dataLayer array if it doesn't exist
      window.dataLayer = window.dataLayer || [];

      // Define the gtag function if it doesn't exist
      window.gtag =
        window.gtag ||
        function (...args: unknown[]) {
          if (window.dataLayer) {
            window.dataLayer.push(...args);
          }
        };

      // Send the standard conversion event as required by Google Ads
      window.gtag('event', 'conversion', {
        send_to: `${conversionId}/${conversionLabel}`,
        value: conversionValue,
        currency: currency,
        transaction_id: transactionId
      });

      // Also send a custom event that matches your GA4 event name
      window.gtag('event', 'ads_conversion_Purchase_1', {
        value: conversionValue,
        currency: currency,
        transaction_id: transactionId
      });
    }
  }, [conversionId, conversionLabel, conversionValue, transactionId, currency]);

  return (
    <>
      {/* Google Ads conversion tracking script */}
      <Script
        id="google-ads-conversion-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){window.dataLayer.push(arguments);}
            window.gtag = gtag;
            gtag('js', new Date());
            gtag('config', '${conversionId}');
          `
        }}
      />
    </>
  );
}
