// app/page.tsx - OPTIMIZED VERSION
import { Metadata } from 'next';
import { constructMetadata } from '@/lib/seo-config';
import dynamic from 'next/dynamic';
import JsonLd from '@/components/seo/JsonLd';
import { faqLd } from 'lib/faq';
import { homepageFaqs } from 'lib/homepageFaqs';

// ✅ Lazy load heavy components with proper loading states
const HomePage = dynamic(() => import('@/components/home/<USER>'), {
  loading: () => (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-pulse">Loading...</div>
    </div>
  )
});

export const metadata: Metadata = constructMetadata({
  title: 'Free ATS CV Checker UK | Get Hired Faster 2025',
  description:
    'AI-powered CV generator, cover letter creator, and mock interview practice. Transform your job search with ATS-optimized tools built for UK professionals. Free CV checker included.',
  keywords: [
    'AI job preparation UK',
    'ATS CV optimizer',
    'job interview coaching',
    'cover letter generator',
    'UK job search tools',
    'career success platform',
    'CV builder UK',
    'interview preparation AI',
    'job application help',
    'career coaching online',
    'AI CV builder UK',
    'mock interview simulator',
    'AI career coach',
    'job application AI platform',
    'get a job UK AI',
    'AI job seeker tools'
  ],
  image: '/og-image-homepage.jpg',
  path: '/'
});

export default function Page() {
  return (
    <>
      <HomePage />
      <JsonLd id="homepage-faq-schema" data={faqLd('/', homepageFaqs)} />
    </>
  );
}
