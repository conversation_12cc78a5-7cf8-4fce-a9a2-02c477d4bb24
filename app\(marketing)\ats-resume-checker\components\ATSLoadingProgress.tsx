'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { CheckCircle2, FileText, Sparkles, Target, Clock } from 'lucide-react';

interface ATSLoadingProgressProps {
  isLoading: boolean;
  currentStep?: number;
}

const scanSteps = [
  {
    id: 1,
    title: 'Analyzing your CV',
    description: 'Extracting text and parsing CV structure...',
    icon: FileText,
    details:
      'Reading PDF content, identifying sections, and extracting key information'
  },
  {
    id: 2,
    title: 'Processing job description',
    description: 'Identifying key requirements and skills...',
    icon: Target,
    details:
      'Analyzing job requirements, extracting keywords, and mapping skills'
  },
  {
    id: 3,
    title: 'Running ATS compatibility check',
    description: 'Comparing against UK employer ATS systems...',
    icon: Sparkles,
    details:
      'Testing against Reed, NHS Jobs, Amazon UK, and Barclays ATS systems'
  },
  {
    id: 4,
    title: 'Generating recommendations',
    description: 'Creating personalized optimization suggestions...',
    icon: CheckCircle2,
    details: 'Preparing keyword suggestions and formatting improvements'
  }
];

export default function ATSLoadingProgress({
  isLoading,
  currentStep = 1
}: ATSLoadingProgressProps) {
  const [activeStep, setActiveStep] = useState(1);
  const [progress, setProgress] = useState(0);
  const [mounted, setMounted] = useState(false);

  // Ensure we're mounted before using portals
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!isLoading) {
      setActiveStep(1);
      setProgress(0);
      return;
    }

    // Set the active step based on the prop passed from parent
    setActiveStep(currentStep);

    // Animate progress for the current step
    let stepProgress = 0;

    const progressInterval = setInterval(() => {
      stepProgress += 2; // Increment by 2% every 100ms for smooth animation
      if (stepProgress >= 95) {
        // Stop at 95% to avoid completion before actual step finishes
        stepProgress = 95;
        clearInterval(progressInterval);
      }
      setProgress(stepProgress);
    }, 100);

    return () => {
      clearInterval(progressInterval);
    };
  }, [isLoading, currentStep]);

  if (!mounted || !isLoading) return null;

  const currentStepData = scanSteps[activeStep - 1];
  const IconComponent = currentStepData.icon;

  const modalContent = (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center p-4"
      style={{
        zIndex: 999999,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      }}
    >
      <div className="bg-gray-900/95 border border-gray-700 rounded-2xl p-8 w-full max-w-md shadow-2xl backdrop-blur-sm">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-yellow-400/20 rounded-full flex items-center justify-center mx-auto mb-4 ring-2 ring-yellow-400/30">
            <IconComponent className="w-8 h-8 text-yellow-400 animate-pulse" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">
            Scanning your CV
          </h3>
          <p className="text-slate-300 text-sm">
            Testing against UK employer ATS systems...
          </p>
        </div>

        {/* Progress Steps */}
        <div className="space-y-3 mb-6">
          {scanSteps.map((step) => {
            const StepIcon = step.icon;
            const isActive = step.id === activeStep;
            const isCompleted = step.id < activeStep;
            const isPending = step.id > activeStep;

            return (
              <div
                key={step.id}
                className={`flex items-start space-x-3 p-4 rounded-lg transition-all duration-500 ${
                  isActive
                    ? 'bg-yellow-400/15 border border-yellow-400/40 scale-[1.02]'
                    : isCompleted
                      ? 'bg-green-400/15 border border-green-400/40'
                      : 'bg-gray-800/50 border border-gray-700'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-500 flex-shrink-0 mt-0.5 ${
                    isActive
                      ? 'bg-yellow-400/25 ring-2 ring-yellow-400/60'
                      : isCompleted
                        ? 'bg-green-400/25 ring-2 ring-green-400/60'
                        : 'bg-gray-700/50'
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle2 className="w-5 h-5 text-green-400" />
                  ) : (
                    <StepIcon
                      className={`w-5 h-5 transition-all duration-300 ${
                        isActive
                          ? 'text-yellow-400 animate-pulse'
                          : isPending
                            ? 'text-slate-400'
                            : 'text-white'
                      }`}
                    />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p
                    className={`font-semibold text-sm transition-colors duration-300 ${
                      isActive
                        ? 'text-yellow-400'
                        : isCompleted
                          ? 'text-green-400'
                          : 'text-slate-300'
                    }`}
                  >
                    {step.title}
                  </p>
                  <p className="text-xs text-slate-400 mt-1 leading-relaxed">
                    {isActive ? step.description : step.details}
                  </p>
                  {isActive && (
                    <div className="mt-2">
                      <div className="w-full bg-gray-700/50 rounded-full h-1.5">
                        <div
                          className="h-1.5 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full transition-all duration-300 ease-out shadow-sm"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                {isActive && (
                  <div className="flex-shrink-0 mt-1">
                    <svg
                      className="animate-spin w-4 h-4 text-yellow-400"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                      />
                    </svg>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Overall Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-xs text-slate-400 mb-2">
            <span>Progress</span>
            <span>
              {Math.round(
                ((activeStep - 1) * 100 + progress) / scanSteps.length
              )}
              %
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-2">
            <div
              className="h-2 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full transition-all duration-300 ease-out"
              style={{
                width: `${((activeStep - 1) * 100 + progress) / scanSteps.length}%`
              }}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="text-center space-y-3">
          <div className="flex items-center justify-center space-x-2 text-slate-400 text-xs">
            <Clock className="w-3 h-3" />
            <span>This usually takes 10-15 seconds</span>
          </div>

          {/* Fun facts while loading */}
          <div className="text-xs text-slate-500 italic">
            {activeStep === 1 &&
              '💡 Did you know? 75% of CVs are rejected by ATS systems before human review'}
            {activeStep === 2 &&
              '🎯 Tip: Most job descriptions contain 15-25 essential keywords'}
            {activeStep === 3 &&
              '🏢 Testing against systems used by FTSE 100 companies'}
            {activeStep === 4 && '✨ Personalized recommendations coming up!'}
          </div>
        </div>
      </div>
    </div>
  );

  // Use a portal to render outside the component tree
  return createPortal(modalContent, document.body);
}
