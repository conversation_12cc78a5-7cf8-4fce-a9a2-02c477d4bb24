import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { cn } from '@/lib/utils';
import { useDashboardContext } from './DashboardContext';
import {
  RecruitmentAgency,
  GooglePlacesRecruitmentAgency
} from '@/app/types/globalTypes';
import { Review } from './Review';
import { renderStarRating } from './StarRating';
import { Loader2 } from 'lucide-react';

type ApiChoice = 'companiesHouse' | 'googlePlaces';
type Agency = RecruitmentAgency | GooglePlacesRecruitmentAgency;

interface RecruitmentAgenciesContentProps {
  onClose?: () => void;
}

export const RecruitmentAgenciesContent: React.FC<
  RecruitmentAgenciesContentProps
> = ({ onClose }) => {
  console.log(
    'RecruitmentAgenciesContent - Component function called with props:',
    { onClose }
  );

  const { selectedAgencies, setSelectedAgencies } = useDashboardContext();
  const [location, setLocation] = useState('');
  const [sector, setSector] = useState('');
  const [apiChoice, setApiChoice] = useState<ApiChoice>('companiesHouse');
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Removed unused initialized state
  const [renderCount, setRenderCount] = useState(0);

  // Log when component is rendered
  useEffect(() => {
    console.log(
      'RecruitmentAgenciesContent - useEffect triggered, render count:',
      renderCount
    );
    setRenderCount((prev) => prev + 1);

    // Debug the DOM to see if our component is actually in the document
    console.log('RecruitmentAgenciesContent - DOM check:', {
      dialogElements: document.querySelectorAll('dialog').length,
      dialogContentElements: document.querySelectorAll('[role="dialog"]').length
    });
  }, [renderCount]);

  const validateForm = () => {
    if (!location.trim()) {
      setError('Location is required');
      return false;
    }
    if (!sector.trim()) {
      setError(
        `${apiChoice === 'companiesHouse' ? 'Sector' : 'Specialization'} is required`
      );
      return false;
    }
    return true;
  };

  const handleSearch = async () => {
    setIsLoading(true);
    setError(null);

    if (!validateForm()) {
      setIsLoading(false);
      return;
    }

    try {
      const url =
        apiChoice === 'companiesHouse'
          ? `${process.env.NEXT_PUBLIC_URL}/api/recruitment-agencies`
          : `${process.env.NEXT_PUBLIC_URL}/api/recruitment-agencies-google`;

      console.log('Making request to:', url);
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          location,
          [apiChoice === 'companiesHouse' ? 'sector' : 'specialization']: sector
        })
      });

      console.log('Response status:', response.status);
      const data = await response.json();
      console.log('Response data:', data);

      if (!response.ok) {
        throw new Error(
          `Failed to fetch recruitment agencies from ${apiChoice}`
        );
      }

      setAgencies(data.agencies);
    } catch (err) {
      console.error('Error fetching agencies:', err);
      if (err instanceof Error) {
        if (err.message.includes('Failed to fetch')) {
          setError(
            'Network error: Please check your internet connection and try again.'
          );
        } else if (err.message.includes('404')) {
          setError(
            'Service not available: The recruitment agencies service is currently unavailable.'
          );
        } else {
          setError(`An error occurred while fetching agencies: ${err.message}`);
        }
      } else {
        setError('An unexpected error occurred. Please try again later.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleAgencySelection = (agency: Agency) => {
    setSelectedAgencies((prev) => {
      const exists = prev.some((a) => a.name === agency.name);
      return exists
        ? prev.filter((a) => a.name !== agency.name)
        : [...prev, agency];
    });
  };

  const handleSelectAll = () => setSelectedAgencies(agencies);
  const handleSelectNone = () => setSelectedAgencies([]);

  const renderAgencyDetails = (agency: Agency) => {
    if ('number' in agency) {
      return (
        <>
          <p className="text-sm text-white">
            {agency.address || 'Address not available'}
          </p>
          <p className="text-sm text-slate-300">
            Company Number: {agency.number}
          </p>
          {agency.website && agency.website !== 'N/A' && (
            <p className="text-sm">
              Website:{' '}
              <a
                href={agency.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[hsl(var(--hero-yellow))] hover:text-[hsl(var(--hero-yellow-light))] hover:underline"
              >
                {agency.website}
              </a>
            </p>
          )}
          {agency.sic_codes && agency.sic_codes.length > 0 && (
            <p className="text-sm text-slate-300">
              SIC Codes: {agency.sic_codes.join(', ')}
            </p>
          )}
        </>
      );
    }

    return (
      <>
        <p className="text-sm text-white">{agency.address}</p>
        {agency.phone && (
          <p className="text-sm text-slate-300">Phone: {agency.phone}</p>
        )}
        {agency.website && (
          <p className="text-sm">
            Website:{' '}
            <a
              href={agency.website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-[hsl(var(--hero-yellow))] hover:text-[hsl(var(--hero-yellow-light))] hover:underline"
            >
              {agency.website}
            </a>
          </p>
        )}
        {agency.rating && (
          <div className="flex items-center mt-2">
            <span className="mr-2 text-white">Rating:</span>
            {renderStarRating(agency.rating)}
            <span className="ml-2 text-white">{agency.rating.toFixed(1)}</span>
          </div>
        )}
        {agency.opening_hours && agency.opening_hours.length > 0 && (
          <Accordion type="single" collapsible className="w-full mt-2">
            <AccordionItem value="opening-hours" className="border-gray-700">
              <AccordionTrigger className="hover:text-[hsl(var(--hero-yellow))]">
                Opening Hours
              </AccordionTrigger>
              <AccordionContent>
                <ul className="text-xs text-slate-300 list-disc list-inside">
                  {agency.opening_hours.map((h, i) => (
                    <li key={i}>{h}</li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}
        {agency.reviews && agency.reviews.length > 0 && (
          <Accordion type="single" collapsible className="w-full mt-2">
            <AccordionItem value="reviews" className="border-gray-700">
              <AccordionTrigger className="hover:text-[hsl(var(--hero-yellow))]">
                Reviews ({agency.reviews.length})
              </AccordionTrigger>
              <AccordionContent>
                {agency.reviews.map((r, i) => (
                  <Review key={i} review={r} />
                ))}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}
      </>
    );
  };

  // Show loading indicator if the component is still initializing
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4 p-6 backdrop-blur-sm bg-[#1e293b]/90 rounded-lg border border-gray-800">
        <Loader2 className="h-8 w-8 animate-spin text-[hsl(var(--hero-yellow))]" />
        <p className="text-sm text-slate-300">
          Loading recruitment agencies...
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-4 p-6 backdrop-blur-sm bg-[#1e293b]/90 rounded-lg border border-gray-800">
      <div className="flex items-center gap-3">
        <div className="w-1.5 h-8 bg-[hsl(var(--hero-yellow))] rounded-full shadow-[0_0_8px_rgba(246,160,60,0.5)]"></div>
        <div>
          <h2 className="text-2xl font-bold text-white bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent drop-shadow-[0_0_2px_rgba(255,255,255,0.3)]">
            Recruitment Agencies
          </h2>
          <p className="text-sm text-slate-300 mt-1">
            Find recruitment agencies that match your profile
          </p>
        </div>
      </div>

      <div className="grid gap-4 py-4 overflow-y-auto">
        <RadioGroup
          defaultValue="companiesHouse"
          onValueChange={(v) => setApiChoice(v as ApiChoice)}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value="companiesHouse"
              id="companiesHouse"
              className="border-gray-700 text-[hsl(var(--hero-yellow))]"
            />
            <Label htmlFor="companiesHouse" className="text-slate-300">
              Companies House
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value="googlePlaces"
              id="googlePlaces"
              className="border-gray-700 text-[hsl(var(--hero-yellow))]"
            />
            <Label htmlFor="googlePlaces" className="text-slate-300">
              Google Places
            </Label>
          </div>
        </RadioGroup>

        <div className="space-y-2">
          <Label
            htmlFor="location"
            className="text-sm font-medium text-slate-300"
          >
            Location <span className="text-destructive">*</span>
          </Label>
          <Input
            id="location"
            placeholder="Enter location (e.g., London, Manchester)"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            required
            className={`bg-[#1e293b] border-gray-700 text-white ${!location.trim() && error ? 'border-destructive' : 'focus:border-[hsl(var(--hero-yellow))]'}`}
          />
        </div>

        <div className="space-y-2">
          <Label
            htmlFor="sector"
            className="text-sm font-medium text-slate-300"
          >
            {apiChoice === 'companiesHouse' ? 'Sector' : 'Specialization'}{' '}
            <span className="text-destructive">*</span>
          </Label>
          <Input
            id="sector"
            placeholder={`Enter ${apiChoice === 'companiesHouse' ? 'sector' : 'specialization'} (e.g., IT, Finance)`}
            value={sector}
            onChange={(e) => setSector(e.target.value)}
            required
            className={`bg-[#1e293b] border-gray-700 text-white ${!sector.trim() && error ? 'border-destructive' : 'focus:border-[hsl(var(--hero-yellow))]'}`}
          />
        </div>

        <Button
          onClick={handleSearch}
          disabled={isLoading || !location.trim() || !sector.trim()}
          className="mt-2 bg-[hsl(var(--hero-yellow))] text-[#111827] hover:bg-[hsl(var(--hero-yellow-light))] hover:border hover:border-yellow-300"
        >
          {isLoading ? 'Searching...' : 'Search'}
        </Button>

        {error && (
          <div className="bg-destructive/10 border border-destructive rounded-md p-3">
            <p className="text-destructive text-sm">{error}</p>
          </div>
        )}

        {agencies.length > 0 && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold text-white">Results:</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-slate-300">
                  Selected: {selectedAgencies.length} / {agencies.length}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="border-gray-700 text-white hover:bg-[hsl(var(--hero-bg))]/50 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))]"
                >
                  Select All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectNone}
                  className="border-gray-700 text-white hover:bg-[hsl(var(--hero-bg))]/50 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))]"
                >
                  Select None
                </Button>
              </div>
            </div>
            <ul className="space-y-4">
              {agencies.map((agency, idx) => (
                <li
                  key={idx}
                  className={cn(
                    'border p-4 rounded cursor-pointer transition-all duration-200 backdrop-blur-sm bg-[#1e293b]/80',
                    selectedAgencies.some((a) => a.name === agency.name)
                      ? 'border-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.3)]'
                      : 'border-gray-700 hover:border-[hsl(var(--hero-yellow))]'
                  )}
                  onClick={() => handleAgencySelection(agency)}
                >
                  <h4 className="font-medium text-white">{agency.name}</h4>
                  {renderAgencyDetails(agency)}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      <div className="mt-4 flex justify-end">
        <Button
          onClick={() => onClose?.()}
          className="border border-gray-700 bg-[#1e293b] text-white hover:bg-[hsl(var(--hero-bg))]/50 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))]"
        >
          Close
        </Button>
      </div>
    </div>
  );
};
