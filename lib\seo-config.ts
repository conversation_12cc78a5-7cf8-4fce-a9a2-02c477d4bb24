import type { Metadata } from 'next';

export const siteConfig = {
  name: 'Job Space AI',
  url: 'https://jobspaceai.com',
  ogImage: '/og-image.jpg',
  description:
    'Stop rewriting your résumé and cover letter for every job application. Our AI tailors your documents in under 60 seconds, handling all the buzzwords, skills alignment, and ATS-friendly formatting for you.',
  keywords: [
    'CV tailoring',
    'cover letter generator',
    'ATS optimization',
    'job application automation',
    'CV rewriting',
    'AI CV writer',
    'custom cover letters',
    'job-specific CV',
    'automatic CV tailoring',
    'CV customization',
    'job application documents',
    'CV buzzwords',
    'skills alignment',
    'ATS-friendly formatting',
    'CV optimization',
    'quick CV tailoring',
    'AI cover letter writer',
    'job application efficiency',
    'CV automation'
  ],
  tagline: 'Never Rewrite Your Job Application Documents Again',
  valueProps: [
    'Instant Résumé Tailoring',
    'AI Cover Letter Writer',
    '60-Second Turnaround',
    'ATS-Friendly Formatting',
    'Unlimited Rewrites'
  ],
  twitter: {
    handle: '@jobspaceai',
    site: '@jobspaceai',
    cardType: 'summary_large_image'
  }
};

export type MetadataProps = {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  noIndex?: boolean;
  path?: string; // Page-specific path
};

export function constructMetadata({
  title,
  description,
  keywords = [],
  image,
  noIndex,
  path = ''
}: MetadataProps = {}): Metadata {
  // Normalize path
  const normalizedPath =
    path.endsWith('/') && path !== '/' ? path.slice(0, -1) : path;
  const canonicalUrl = `${siteConfig.url}${normalizedPath}`;

  // Derive page title from slug if no title provided
  const slugPart =
    normalizedPath && normalizedPath !== '/'
      ? normalizedPath.replace(/^\//, '').split('/').pop() || ''
      : '';
  const derivedTitle = title
    ? title
    : slugPart
      ? slugPart.replace(/[-_]/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase())
      : '';
  const metaTitle = derivedTitle
    ? `${derivedTitle} | ${siteConfig.name}`
    : siteConfig.name;

  // Build page-specific description if none provided
  const pageDescription = description
    ? description
    : derivedTitle
      ? `${derivedTitle} — ${siteConfig.description}`
      : siteConfig.description;

  return {
    title: metaTitle,
    description: pageDescription,
    keywords: [...siteConfig.keywords, ...keywords].join(', '),
    authors: [{ name: siteConfig.name }],
    creator: siteConfig.name,
    openGraph: {
      type: 'website',
      locale: 'en_GB',
      url: canonicalUrl,
      title: metaTitle,
      description: pageDescription,
      siteName: siteConfig.name,
      images: [
        {
          url: image || siteConfig.ogImage,
          width: 1200,
          height: 630,
          alt: derivedTitle || siteConfig.name
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: pageDescription,
      images: [image || siteConfig.ogImage],
      creator: siteConfig.twitter.handle
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
      googleBot: {
        index: !noIndex,
        follow: !noIndex,
        'max-video-preview': -1,
        'max-image-preview': 'large' as const,
        'max-snippet': -1
      }
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code'
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en-GB': canonicalUrl,
        'x-default': canonicalUrl
      }
    },
    metadataBase: new URL(siteConfig.url),
    icons: {
      icon: '/favicon.ico',
      shortcut: '/favicon-16x16.png',
      apple: '/apple-touch-icon.png'
    }
  };
}
