// app/dashboard/[id]/page.tsx
import { Job, Resume } from '@/app/types/globalTypes';
import { createClient } from '@/app/utils/supabase/server';
import { SERVICE_CREDIT_COSTS } from '@/lib/credit-costs';
import { constructMetadata } from '@/lib/seo-config';
import { Metadata } from 'next';
import SignInRequired from '@/components/ui/AuthForms/SignInRequired';

// CRITICAL: Import the wrapper, not the dashboard directly
import DashboardPageWrapper from './DashboardPageWrapper';

export async function generateMetadata({
  params
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const resolvedParams = await params;
  return constructMetadata({
    title: 'Your Dashboard | Job Space AI',
    description:
      'Access your personalized dashboard to manage your job search, CVs, and career tools.',
    path: `/dashboard/${resolvedParams.id || ''}`
  });
}

interface UserCredits {
  user_id: string;
  amount: number;
  updated_at: string;
}

// TODO: Reinstate Salary Predictor and Gamified Search features once implementations are ready
const allFeatures = [
  {
    title: 'ATS',
    description:
      'See how applicant tracking system (ATS) software views your CV.',
    icon: '📋',
    creditCost: SERVICE_CREDIT_COSTS.ATS_ANALYSIS,
    requiresJob: true,
    requiresResume: true
  },
  {
    title: 'Skill Gap Analysis',
    description:
      'Analyze your CV and find missing skills for your target job roles.',
    icon: '📊',
    creditCost: SERVICE_CREDIT_COSTS.SKILL_GAP_ANALYSIS,
    requiresJob: true,
    requiresResume: true
  },
  {
    title: 'Mock Interview',
    description: 'Practice interviewing with AI for your target role.',
    icon: '🎯',
    creditCost: SERVICE_CREDIT_COSTS.MOCK_INTERVIEW,
    requiresJob: true,
    requiresResume: true
  },
  {
    title: 'Recruitment Agencies',
    description: 'Find recruitment agencies specializing in your field.',
    icon: '🏢',
    creditCost: SERVICE_CREDIT_COSTS.RECRUITMENT_AGENCIES,
    requiresJob: false,
    requiresResume: false
  },
  {
    title: 'AI CV Improvement',
    description: 'Get AI-powered suggestions to improve your CV.',
    icon: '✨',
    creditCost: SERVICE_CREDIT_COSTS.RESUME_IMPROVEMENT,
    requiresJob: true,
    requiresResume: true
  },
  {
    title: 'AI Career Matching',
    description: 'Discover careers that match your skills and experience.',
    icon: '🎯',
    creditCost: SERVICE_CREDIT_COSTS.CAREER_MATCHING,
    requiresJob: false,
    requiresResume: true
  },
  {
    title: 'Cover Letter Generator',
    description: 'Generate tailored cover letters for your job applications.',
    icon: '📝',
    creditCost: SERVICE_CREDIT_COSTS.COVER_LETTER,
    requiresJob: true,
    requiresResume: true
  },
  {
    title: 'AI Career Coach',
    description:
      'Get instant answers to your job search questions with our AI-powered assistant.',
    icon: '💬',
    creditCost: SERVICE_CREDIT_COSTS.CAREER_COACHING,
    requiresJob: false,
    requiresResume: true
  },
  {
    title: 'Market Trends',
    description: 'Analyze current market trends and salary data.',
    icon: '📈',
    creditCost: SERVICE_CREDIT_COSTS.MARKET_TRENDS,
    requiresJob: false,
    requiresResume: false
  }
];

// Filter out the Success Prediction feature
const features = allFeatures.filter(
  (feature) => feature.title !== 'Success Prediction'
);

export default async function DashboardPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  // Get the dashboard ID from params
  const resolvedParams = await params;
  const dashboardId = resolvedParams.id;

  try {
    const supabase = await createClient();

    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
      return (
        <SignInRequired
          message="You need to sign in to access your dashboard."
          redirectPath="/signin"
        />
      );
    }

    // Fetch resumes
    const { data: resumesData, error: resumesError } = await supabase
      .from('user_resumes')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // Fetch jobs
    const { data: savedJobsData, error: jobsError } = await supabase
      .from('user_jobs')
      .select('*')
      .eq('user_id', user.id);

    // Get user's credits
    const { data: creditsData, error: creditsError } = await supabase
      .from('credits')
      .select('*')
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })
      .limit(1);

    let userCredits: UserCredits | null = null;

    // If no credits found, create initial credits record
    if ((!creditsData || creditsData.length === 0) && !creditsError) {
      const { data: newCreditsData, error: insertError } = await supabase
        .from('credits')
        .insert([
          {
            user_id: user.id,
            amount: 80 // Initial credits amount
          }
        ])
        .select();

      if (insertError) {
        console.error('Error creating initial credits:', insertError);
      }

      if (newCreditsData && newCreditsData.length > 0) {
        userCredits = newCreditsData[0] as UserCredits;
      }
    } else if (creditsData && creditsData.length > 0) {
      userCredits = creditsData[0] as UserCredits;
    }

    const error = resumesError || jobsError || creditsError;

    if (error) {
      console.error('Dashboard error:', error);
    }

    // CRITICAL: Use the wrapper component instead of Dashboard directly
    return (
      <DashboardPageWrapper
        userId={user.id}
        initialFeatures={features}
        initialResumes={(resumesData as Resume[]) || []}
        initialJobs={(savedJobsData as Job[]) || []}
        initialError={error}
        initialCredits={userCredits?.amount ?? 0}
        dashboardId={dashboardId}
      />
    );
  } catch (error) {
    console.error('Error in DashboardPage:', error);
    return (
      <SignInRequired
        message="There was an error loading your dashboard."
        redirectPath="/signin"
      />
    );
  }
}
