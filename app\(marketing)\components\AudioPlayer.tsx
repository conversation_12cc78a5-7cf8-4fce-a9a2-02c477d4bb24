'use client';

import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2 } from 'lucide-react';

interface AudioPlayerProps {
  src?: string | string[]; // Allow multiple sources
  title: string;
  duration: string;
  storageKey?: string; // For Supabase storage compatibility
}

export default function AudioPlayer({
  src,
  title,
  duration,
  storageKey
}: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const audioRef = useRef<HTMLAudioElement>(null);

  // Function to get Supabase audio URL
  const getSupabaseAudioUrl = async (storageKey: string) => {
    try {
      console.log('🔍 Attempting to load audio from Supabase:', storageKey);

      // Import Supabase client dynamically to avoid SSR issues
      const { createClient } = await import('@supabase/supabase-js');

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

      console.log('🔧 Supabase config:', {
        hasUrl: !!supabaseUrl,
        hasKey: !!supabaseAnonKey,
        url: supabaseUrl?.substring(0, 20) + '...'
      });

      if (!supabaseUrl || !supabaseAnonKey) {
        throw new Error('Supabase configuration missing');
      }

      const supabase = createClient(supabaseUrl, supabaseAnonKey);

      // Use the correct bucket name: articles-media
      console.log('🪣 Using bucket: articles-media');

      // Get public URL for the audio file
      const { data } = supabase.storage
        .from('articles-media')
        .getPublicUrl(storageKey);

      console.log('🌐 Generated URL:', data.publicUrl);

      // Test if the URL is accessible
      try {
        const response = await fetch(data.publicUrl, { method: 'HEAD' });
        console.log(
          '🌐 URL test response:',
          response.status,
          response.statusText
        );

        if (response.ok) {
          return data.publicUrl;
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (fetchError) {
        console.error('❌ URL test failed:', fetchError);
        throw new Error(`URL not accessible: ${fetchError}`);
      }
    } catch (error) {
      console.error('❌ Error getting Supabase URL:', error);
      throw error;
    }
  };

  // Initialize audio URL
  useEffect(() => {
    const initializeAudio = async () => {
      try {
        console.log('🎵 Initializing audio...', { storageKey, src });
        setIsLoading(true);
        setError(null);

        if (storageKey) {
          // Use Supabase storage
          console.log('📂 Using Supabase storage key:', storageKey);
          const url = await getSupabaseAudioUrl(storageKey);
          console.log('✅ Got Supabase URL:', url);
          setAudioUrl(url);
        } else if (typeof src === 'string') {
          // Use direct URL
          console.log('🔗 Using direct URL:', src);
          setAudioUrl(src);
        } else if (Array.isArray(src) && src.length > 0) {
          // Use first URL from array
          console.log('📋 Using first URL from array:', src[0]);
          setAudioUrl(src[0]);
        } else {
          throw new Error('No audio source provided');
        }
      } catch (err) {
        console.error('❌ Error initializing audio:', err);
        setError(
          `Failed to load audio file: ${err instanceof Error ? err.message : 'Unknown error'}`
        );
        setIsLoading(false);
      }
    };

    initializeAudio();
  }, [src, storageKey]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !audioUrl) {
      console.log('⏸️ Audio element or URL not ready:', {
        hasAudio: !!audio,
        audioUrl
      });
      return;
    }

    console.log('🎵 Setting up audio event listeners for:', audioUrl);

    // Set a timeout to catch hanging loads
    const loadTimeout = setTimeout(() => {
      console.error('⏰ Audio load timeout after 10 seconds');
      setError(
        'Audio loading timed out. The file may be too large or inaccessible.'
      );
      setIsLoading(false);
    }, 10000);

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => {
      console.log('⏱️ Audio duration loaded:', audio.duration);
      setAudioDuration(audio.duration);
      setIsLoading(false);
      clearTimeout(loadTimeout);
    };
    const handleEnded = () => {
      console.log('🔚 Audio ended');
      setIsPlaying(false);
    };
    const handleError = (e: Event) => {
      console.error('❌ Audio error event:', e);
      const target = e.target as HTMLAudioElement;
      console.error('Audio error details:', {
        error: target.error,
        code: target.error?.code,
        message: target.error?.message,
        networkState: target.networkState,
        readyState: target.readyState,
        src: target.src
      });

      // More specific error messages
      let errorMessage = 'Audio file could not be loaded';
      if (target.error) {
        switch (target.error.code) {
          case MediaError.MEDIA_ERR_ABORTED:
            errorMessage = 'Audio loading was aborted';
            break;
          case MediaError.MEDIA_ERR_NETWORK:
            errorMessage = 'Network error while loading audio';
            break;
          case MediaError.MEDIA_ERR_DECODE:
            errorMessage = 'Audio file is corrupted or unsupported format';
            break;
          case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'Audio format not supported by browser';
            break;
        }
      }

      setError(errorMessage);
      setIsLoading(false);
      clearTimeout(loadTimeout);
    };
    const handleCanPlay = () => {
      console.log('✅ Audio can play');
      setError(null);
      setIsLoading(false);
      clearTimeout(loadTimeout);
    };
    const handleLoadStart = () => {
      console.log('⏳ Audio load started');
      setIsLoading(true);
    };
    const handleProgress = () => {
      console.log('📊 Audio loading progress:', {
        buffered: audio.buffered.length > 0 ? audio.buffered.end(0) : 0,
        duration: audio.duration
      });
    };

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('progress', handleProgress);

    // Force load the audio
    console.log('🔄 Forcing audio load...');
    audio.load();

    return () => {
      clearTimeout(loadTimeout);
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('progress', handleProgress);
    };
  }, [audioUrl]);

  const togglePlayPause = async () => {
    const audio = audioRef.current;
    if (!audio || error) return;

    try {
      if (isPlaying) {
        audio.pause();
      } else {
        await audio.play();
      }
      setIsPlaying(!isPlaying);
    } catch (err) {
      console.error('Audio playback failed:', err);
      setError('Playback failed');
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = (parseFloat(e.target.value) / 100) * audioDuration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="max-w-2xl mx-auto mb-8">
      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <Volume2 className="h-5 w-5 text-yellow-400" />
          <span className="text-white font-medium">{title}</span>
          <span className="text-slate-400 text-sm">• {duration}</span>
        </div>

        {error ? (
          <div className="text-red-400 text-sm p-4 bg-red-500/10 border border-red-400/20 rounded-lg">
            <div className="font-semibold mb-2">Audio Error:</div>
            <div className="mb-2">{error}</div>
            {storageKey && (
              <div className="text-xs opacity-75">
                <div>Storage Key: {storageKey}</div>
                <div>Audio URL: {audioUrl || 'Not generated'}</div>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Debug info - remove this in production */}
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs text-slate-500 mb-2 p-2 bg-slate-800/50 rounded">
                <div className="mb-1">
                  Debug:{' '}
                  {isLoading
                    ? 'Loading...'
                    : `Ready (${audioUrl ? 'URL set' : 'No URL'})`}
                </div>
                {audioUrl && (
                  <div className="space-y-1">
                    <div>URL: {audioUrl}</div>
                    <button
                      onClick={() => window.open(audioUrl, '_blank')}
                      className="text-blue-400 hover:text-blue-300 underline"
                    >
                      Test URL in new tab
                    </button>
                  </div>
                )}
              </div>
            )}

            <div className="flex items-center gap-4">
              <button
                onClick={togglePlayPause}
                disabled={isLoading || !!error}
                className="flex items-center justify-center w-12 h-12 bg-yellow-400 hover:bg-yellow-300 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-full transition-colors"
                aria-label={isPlaying ? 'Pause audio' : 'Play audio'}
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                ) : isPlaying ? (
                  <Pause className="h-6 w-6 text-black" />
                ) : (
                  <Play className="h-6 w-6 text-black ml-1" />
                )}
              </button>

              <div className="flex-1">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={
                    audioDuration ? (currentTime / audioDuration) * 100 : 0
                  }
                  onChange={handleSeek}
                  disabled={isLoading || !!error}
                  className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer disabled:cursor-not-allowed"
                  style={{
                    background: `linear-gradient(to right, #facc15 0%, #facc15 ${audioDuration ? (currentTime / audioDuration) * 100 : 0}%, #334155 ${audioDuration ? (currentTime / audioDuration) * 100 : 0}%, #334155 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-slate-400 mt-1">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(audioDuration)}</span>
                </div>
              </div>
            </div>
          </>
        )}

        {audioUrl && (
          <audio ref={audioRef} preload="metadata">
            <source src={audioUrl} />
            Your browser does not support the audio element.
          </audio>
        )}
      </div>
    </div>
  );
}
