import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription
} from '../ui/dialog';
import { ThemedButton } from '../ui/themed-button';
import { ExternalLink, MapPin, Building, Clock, Banknote } from 'lucide-react';
import { Job, JobAnalysisResult } from '../../app/types/globalTypes';

interface JobDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  job: Job | null;
}

const renderAnalysisResult = (
  analysisResult: JobAnalysisResult | undefined | null
): React.ReactNode => {
  if (!analysisResult) {
    return null;
  }
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        {analysisResult?.job_title && (
          <h3 className="text-2xl font-semibold text-white">
            {analysisResult.job_title}
          </h3>
        )}

        <div className="flex flex-wrap gap-4 md:gap-6">
          {analysisResult.company_name && (
            <div className="flex items-center gap-2 text-slate-300">
              <Building className="h-4 w-4 flex-shrink-0 text-yellow-400" />
              <span className="text-sm md:text-base">
                {analysisResult.company_name}
              </span>
            </div>
          )}
          {analysisResult.location && (
            <div className="flex items-center gap-2 text-slate-300">
              <MapPin className="h-4 w-4 flex-shrink-0 text-yellow-400" />
              <span className="text-sm md:text-base">
                {analysisResult.location}
              </span>
            </div>
          )}
          {analysisResult.job_type && (
            <div className="flex items-center gap-2 text-slate-300">
              <Clock className="h-4 w-4 flex-shrink-0 text-yellow-400" />
              <span className="text-sm md:text-base">
                {analysisResult.job_type}
              </span>
            </div>
          )}
          {analysisResult.salary_range && (
            <div className="flex items-center gap-2 text-slate-300">
              <Banknote className="h-4 w-4 flex-shrink-0 text-yellow-400" />
              <span className="text-sm md:text-base">
                {analysisResult.salary_range}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="divide-y divide-white/10">
        {analysisResult.sections?.map((section, index) => (
          <div key={index} className="py-6 first:pt-0 last:pb-0">
            <h4 className="text-lg font-semibold text-white mb-4">
              {section.title}
            </h4>
            <ul className="space-y-3">
              {section.content.map((item, itemIndex) => (
                <li key={itemIndex} className="flex gap-3">
                  <span className="text-yellow-400 mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-yellow-400" />
                  <span className="text-slate-300 text-sm md:text-base">
                    {item}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export const JobDetailsDialog: React.FC<JobDetailsDialogProps> = ({
  isOpen,
  onClose,
  job
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto sm:p-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg">
        {/* Add top padding for mobile */}
        <div className="pt-6 pb-4 sm:p-0">
          <DialogHeader className="mb-6 sm:mb-8">
            <DialogTitle className="text-xl sm:text-2xl text-white">
              Job Details
            </DialogTitle>
            <DialogDescription className="text-slate-300 text-sm">
              Detailed information about the selected job posting
            </DialogDescription>
          </DialogHeader>

          {job && (
            <div className="space-y-6 sm:space-y-8">
              {job.job_link && (
                <div className="flex items-center justify-between p-3 sm:p-4 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
                  <span className="text-sm text-slate-300">
                    Original Job Posting
                  </span>
                  <ThemedButton
                    variant="primary"
                    size="sm"
                    onClick={() =>
                      window.open(job.job_link, '_blank', 'noopener noreferrer')
                    }
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span className="hidden sm:inline">View Job</span>
                    <span className="sm:hidden">View</span>
                  </ThemedButton>
                </div>
              )}

              <div>
                <div className="space-y-1 mb-6">
                  <h3 className="text-lg font-semibold text-white">
                    Analysis Result
                  </h3>
                  <p className="text-sm text-slate-300">
                    AI-powered analysis of the job posting
                  </p>
                </div>

                {job.analysis_result &&
                  renderAnalysisResult(job.analysis_result)}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default JobDetailsDialog;
