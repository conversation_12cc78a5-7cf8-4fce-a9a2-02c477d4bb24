// components/home/<USER>
'use client';

import dynamic from 'next/dynamic';
import React from 'react';

// ✅ Dynamic import with no SSR to prevent consent hook issues
const HeroSection = dynamic(() => import('./HeroWithConsent'), {
  ssr: false,
  loading: () => (
    // Loading placeholder that matches the hero layout
    <section id="hero" className="relative w-full isolate overflow-visible">
      <div className="relative z-10 min-h-[calc(100vh-64px)] flex flex-col justify-center">
        <div className="mx-auto w-full max-w-5xl px-4 sm:px-6 lg:px-8 py-12">
          {/* Skeleton content */}
          <div className="mb-6 flex justify-center">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-hero-yellow/10 to-orange-400/10 backdrop-blur-sm border border-hero-yellow/30 rounded-full px-6 py-3 text-hero-yellow text-xs sm:text-sm font-semibold shadow-lg opacity-50">
              <span className="w-4 h-4 bg-purple-400 rounded animate-pulse" />
              <span>AI-Powered Career Tools for UK Professionals</span>
              <span className="w-4 h-4 bg-yellow-400 rounded animate-pulse" />
            </div>
          </div>

          <div className="mb-8 text-center">
            <h1 className="font-roboto-condensed mb-4 font-bold leading-[0.85] tracking-[-0.02em] text-white drop-shadow-2xl text-4xl md:text-8xl lg:text-8xl opacity-50">
              AI CV Generator &<br />
              <span className="text-yellow-400 text-shadow-yellow">
                Mock Interview Practice
              </span>
            </h1>
            <h2 className="mb-6 font-semibold leading-[1.1] text-slate-200 text-xl sm:text-2xl md:text-3xl opacity-50">
              Land your next job with our AI-powered job-prep platform for UK
              professionals
            </h2>
            <p className="text-lg text-slate-300 max-w-3xl mx-auto leading-relaxed mb-8 opacity-50">
              JobSpaceAI isn&apos;t a job board — we&apos;re your AI career
              coach. Get ATS-ready CVs, practise interviews, and win the jobs
              you find elsewhere.
            </p>

            {/* Skeleton CTAs */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <div className="inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-black bg-hero-yellow/50 rounded-xl opacity-50 animate-pulse">
                Get Started Free
              </div>
              <div className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border border-white/20 rounded-xl opacity-50 animate-pulse">
                Free ATS Check
              </div>
            </div>
          </div>

          {/* Skeleton stats */}
          <div className="flex justify-center">
            <div className="grid grid-cols-3 gap-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 opacity-50">
              {[1, 2, 3].map((i) => (
                <div key={i} className="text-center animate-pulse">
                  <div className="flex justify-center mb-2">
                    <div className="w-6 h-6 bg-hero-yellow/50 rounded" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">--</div>
                  <div className="text-sm text-slate-400">Loading...</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
});

export default function HeroWrapper() {
  return <HeroSection />;
}
