import React from 'react';
import { FileText } from 'lucide-react';
import type { ResumeAnalysisResultProps } from '../types/resumeTypes';

export const ResumeAnalysisResult: React.FC<ResumeAnalysisResultProps> = ({
  analysis
}) => {
  if (!analysis?.sections) return null;

  return (
    <div className="mt-8 p-6 bg-white rounded-lg shadow-md border border-gray-100">
      <h3 className="text-xl font-semibold mb-4 text-gray-800 flex items-center gap-2">
        <FileText className="w-5 h-5" />
        CV Analysis
      </h3>
      <div className="space-y-6">
        {analysis.sections.map((section, index) => (
          <div key={index} className="bg-gray-50 p-4 rounded-md">
            <h4 className="font-medium text-gray-700 mb-2">{section.title}</h4>
            <ul className="space-y-2">
              {Array.isArray(section.content) ? (
                section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="text-gray-600 ml-4 list-disc">
                    {typeof item === 'string' ? item : JSON.stringify(item)}
                  </li>
                ))
              ) : (
                <li className="text-gray-600 ml-4 list-disc">
                  {typeof section.content === 'string'
                    ? section.content
                    : JSON.stringify(section.content)}
                </li>
              )}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};
