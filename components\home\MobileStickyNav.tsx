// components/home/<USER>
'use client';

import React, { useEffect, useState } from 'react';
import { ArrowR<PERSON>, Sparkles, Target, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import { createClient } from '@/app/utils/supabase/client';
import { ThemedLinkButton } from '@/components/ui/themed-button';

interface MobileStickyNavProps {
  heroHeight?: number;
}

const MobileStickyNav: React.FC<MobileStickyNavProps> = ({
  heroHeight = 500
}) => {
  const [mounted, setMounted] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [ctaVariant, setCTAVariant] = useState(0);
  const [userId, setUserId] = useState<string | null>(null);

  // Check user authentication
  useEffect(() => {
    const supabase = createClient();

    const getUser = async () => {
      try {
        const {
          data: { user }
        } = await supabase.auth.getUser();
        setUserId(user?.id || null);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    getUser();
  }, []);

  // Rotate CTA variants every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCTAVariant((prev) => (prev + 1) % 3);
    }, 8000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    setMounted(true);

    const handleScroll = () => {
      const shouldShow = window.scrollY > heroHeight;
      if (shouldShow !== isVisible) {
        setIsVisible(shouldShow);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [heroHeight, isVisible]);

  if (!mounted) return null;

  // Smart CTA options based on user state
  const getCtaOptions = () => {
    if (userId) {
      // Authenticated user CTAs
      return [
        {
          text: 'Continue Analysis',
          subtext: 'View your dashboard',
          icon: <Target className="w-4 h-4" />,
          href: `/dashboard/${userId}`
        },
        {
          text: 'Start New Analysis',
          subtext: 'Fresh CV optimization',
          icon: <Sparkles className="w-4 h-4" />,
          href: '/onboarding'
        },
        {
          text: 'Quick ATS Check',
          subtext: 'Fast & free analysis',
          icon: <FileText className="w-4 h-4" />,
          href: '/ats-resume-checker'
        }
      ];
    } else {
      // Non-authenticated user CTAs
      return [
        {
          text: 'Start Free Analysis',
          subtext: 'Get instant ATS score',
          icon: <Target className="w-4 h-4" />,
          href: '/onboarding'
        },
        {
          text: 'Try ATS Checker',
          subtext: 'No signup required',
          icon: <FileText className="w-4 h-4" />,
          href: '/ats-resume-checker'
        },
        {
          text: 'Get Hired Faster',
          subtext: 'Join 10k+ success stories',
          icon: <ArrowRight className="w-4 h-4" />,
          href: '/onboarding'
        }
      ];
    }
  };

  const ctaOptions = getCtaOptions();
  const currentCTA = ctaOptions[ctaVariant];

  // Smart secondary actions based on user state
  const getSecondaryActions = () => {
    if (userId) {
      return [
        {
          href: '/onboarding',
          label: 'New Analysis',
          icon: <Target className="w-3 h-3" />
        },
        {
          href: '/ats-resume-checker',
          label: 'Quick Check',
          icon: <FileText className="w-3 h-3" />
        },
        {
          href: '/ai-interview-coach',
          label: 'Interview',
          icon: <Sparkles className="w-3 h-3" />
        }
      ];
    } else {
      return [
        {
          href: '/ats-resume-checker',
          label: 'Free Check',
          icon: <Target className="w-3 h-3" />
        },
        {
          href: '/onboarding',
          label: 'Get Started',
          icon: <FileText className="w-3 h-3" />
        },
        {
          href: '/articles',
          label: 'Tips',
          icon: <Sparkles className="w-3 h-3" />
        }
      ];
    }
  };

  const secondaryActions = getSecondaryActions();

  return (
    <>
      {/* Enhanced main floating CTA */}
      <div
        className={cn(
          'fixed bottom-4 left-4 right-4 z-50 transition-all duration-500 md:hidden',
          isVisible
            ? 'opacity-100 translate-y-0'
            : 'opacity-0 translate-y-8 pointer-events-none'
        )}
      >
        <div className="bg-gradient-to-r backdrop-blur-sm bg-hero-bg/95 border border-white/20 rounded-2xl p-4 shadow-2xl">
          <div className="flex items-center justify-between gap-3">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                {currentCTA.icon}
                <p className="text-white font-semibold text-sm leading-tight">
                  {currentCTA.text}
                </p>
              </div>
              <p className="text-slate-300 text-xs">{currentCTA.subtext}</p>
            </div>
            <ThemedLinkButton
              href={currentCTA.href}
              variant="primary"
              size="sm"
              className="min-w-fit px-4 py-3 text-sm font-bold"
              showArrow
              prefetch={false}
            >
              {currentCTA.text}
            </ThemedLinkButton>
          </div>
        </div>
      </div>

      {/* Secondary quick actions bar */}
      <div
        className={cn(
          'fixed bottom-24 left-4 right-4 z-40 transition-all duration-300 md:hidden',
          isVisible && window.scrollY > heroHeight + 200
            ? 'opacity-100 translate-y-0'
            : 'opacity-0 translate-y-4 pointer-events-none'
        )}
      >
        <div className="flex gap-2">
          {secondaryActions.map((action, index) => (
            <ThemedLinkButton
              key={index}
              href={action.href}
              variant="ghost"
              size="sm"
              className="flex-1 bg-white/10 backdrop-blur-sm border border-white/20 text-white 
                       px-3 py-2 text-xs font-medium hover:bg-white/20"
            >
              <span className="flex items-center justify-center gap-1">
                {action.icon}
                {action.label}
              </span>
            </ThemedLinkButton>
          ))}
        </div>
      </div>
    </>
  );
};

export default MobileStickyNav;
