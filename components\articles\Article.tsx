import React from 'react';
import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Article } from '@/app/types/globalTypes';
import { Clock, Headphones } from 'lucide-react';
import ArticleImage from '../ui/article-image';

const ArticleCard: React.FC<{ article: Article }> = ({ article }) => (
  <Link href={`/articles/${article.slug}`} className="block h-full">
    <Card className="overflow-hidden transition-all duration-300 group h-full flex flex-col cursor-pointer backdrop-blur-sm bg-white/5 border border-white/20 text-white">
      <div className="relative w-full" style={{ aspectRatio: '16/9' }}>
        <ArticleImage
          src={
            article.schema.image ||
            `/images/articles/placeholder-${article.slug.charAt(0)}.jpg`
          }
          alt={article.title}
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          fallbackText="Loading article..."
        />
        <div className="absolute top-2 right-2">
          {article.audio && (
            <Badge
              variant="secondary"
              className="bg-hero-yellow/80 text-[#111827]"
            >
              <Headphones size={14} className="mr-1" /> Audio
            </Badge>
          )}
        </div>
      </div>
      <CardHeader className="pb-2 flex-grow min-h-[6.5rem]">
        <CardTitle className="text-lg font-bold mb-3 line-clamp-2 transition-colors duration-300 group-hover:text-hero-yellow">
          {article.title}
        </CardTitle>
        <div className="flex flex-col space-y-1 text-xs text-slate-300">
          <div className="flex items-center space-x-2">
            <span className="truncate flex-1">{article.author}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="flex items-center whitespace-nowrap">
              <Clock size={14} className="mr-1 flex-shrink-0" />
              {article.readingTime}
            </span>
            <span>•</span>
            <span className="truncate">
              {new Date(
                article.date || article.schema.datePublished
              ).toLocaleDateString(undefined, {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex flex-col justify-between pt-2">
        <p className="text-slate-200 mb-4 line-clamp-2 transition-colors duration-300 group-hover:text-white">
          {article.metaDescription}
        </p>
        <div className="flex flex-wrap gap-2">
          {article.tags?.slice(0, 3).map((tag) => (
            <Badge
              key={tag}
              variant="secondary"
              className="text-xs transition-colors duration-300 bg-white/10 text-slate-200 group-hover:bg-hero-yellow/20 group-hover:text-hero-yellow"
            >
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  </Link>
);

export default ArticleCard;
