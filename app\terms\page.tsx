import React from 'react';
import { Metadata } from 'next';
import { <PERSON>, CardH<PERSON>er, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import {
  Check<PERSON>ircle,
  Book,
  Shield,
  AlertTriangle,
  RefreshCw,
  Sparkles
} from 'lucide-react';
import { constructMetadata } from '@/lib/seo-config';

export const metadata: Metadata = constructMetadata({
  title: 'Terms of Service | Job Space AI',
  description:
    'Read our Terms of Service to understand the conditions for using Job Space AI&apos;s services.',
  path: '/terms'
});

const TermsSection = ({
  icon,
  title,
  children
}: {
  icon: React.ReactNode;
  title: string;
  children: React.ReactNode;
}) => (
  <Card className="mb-6 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg text-white overflow-hidden">
    <CardHeader>
      <CardTitle className="flex items-center text-xl font-semibold text-white">
        {icon}
        <span className="ml-2">{title}</span>
      </CardTitle>
    </CardHeader>
    <CardContent className="text-slate-200">{children}</CardContent>
  </Card>
);

export default function TermsOfServicePage() {
  return (
    <div className="relative isolate min-h-screen overflow-hidden">
      {/* Background with animated blobs */}
      <div className="absolute inset-0 -z-10 pointer-events-none">
        {/* Subtle background features */}
        <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-50"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000 opacity-50"></div>
        <div className="absolute top-2/3 left-1/2 w-[550px] h-[550px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000 opacity-50"></div>
        <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000 opacity-50"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-16 max-w-[1200px]">
        <div className="text-center mb-12">
          <span className="text-yellow-300 font-semibold inline-flex items-center gap-2">
            <Sparkles className="w-4 h-4" /> LEGAL INFORMATION
          </span>
          <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
            Terms of{' '}
            <span className="text-yellow-400 text-shadow-yellow">Service</span>
          </h1>
          <p className="text-slate-200 max-w-2xl mx-auto text-[16px] md:text-[18px] mb-8">
            Welcome to Job Space AI. By using our services, you agree to comply
            with and be bound by the following terms and conditions. Please read
            these Terms of Service carefully before accessing or using our
            platform.
          </p>
        </div>

        <TermsSection
          icon={<CheckCircle className="w-6 h-6" />}
          title="1. Acceptance of Terms"
        >
          <p>
            By accessing or using Job Space AI, you agree to be bound by these
            Terms of Service and all applicable laws and regulations. If you do
            not agree with any part of these terms, you may not use our
            services.
          </p>
        </TermsSection>

        <TermsSection
          icon={<Book className="w-6 h-6" />}
          title="2. Use of Services"
        >
          <p className="mb-4">
            You agree to use our services only for lawful purposes and in
            accordance with these Terms. You are responsible for maintaining the
            confidentiality of your account information.
          </p>
          <p>As a user, you agree not to:</p>
          <ul className="list-disc pl-6">
            <li>
              Use the service for any illegal purpose or in violation of any
              local, state, national, or international law
            </li>
            <li>
              Violate or encourage others to violate the rights of third
              parties, including intellectual property rights
            </li>
            <li>Upload or transmit viruses or other malicious code</li>
            <li>
              Attempt to gain unauthorized access to our systems or networks
            </li>
          </ul>
        </TermsSection>

        <TermsSection
          icon={<Shield className="w-6 h-6" />}
          title="3. Intellectual Property"
        >
          <p className="mb-4">
            All content and materials available on Job Space AI are the property
            of Job Space AI or its licensors and are protected by copyright,
            trademark, and other intellectual property laws.
          </p>
          <p>You may not:</p>
          <ul className="list-disc pl-6">
            <li>
              Reproduce, distribute, or publicly display any content from our
              platform without prior written consent
            </li>
            <li>
              Modify or create derivative works based on our content or services
            </li>
            <li>
              Use any of our trademarks, service marks, or logos without express
              permission
            </li>
          </ul>
        </TermsSection>

        <TermsSection
          icon={<AlertTriangle className="w-6 h-6" />}
          title="4. Limitation of Liability"
        >
          <p className="mb-4">
            Job Space AI shall not be liable for any indirect, incidental,
            special, consequential, or punitive damages resulting from your use
            of or inability to use our services.
          </p>
          <p>
            We do not guarantee the accuracy, completeness, or usefulness of any
            information on the platform or that it will meet your specific
            requirements.
          </p>
        </TermsSection>

        <TermsSection
          icon={<RefreshCw className="w-6 h-6" />}
          title="5. Changes to Terms"
        >
          <p>
            We reserve the right to modify these Terms of Service at any time.
            Your continued use of our services after any changes indicates your
            acceptance of the modified terms. We will make reasonable efforts to
            notify you of any significant changes.
          </p>
        </TermsSection>

        <Accordion
          type="single"
          collapsible
          className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
        >
          <AccordionItem value="termination" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              Termination
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              We may terminate or suspend your account and access to our
              services immediately, without prior notice or liability, for any
              reason, including if you breach the Terms of Service.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="governing-law" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              Governing Law
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              These Terms shall be governed by and construed in accordance with
              the laws of the State of California, without regard to its
              conflict of law provisions.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="contact" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              Contact Us
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              If you have any questions about these Terms of Service, please
              contact us at:
              <br />
              <br />
              Email:{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
              >
                <EMAIL>
              </a>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <p className="text-sm text-slate-400 text-center">
          Last Updated: October 3, 2024
        </p>
      </div>
    </div>
  );
}
