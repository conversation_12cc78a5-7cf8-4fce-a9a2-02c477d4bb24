import React from 'react';
import { MarketData } from '@/app/types/globalTypes';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { JobGrowthChart } from './JobGrowthChart';
import { SalaryTrendsChart } from './SalaryTrendsChart';
import { SkillDemandChart } from './SkillDemandChart';
import { IndustryBreakdownChart } from './IndustryBreakdownChart';

interface OverviewTabProps {
  marketData: MarketData;
}

export function OverviewTab({ marketData }: Readonly<OverviewTabProps>) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <CardTitle>Job Growth</CardTitle>
        </CardHeader>
        <CardContent>
          <JobGrowthChart jobGrowthData={marketData.jobGrowth} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Salary Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <SalaryTrendsChart salaryTrends={marketData.salaryTrends} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Top Skills in Demand</CardTitle>
        </CardHeader>
        <CardContent>
          <SkillDemandChart skillDemand={marketData.skillDemand} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Industry Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <IndustryBreakdownChart data={marketData.industryBreakdown} />
        </CardContent>
      </Card>
    </div>
  );
}
