'use client';

import dynamic from 'next/dynamic';
import { User } from '@supabase/supabase-js';
import { Resume, JobAnalysisResult, Job } from '@/app/types/globalTypes';
import { useState } from 'react';
import { ClientOnly } from '@/components/application-funnel/ClientOnly';
import { Loader2 } from 'lucide-react';

const DynamicApplicationFunnel = dynamic(
  () => import('@/components/application-funnel/ApplicationFunnel'),
  {
    loading: () => (
      <div className="flex flex-col items-center justify-center min-h-screen">
        {/* Using global background from LayoutWrapper instead of duplicating here */}

        {/* Loading spinner with glow effect */}
        <div className="relative mb-6">
          <div className="absolute inset-0 rounded-full bg-hero-yellow/20 blur-xl animate-pulse"></div>
          <Loader2 className="w-12 h-12 text-[hsl(var(--hero-yellow))] animate-spin relative z-10" />
        </div>

        <p className="text-center text-lg font-medium text-white mb-2">
          Loading Analysis Tools
        </p>
        <p className="text-center text-sm text-slate-300 max-w-md px-4">
          Please wait while we prepare your analysis environment...
        </p>
      </div>
    )
  }
);

interface ApplicationFunnelClientProps {
  user: User;
  initialResume: Resume | null;
  saveJob: (
    jobDescription: string,
    jobLink: string,
    analysisResult: JobAnalysisResult
  ) => Promise<Job | null>;
}

export default function ApplicationFunnelClient({
  user,
  initialResume,
  saveJob
}: Readonly<ApplicationFunnelClientProps>) {
  const [isLoading, setIsLoading] = useState(false);

  // ApplicationFunnelClient.tsx
  const onSubmit = async (job: Job): Promise<Job> => {
    setIsLoading(true);

    try {
      if (!job.analysis_result) {
        throw new Error('Analysis result is missing');
      }

      const savedJob = await saveJob(
        job.description,
        job.job_link,
        job.analysis_result
      );

      if (!savedJob) {
        throw new Error('Failed to save job');
      }

      return savedJob; // ✅ satisfy the Promise<Job> contract
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ClientOnly
      fallback={
        <div className="flex items-center justify-center">
          <div className="relative">
            <div className="absolute inset-0 rounded-full bg-hero-yellow/20 blur-xl animate-pulse"></div>
            <Loader2 className="h-12 w-12 text-[hsl(var(--hero-yellow))] animate-spin relative z-10" />
          </div>
        </div>
      }
    >
      <DynamicApplicationFunnel
        user={user}
        resume={initialResume}
        isLoading={isLoading}
        onSubmit={onSubmit}
      />
    </ClientOnly>
  );
}
