import Link from 'next/link';

const Footer = () => (
  <footer className="relative isolate py-16 overflow-hidden border-t border-white/10">
    <div className="relative z-10 mx-auto w-full max-w-7xl px-6 lg:px-8">
      {/* Main Footer Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-12 lg:gap-16 pb-16">
        {/* Company Info - Takes up 2 columns on large screens */}
        <div className="lg:col-span-2 text-center lg:text-left">
          <h3 className="font-roboto-condensed font-bold text-2xl mb-6 text-hero-yellow">
            Job Space AI
          </h3>
          <p className="text-base text-slate-200 leading-relaxed mb-6 max-w-md mx-auto lg:mx-0">
            Empowering your career journey with AI-powered tools designed for
            the UK job market.
          </p>
          <div className="text-sm text-slate-300 space-y-1">
            <p>A trading name of NRB CONSULTING LTD</p>
            <p>Registered in England and Wales</p>
            <p>Company Number: 16375535</p>
          </div>
        </div>

        {/* UK CV Tools by City */}
        <div className="text-center lg:text-left">
          <h3 className="font-roboto-condensed font-bold text-lg mb-6 text-hero-yellow">
            CV Tools by City
          </h3>
          <nav className="flex flex-col space-y-3">
            <Link
              href="/cv-checker-uk"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              CV Checker London
            </Link>
            <Link
              href="/cv-checker-uk"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              CV Checker Manchester
            </Link>
            <Link
              href="/cv-checker-uk"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              CV Checker Birmingham
            </Link>
            <Link
              href="/ats-resume-checker"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              ATS Scanner UK
            </Link>
          </nav>
        </div>

        {/* Legal & Support */}
        <div className="text-center lg:text-left">
          <h3 className="font-roboto-condensed font-bold text-lg mb-6 text-hero-yellow">
            Support &amp; Legal
          </h3>
          <nav className="flex flex-col space-y-3">
            <Link
              href="/about"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              About Us
            </Link>
            <Link
              href="/contact"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              Contact
            </Link>
            <Link
              href="/privacy"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              Terms of Service
            </Link>
            <Link
              href="/cookies"
              className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 ease-in-out"
            >
              Cookie Policy
            </Link>
          </nav>
        </div>
      </div>

      {/* AI Career Tools 2025 Section */}
      <section className="border-t border-white/10 pt-12 pb-12">
        <div className="text-center mb-10">
          <h4 className="font-roboto-condensed font-bold text-xl text-hero-yellow mb-3">
            AI Career Tools 2025
          </h4>
          <p className="text-sm text-slate-300 max-w-2xl mx-auto">
            Comprehensive suite of AI-powered tools to accelerate your career
            success
          </p>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
          <Link
            href="/ai-cv-builder"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              AI CV Builder UK
            </span>
          </Link>
          <Link
            href="/ai-cover-letter"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              AI Cover Letter Generator
            </span>
          </Link>
          <Link
            href="/ai-interview-coach"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              AI Interview Coach
            </span>
          </Link>
          <Link
            href="/what-is-ats-uk"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              What is ATS? UK Guide
            </span>
          </Link>
          <Link
            href="/platform"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              All-in-One Job Platform UK
            </span>
          </Link>
          <Link
            href="/ats-resume-checker"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              Scan CV for London Job
            </span>
          </Link>
          <Link
            href="/ai-cv-builder"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              Manchester Job CV Checker
            </span>
          </Link>
          <Link
            href="/ats-resume-checker"
            className="group text-center p-4 rounded-lg border border-white/5 hover:border-hero-yellow/30 transition-all duration-200 hover:bg-white/5"
          >
            <span className="text-sm text-slate-200 group-hover:text-hero-yellow transition-colors duration-200">
              Birmingham CV Analysis
            </span>
          </Link>
        </div>
      </section>

      {/* Popular Job Search Tools */}
      <div className="border-t border-white/10 pt-12 pb-12">
        <div className="text-center mb-10">
          <h4 className="font-roboto-condensed font-bold text-xl text-hero-yellow mb-3">
            Popular Job Search Tools
          </h4>
          <p className="text-sm text-slate-300">
            Most used tools by UK job seekers
          </p>
        </div>
        <div className="flex flex-wrap justify-center gap-8 max-w-6xl mx-auto">
          <Link
            href="/ats-resume-checker"
            className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 px-3 py-2 rounded-md hover:bg-white/5"
          >
            Scan CV for London Job
          </Link>
          <Link
            href="/ai-cv-builder"
            className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 px-3 py-2 rounded-md hover:bg-white/5"
          >
            Manchester Job CV Checker
          </Link>
          <Link
            href="/ats-resume-checker"
            className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 px-3 py-2 rounded-md hover:bg-white/5"
          >
            CV Scan UK
          </Link>
          <Link
            href="/ats-resume-checker"
            className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 px-3 py-2 rounded-md hover:bg-white/5"
          >
            Birmingham CV Analysis
          </Link>
          <Link
            href="/articles"
            className="text-sm text-slate-200 hover:text-hero-yellow hover:underline transition-all duration-200 px-3 py-2 rounded-md hover:bg-white/5"
          >
            UK Job Tips 2025
          </Link>
        </div>
      </div>

      {/* Copyright */}
      <div className="border-t border-white/10 pt-8 text-center">
        <p className="text-sm text-slate-400">
          &copy; 2025 Job Space AI - All Rights Reserved
        </p>
      </div>
    </div>
  </footer>
);

export default Footer;
