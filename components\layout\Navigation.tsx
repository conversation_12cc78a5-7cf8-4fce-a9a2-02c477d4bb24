'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { createClient } from '@/app/utils/supabase/client';
import { User } from '@supabase/supabase-js';
import { Menu } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useToast } from '@/hooks/use-toast';
import { Button } from '../ui/button';
import { ThemedButton } from '../ui/themed-button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet';

import { signOut } from '@/app/actions/auth';

const Navigation = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const supabase = createClient();
  const { toast } = useToast();

  // Function to check if a link is active
  const isLinkActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    // Special case for dashboard which has dynamic route
    if (href.includes('/dashboard/') && pathname?.startsWith('/dashboard/')) {
      return true;
    }
    return pathname === href || pathname?.startsWith(`${href}/`);
  };

  const forceSessionRefresh = useCallback(async () => {
    try {
      setLoading(true);
      console.log('Forcing session refresh...');

      // First try to refresh the session
      const { error: refreshError } = await supabase.auth.refreshSession();

      if (refreshError) {
        console.error('Error refreshing session:', refreshError);
      }

      // Then get the latest session regardless of refresh outcome
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error getting session after refresh:', error);
        setUser(null);
        return;
      }

      if (data?.session) {
        console.log('Session refreshed, user:', data.session.user.email);
        setUser(data.session.user);
      } else {
        console.log('No active session found after refresh');
        setUser(null);
      }
    } catch (e) {
      console.error('Unexpected error refreshing session:', e);
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [supabase.auth]);

  // Define checkSession as a useCallback to avoid recreation on each render
  const checkSession = useCallback(async () => {
    try {
      setLoading(true);
      console.log('Checking session...');

      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error checking session:', error);
        setUser(null);
        return;
      }

      if (data?.session) {
        console.log('Session found, user:', data.session.user.email);
        setUser(data.session.user);
      } else {
        console.log('No active session found');
        setUser(null);
      }
    } catch (e) {
      console.error('Unexpected error checking session:', e);
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [supabase.auth]);

  const handleStorageChange = useCallback(
    (event: StorageEvent) => {
      if (
        event.key?.includes('supabase.auth') ||
        event.key?.includes('sb-') ||
        event.key === null // localStorage was cleared
      ) {
        console.log('Auth-related storage change detected:', event.key);
        checkSession();
      }
    },
    [checkSession]
  );

  useEffect(() => {
    // Check session on component mount
    checkSession();

    // Set up auth state change listener
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event);

      if (session) {
        console.log('Session in auth state change:', session.user.email);
        setUser(session.user);
      } else {
        setUser(null);
      }

      if (
        ['SIGNED_IN', 'SIGNED_OUT', 'TOKEN_REFRESHED', 'USER_UPDATED'].includes(
          event
        )
      ) {
        checkSession();
      }
    });

    // Check for session when tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Page is becoming visible again - might be restored from bfcache
        checkSession();
      } else if (document.visibilityState === 'hidden') {
        // Page is being hidden - might go into bfcache
        // Clean up any subscriptions that might prevent bfcache
        subscription.unsubscribe();
      }
    };

    // Handle page show event (when page is restored from bfcache)
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        // Page was restored from bfcache
        console.log('Page restored from bfcache, refreshing session');
        forceSessionRefresh();
      }
    };

    // Check session when the component mounts or when the window gets focus
    const searchParams = new URLSearchParams(window.location.search);
    if (
      searchParams.has('status') &&
      (searchParams.get('status')?.includes('Success') ||
        searchParams.get('status')?.includes('success'))
    ) {
      console.log(
        'Detected successful auth via URL params, refreshing session'
      );
      forceSessionRefresh();

      // Clear status params from URL to prevent repeated checks
      const cleanUrl = window.location.pathname;
      window.history.replaceState({}, document.title, cleanUrl);
    }

    // Also check for any auth hints in localStorage
    const authHint = localStorage.getItem('auth_state_changed');
    if (authHint) {
      console.log('Detected auth state change hint in localStorage');
      forceSessionRefresh();
      localStorage.removeItem('auth_state_changed');
    }

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('pageshow', handlePageShow);

    return () => {
      subscription.unsubscribe();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('pageshow', handlePageShow);
    };
  }, [checkSession, forceSessionRefresh, handleStorageChange, supabase.auth]);

  const handleSignIn = () => {
    // Store current path for redirect after login
    const currentPath = window.location.pathname + window.location.search;
    const nextParam = encodeURIComponent(currentPath);

    // Mark that we're trying to log in (for session detection later)
    localStorage.setItem('auth_redirect_pending', 'true');

    // Navigate to login page
    router.push(`/signin/password_signin?next=${nextParam}`);
    setOpen(false);
  };

  const handleSignOut = async () => {
    try {
      setLoading(true);

      // Clear all application state from localStorage
      localStorage.clear();

      // Sign out with Supabase client first to clear client-side auth state
      await supabase.auth.signOut();

      // Clear user state
      setUser(null);

      // Try the server action to clear cookies
      try {
        const redirectUrl = await signOut();
        console.log(
          'Server-side logout successful, redirecting to:',
          redirectUrl
        );

        // Success message
        toast({
          title: 'Signed Out',
          description: 'You have been successfully logged out.'
        });

        // Force page refresh to clear any cached state
        window.location.href = redirectUrl || '/';
      } catch (serverError) {
        console.error('Error with server-side logout:', serverError);

        // We've already done client-side logout, just redirect
        toast({
          title: 'Signed Out',
          description: 'You have been successfully logged out.'
        });
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Error with client-side logout:', error);

      // Try server-side logout as fallback
      try {
        const redirectUrl = await signOut();
        toast({
          title: 'Signed Out',
          description: 'You have been successfully logged out.'
        });
        window.location.href = redirectUrl || '/';
      } catch (fallbackError) {
        console.error('Fallback logout also failed:', fallbackError);
        toast({
          title: 'Error',
          description: 'Failed to sign out. Please try again.',
          variant: 'destructive'
        });
      }
    } finally {
      setLoading(false);
      setOpen(false);
    }
  };

  const handleNavigation = (path: string) => {
    // Simply navigate to the path - loading.tsx will handle loading states
    router.push(path);
    setOpen(false);
  };

  const UserMenu = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="p-0 h-10 w-10 rounded-full"
        >
          <Avatar>
            <AvatarImage
              src={user?.user_metadata?.avatar_url}
              alt={`Profile picture for ${user?.email || 'user'}`}
            />
            <AvatarFallback>
              {user?.email ? user.email.charAt(0).toUpperCase() : 'U'}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {user?.email && (
          <div className="px-2 py-1.5 text-sm text-muted-foreground break-all">
            {user.email}
          </div>
        )}
        {user?.id && (
          <DropdownMenuItem
            onSelect={() => handleNavigation(`/dashboard/${user.id}`)}
            className="hover:text-[hsl(var(--hero-yellow))] cursor-pointer group"
          >
            <span className="group-hover:text-[hsl(var(--hero-yellow))]">
              Dashboard
            </span>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem
          onSelect={handleSignOut}
          className="hover:text-[hsl(var(--hero-yellow))] cursor-pointer group"
        >
          <span className="group-hover:text-[hsl(var(--hero-yellow))]">
            Sign Out
          </span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  const funnelText = user ? 'Start Analysis' : 'Get Started';

  return (
    <>
      <Sheet open={open} onOpenChange={setOpen}>
        <nav
          className="flex items-center justify-between px-4 py-3 w-full overflow-x-clip relative transition-all duration-300 h-16 min-h-16 max-h-16"
          style={{ height: '64px' }}
        >
          {/* Left side - Menu button on mobile, Logo on desktop */}
          <div className="flex items-center gap-2 min-w-0 md:flex-1 z-10">
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                aria-label="Open menu"
              >
                <Menu />
              </Button>
            </SheetTrigger>
            <Link
              href="/"
              className="hidden md:flex items-center gap-2 min-w-0"
            >
              <Image
                src="/images/logo_light.png"
                alt="Job Space AI Logo"
                width={32}
                height={32}
                className="w-6 h-6 sm:w-8 sm:h-8 shrink-0"
              />
              <span className="text-xl font-bold truncate">Job Space AI</span>
            </Link>
          </div>

          {/* Center - Logo for mobile only */}
          <div className="absolute left-1/2 transform -translate-x-1/2 md:hidden">
            <Link href="/" className="flex items-center gap-2">
              <Image
                src="/images/logo_light.png"
                alt="Job Space AI Logo"
                width={24}
                height={24}
                className="w-6 h-6 shrink-0"
              />
              <span className="text-xl font-bold">Job Space AI</span>
            </Link>
          </div>

          {/* Right side - Navigation links on desktop, Login/Avatar on both */}
          <div className="flex items-center gap-6 shrink-0 md:flex-1 md:justify-end z-10">
            <div className="hidden md:flex gap-6">
              <Link
                href="/application-funnel"
                className={`relative transition-colors duration-200
                after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300
                ${
                  isLinkActive('/application-funnel')
                    ? 'text-primary after:w-full'
                    : 'text-foreground hover:text-primary hover:after:w-full after:w-0'
                }
                `}
              >
                {funnelText}
              </Link>
              <Link
                href="/about"
                className={`relative transition-colors duration-200
                after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300
                ${
                  isLinkActive('/about')
                    ? 'text-primary after:w-full'
                    : 'text-foreground hover:text-primary hover:after:w-full after:w-0'
                }
                `}
              >
                About Us
              </Link>
              <Link
                href="/articles"
                className={`relative transition-colors duration-200
                after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300
                ${
                  isLinkActive('/articles')
                    ? 'text-primary after:w-full'
                    : 'text-foreground hover:text-primary hover:after:w-full after:w-0'
                }
                `}
              >
                Articles
              </Link>
              <Link
                href="/credits"
                className={`relative transition-colors duration-200
                after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300
                ${
                  isLinkActive('/credits')
                    ? 'text-primary after:w-full'
                    : 'text-foreground hover:text-primary hover:after:w-full after:w-0'
                }
                `}
              >
                Pricing
              </Link>
              <Link
                href="/score-gauge-demo"
                className={`relative transition-colors duration-200
                after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300
                ${
                  isLinkActive('/score-gauge-demo')
                    ? 'text-primary after:w-full'
                    : 'text-foreground hover:text-primary hover:after:w-full after:w-0'
                }
                `}
              >
                Score Demo
              </Link>
              {user && user.id && (
                <button
                  onClick={() => handleNavigation(`/dashboard/${user.id}`)}
                  className={`relative transition-colors duration-200
                  after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300
                  ${
                    isLinkActive(`/dashboard/${user.id}`)
                      ? 'text-primary after:w-full'
                      : 'text-foreground hover:text-primary hover:after:w-full after:w-0'
                  }
                  `}
                >
                  Dashboard
                </button>
              )}
            </div>
            {loading ? (
              <Button variant="ghost" disabled>
                Loading...
              </Button>
            ) : user ? (
              <UserMenu />
            ) : (
              <ThemedButton onClick={handleSignIn} variant="primary" size="sm">
                Login here
              </ThemedButton>
            )}
          </div>
        </nav>

        <SheetContent
          side="left"
          className="w-[240px] sm:w-[280px] bg-primary text-primary-foreground"
        >
          <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
          <nav className="flex flex-col space-y-4 mt-8">
            {/* User info section for mobile */}
            {user && (
              <div className="flex items-center gap-3 pb-4 mb-4 border-b border-primary-foreground/20">
                <Avatar className="h-10 w-10">
                  <AvatarImage
                    src={user.user_metadata?.avatar_url}
                    alt={`Profile picture for ${user.email || 'user'}`}
                  />
                  <AvatarFallback>
                    {user.email ? user.email.charAt(0).toUpperCase() : 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col">
                  <span className="text-sm font-medium">{user.email}</span>
                  <span className="text-xs text-primary-foreground/70">
                    Signed In
                  </span>
                </div>
              </div>
            )}

            {/* Navigation Links */}
            <button
              onClick={() => handleNavigation('/')}
              className={`text-lg text-left transition-colors ${
                isLinkActive('/')
                  ? 'text-[hsl(var(--hero-yellow))]'
                  : 'hover:text-[hsl(var(--hero-yellow))]'
              }`}
            >
              Home
            </button>
            <button
              onClick={() => handleNavigation('/application-funnel')}
              className={`text-lg text-left transition-colors ${
                isLinkActive('/application-funnel')
                  ? 'text-[hsl(var(--hero-yellow))]'
                  : 'hover:text-[hsl(var(--hero-yellow))]'
              }`}
            >
              {funnelText}
            </button>
            <button
              onClick={() => handleNavigation('/about')}
              className={`text-lg text-left transition-colors ${
                isLinkActive('/about')
                  ? 'text-[hsl(var(--hero-yellow))]'
                  : 'hover:text-[hsl(var(--hero-yellow))]'
              }`}
            >
              About Us
            </button>
            <button
              onClick={() => handleNavigation('/articles')}
              className={`text-lg text-left transition-colors ${
                isLinkActive('/articles')
                  ? 'text-[hsl(var(--hero-yellow))]'
                  : 'hover:text-[hsl(var(--hero-yellow))]'
              }`}
            >
              Articles
            </button>
            <button
              onClick={() => handleNavigation('/credits')}
              className={`text-lg text-left transition-colors ${
                isLinkActive('/credits')
                  ? 'text-[hsl(var(--hero-yellow))]'
                  : 'hover:text-[hsl(var(--hero-yellow))]'
              }`}
            >
              Pricing
            </button>
            <button
              onClick={() => handleNavigation('/score-gauge-demo')}
              className={`text-lg text-left transition-colors ${
                isLinkActive('/score-gauge-demo')
                  ? 'text-[hsl(var(--hero-yellow))]'
                  : 'hover:text-[hsl(var(--hero-yellow))]'
              }`}
            >
              Score Demo
            </button>

            {/* User-specific actions */}
            {user ? (
              <>
                {user.id && (
                  <button
                    onClick={() => handleNavigation(`/dashboard/${user.id}`)}
                    className={`text-lg text-left transition-colors ${
                      isLinkActive(`/dashboard/${user.id}`)
                        ? 'text-[hsl(var(--hero-yellow))]'
                        : 'hover:text-[hsl(var(--hero-yellow))]'
                    }`}
                  >
                    Dashboard
                  </button>
                )}
                <button
                  onClick={handleSignOut}
                  className="text-lg hover:text-[hsl(var(--hero-yellow))] text-left text-red-400"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <button
                onClick={handleSignIn}
                className="text-lg hover:text-[hsl(var(--hero-yellow))] text-left"
              >
                Sign In
              </button>
            )}
          </nav>
        </SheetContent>
      </Sheet>
    </>
  );
};

export default Navigation;
