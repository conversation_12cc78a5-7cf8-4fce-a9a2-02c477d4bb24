// app/dashboard/[id]/DashboardSystem.tsx
'use client';

import React from 'react';

// ONLY import dashboard-specific contexts and providers HERE
// This prevents them from being loaded on other routes
import { DashboardProvider } from '@/components/dashboard/DashboardContext';
import { FeatureProvider } from '@/components/shared/FeatureContext';

// Import the actual dashboard component
import Dashboard from '@/components/dashboard/Dashboard';

// Types
import type { Resume, Job, FeatureProps } from 'app/types/globalTypes';
import type { PostgrestError } from '@supabase/supabase-js';

export interface DashboardSystemProps {
  userId: string;
  initialFeatures: FeatureProps[];
  initialResumes: Resume[];
  initialJobs: Job[];
  initialError: PostgrestError | null;
  initialCredits: number;
  dashboardId?: string;
}

// This component wraps the dashboard with ALL its heavy dependencies
// It's only loaded when the user actually visits a dashboard route
const DashboardSystem: React.FC<DashboardSystemProps> = (props) => {
  return (
    <DashboardProvider {...props}>
      <FeatureProvider>
        <Dashboard {...props} />
      </FeatureProvider>
    </DashboardProvider>
  );
};

export default DashboardSystem;
