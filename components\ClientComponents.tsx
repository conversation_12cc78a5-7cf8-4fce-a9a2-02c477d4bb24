// components/ClientComponents.tsx
'use client';

import { ReactNode, useEffect, useState, ComponentType } from 'react';
import dynamic from 'next/dynamic';
import { Toaster } from './ui/toaster';

// ✅ CORE COMPONENTS - Load immediately
import { ConsentProvider } from '@/hooks/useConsentManager';
import { ConsentBanner } from '@/components/consent/ConsentBanner';

// ✅ Component type definitions for dynamic imports
type DynamicComponentType = ComponentType<Record<string, unknown>>;

// ✅ ESSENTIAL COMPONENTS - Dynamic loading
const SessionRefresher = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/SessionRefresher'),
  {
    ssr: false,
    loading: () => null
  }
);

const WebLockReleaser = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/components/WebLockReleaser'),
  {
    ssr: false,
    loading: () => null
  }
);

// ✅ ANALYTICS COMPONENTS - Using our new consolidated system
const TrafficSourceTracker = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/analytics/TrafficSourceTracker'),
  {
    ssr: false,
    loading: () => null
  }
);

const SignupConversionTracker = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/analytics/SignupConversionTracker').then((mod) => ({
      default: mod.SignupConversionTracker
    })),
  {
    ssr: false,
    loading: () => null
  }
);

// ✅ OPTIONAL COMPONENTS - Only load in development
const DevAnalyticsDebugger = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/analytics/DevAnalyticsDebugger').catch(() => ({
      default: () => null // Graceful fallback if component doesn't exist
    })),
  {
    ssr: false,
    loading: () => null
  }
);

interface ClientComponentsWrapperProps {
  children: ReactNode;
  gaId?: string;
}

interface ComponentState {
  isClient: boolean;
  analyticsReady: boolean;
}

export function ClientComponentsWrapper({
  children,
  gaId
}: ClientComponentsWrapperProps): JSX.Element {
  const [state, setState] = useState<ComponentState>({
    isClient: false,
    analyticsReady: false
  });

  // ✅ Ensure client-side rendering
  useEffect(() => {
    setState((prev) => ({ ...prev, isClient: true }));
    console.log('🔧 ClientComponentsWrapper mounted on client');
  }, []);

  // ✅ Simple analytics readiness check
  useEffect(() => {
    if (!state.isClient) return;

    // Analytics is considered ready when we have analytics tools OR after a timeout
    const hasAnalytics = !!(gaId || process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID);

    if (hasAnalytics) {
      // Wait a moment for analytics to initialize
      const timer = setTimeout(() => {
        setState((prev) => ({ ...prev, analyticsReady: true }));
        console.log('📊 Analytics ready for tracking components');
      }, 1000);

      // Cleanup function
      return () => clearTimeout(timer);
    } else {
      // No analytics configured, mark as ready immediately
      setState((prev) => ({ ...prev, analyticsReady: true }));
    }
  }, [state.isClient, gaId]);

  // ✅ Don't render anything until client-side hydration
  if (!state.isClient) {
    return <>{children}</>;
  }

  return (
    <ConsentProvider>
      <div>
        {children}

        {/* ✅ ESSENTIAL COMPONENTS - Always load */}
        <WebLockReleaser />
        <SessionRefresher />
        <Toaster />

        {/* ✅ ANALYTICS TRACKING - Load after analytics is ready */}
        {state.analyticsReady && (
          <>
            {/* Traffic source tracking - only if Clarity is configured */}
            {process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID && (
              <TrafficSourceTracker />
            )}

            {/* Conversion tracking - only if GA is configured */}
            {gaId && <SignupConversionTracker />}

            {/* Development debugging - only in dev mode */}
            {process.env.NODE_ENV === 'development' && gaId && (
              <DevAnalyticsDebugger />
            )}
          </>
        )}

        {/* ✅ CONSENT BANNER - Always last */}
        <ConsentBanner />
      </div>
    </ConsentProvider>
  );
}

// ✅ Export types for other components to use
export type { ClientComponentsWrapperProps };
