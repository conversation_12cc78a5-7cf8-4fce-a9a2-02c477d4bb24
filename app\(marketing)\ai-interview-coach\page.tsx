export const dynamic = 'force-static';
export const revalidate = 86400; // 24 h

import type { Metadata } from 'next';
import JsonLd from '@/components/seo/JsonLd';
import { faqLd, FAQItem } from 'lib/faq';
import { ThemedLink as Link } from 'components/ui/Link';
import Image from 'next/image';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from 'components/ui/accordion';
import { CallToAction } from 'components/CallToAction';

export const metadata: Metadata = {
  title: 'AI Mock Interview Practice UK | Free Tool 2025',
  description:
    'Practice real-time mock interviews with AI. Get teleprompter prompts, pacing analysis, and feedback for UK job interviews.',
  alternates: {
    canonical: '/ai-interview-coach'
  },
  openGraph: {
    /* existing OG tags */
  },
  twitter: {
    /* existing Twitter tags */
  }
};

const faqs: FAQItem[] = [
  {
    question: 'How does real-time feedback work?',
    answer:
      'Our AI listens to your answers during mock interviews, providing instant transcription, filler word counts, and pacing analysis to help you improve on the spot.'
  },
  {
    question: 'How does the AI interview coach work?',
    answer:
      'It simulates real interview questions and provides feedback to improve your answers and confidence.'
  },
  {
    question: 'Can I practice for specific job roles?',
    answer:
      'Yes, the coach tailors questions based on the job role and industry you are targeting.'
  },
  {
    question: 'Is my data private?',
    answer:
      'Absolutely. Your practice sessions and data are kept confidential and secure.'
  }
];

const sections = [
  { id: 'how-it-works', h2: 'How it works' },
  { id: 'key-features', h2: 'Key features' },
  { id: 'benefits', h2: 'Benefits at a glance' },
  { id: 'faqs', h2: 'Frequently asked questions' }
];

export default function AIInterviewCoach() {
  return (
    <>
      {/* FAQ JSON-LD for FAQPage */}
      <JsonLd id="faq-schema" data={faqLd('/ai-interview-coach', faqs)} />

      {/* HowTo JSON-LD for How it works */}
      <JsonLd
        id="howto-ai-interview"
        data={{
          '@context': 'https://schema.org',
          '@type': 'HowTo',
          name: 'Use the AI Interview Coach',
          description:
            'Follow these steps to practice mock interviews and get AI-powered feedback.',
          step: [
            {
              '@type': 'HowToStep',
              position: 1,
              name: 'Choose a role',
              item: 'https://jobspaceai.com/ai-interview-coach#how-it-works'
            },
            {
              '@type': 'HowToStep',
              position: 2,
              name: 'Start the mock call',
              item: 'https://jobspaceai.com/ai-interview-coach#how-it-works'
            },
            {
              '@type': 'HowToStep',
              position: 3,
              name: 'Follow teleprompter prompts',
              item: 'https://jobspaceai.com/ai-interview-coach#how-it-works'
            }
          ]
        }}
      />

      {/* SoftwareApplication JSON-LD for free app */}
      <JsonLd
        id="app-ai-interview"
        data={{
          '@context': 'https://schema.org',
          '@type': 'SoftwareApplication',
          name: 'JobSpaceAI AI Interview & Career Coach',
          url: 'https://jobspaceai.com/ai-interview-coach',
          applicationCategory: 'BusinessApplication',
          operatingSystem: 'Web',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'GBP'
          }
        }}
      />

      <div className="relative z-10 container px-4 py-4 sm:pb-16 max-w-[1200px] rounded-3xl text-center flex flex-col items-center">
        <div className="relative isolate min-h-screen flex flex-col items-center text-center">
          <div className="relative z-10 container px-4 py-6 sm:py-12 max-w-[1200px] rounded-3xl text-center flex flex-col items-center">
            <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2rem,6vw,3.5rem)] sm:text-[clamp(2.5rem,8vw,4.5rem)]">
              AI{' '}
              <span className="text-yellow-400 text-shadow-yellow">
                Interview Coach
              </span>{' '}
              for UK Job-Seekers
            </h1>
            <p className="text-slate-200 max-w-3xl mx-auto text-[14px] sm:text-[16px] md:text-[18px] mb-8 leading-relaxed">
              78 % of candidates say interviews are their biggest stress point,
              yet AI-powered mock sessions boost confidence by{' '}
              <strong>30 %</strong> on average<sup>[3]</sup>. JobSpaceAI’s
              interview coach acts like a personal recruiter: listening,
              transcribing, and prompting stronger answers in real time—no
              awkward mirror practice required.
            </p>
            <div className="relative mx-auto mt-6 w-full max-w-[1200px] h-[300px] md:h-screen max-h-screen">
              <Image
                src="/images/ai-interview-coach/ai-career-coach.webp"
                alt="AI Career Coach screenshot"
                fill
                sizes="(min-width: 1200px) 1200px, 100vw"
                className="rounded-xl shadow-md object-contain"
                priority={false} // lazy by default
              />
            </div>

            {/* rest of the existing page content, sections loop */}
            {sections.map(({ id, h2 }) => {
              if (id === 'how-it-works') {
                return (
                  <section id={id} key={id} className="mt-12">
                    <h2 className="text-2xl font-semibold mb-4">{h2}</h2>
                    <ol className="list-decimal list-inside mx-auto text-center space-y-2">
                      <li>
                        <strong>Choose a role.</strong> Select “Software
                        Engineer”, “NHS Band 5 Nurse”, etc.
                      </li>
                      <li>
                        <strong>Start the mock call.</strong> Our AI asks common
                        & behavioural questions, listens to your response,
                        scores it on clarity, depth and STAR format.
                      </li>
                      <li>
                        <strong>Live teleprompter prompts.</strong> Need help
                        mid-answer? Look at the on-screen nudge to keep talking
                        confidently.
                      </li>
                    </ol>
                  </section>
                );
              }
              if (id === 'key-features') {
                return (
                  <section id={id} key={id} className="mt-12">
                    <h2 className="text-2xl font-semibold mb-4">{h2}</h2>
                    <ul className="list-disc list-inside mx-auto text-center space-y-1">
                      <li>Role-specific question bank updated monthly</li>
                      <li>Real-time filler-word counter & pace monitor</li>
                      <li>Downloadable feedback report (PDF)</li>
                      <li>Follow-up question generator to refine stories</li>
                    </ul>
                  </section>
                );
              }
              if (id === 'benefits') {
                return (
                  <section id={id} key={id} className="mt-12">
                    <h2 className="text-2xl font-semibold mb-4">{h2}</h2>
                    <p>
                      <strong>Perform under pressure.</strong> Our timing and
                      tone analytics mimic a live recruiter, so you’re ready
                      when the real call comes. Haven’t optimised your documents
                      yet? Start with the{' '}
                      <Link
                        href="/ai-cv-builder"
                        prefetch={false}
                        className="text-primary underline"
                      >
                        AI CV Builder
                      </Link>{' '}
                      and{' '}
                      <Link
                        href="/ai-cover-letter"
                        prefetch={false}
                        className="text-primary underline"
                      >
                        Cover-Letter Generator
                      </Link>
                      .
                    </p>
                  </section>
                );
              }
              if (id === 'faqs') {
                return (
                  <section id={id} key={id} className="mt-12 w-full">
                    <CallToAction
                      label="Start mock interview"
                      href="/signin/signup"
                    />
                    <h2 className="text-2xl font-semibold mb-4">{h2}</h2>
                    <Accordion
                      type="single"
                      collapsible
                      className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden w-full"
                    >
                      {faqs.map(({ question, answer }, index) => (
                        <AccordionItem
                          key={index}
                          value={`item-${index}`}
                          className="border-white/10"
                        >
                          <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-sm sm:text-lg font-medium">
                            {question}
                          </AccordionTrigger>
                          <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
                            {answer}
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                    <JsonLd
                      id="faq-schema"
                      data={faqLd('/ai-interview-coach', faqs)}
                    />
                  </section>
                );
              }
              return null;
            })}
          </div>
        </div>
      </div>
    </>
  );
}
