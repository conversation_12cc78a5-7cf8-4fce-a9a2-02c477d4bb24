import Image from 'next/image';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { Card } from '@/components/ui/card';
import Separator from '@/components/ui/AuthForms/Separator';
import {
  getAuthTypes,
  getViewTypes,
  getDefaultSignInView,
  getRedirectMethod
} from '@/lib/auth-config';

import PasswordSignIn from '@/components/ui/AuthForms/PasswordSignIn';
import EmailSignIn from '@/components/ui/AuthForms/EmailSignIn';
import ForgotPassword from '@/components/ui/AuthForms/ForgotPassword';
import UpdatePassword from '@/components/ui/AuthForms/UpdatePassword';
import OauthSignIn from '@/components/ui/AuthForms/OauthSignIn';
import AlreadySignedIn from '@/components/ui/AuthForms/AlreadySignedIn';
import SignInRequired from '@/components/ui/AuthForms/SignInRequired';
import { createClient } from '@/app/utils/supabase/server';
import { constructMetadata } from '@/lib/seo-config';
import { Metadata } from 'next';
import SignUp from '@/components/ui/AuthForms/Signup';

export async function generateMetadata({
  params
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const resolvedParams = await params;
  let title = 'Sign In';
  let path = '/signin';
  let description =
    'Access your Job Space AI account to manage your career tools and resources.';

  switch (resolvedParams.id) {
    case 'signup':
      title = 'Sign Up';
      path = '/signin/signup';
      description =
        'Create your free JobSpace AI account and start your AI-powered job search. Get personalized job recommendations and career insights.';
      break;
    case 'forgot_password':
      title = 'Reset Password';
      path = '/signin/forgot_password';
      description =
        'Forgot your JobSpace AI password? Reset it securely and regain access to your AI-powered job search tools and applications.';
      break;
    case 'update_password':
      title = 'Update Password';
      path = '/signin/update_password';
      description =
        'Update your JobSpace AI account password. Keep your account secure with a new strong password.';
      break;
    case 'email_signin':
      title = 'Sign In with Email';
      path = '/signin/email_signin';
      description =
        'Sign in to your JobSpace AI account using your email address. Access your personalized job search dashboard and AI-powered career tools.';
      break;
    case 'password_signin':
      title = 'Sign In';
      path = '/signin/password_signin';
      description =
        'Sign in to JobSpace AI with your username and password. Continue your AI-powered job search journey.';
      break;
    default:
      path = `/signin/${resolvedParams.id}`;
  }

  return constructMetadata({
    title: `${title} | Job Space AI`,
    description,
    path
  });
}

export default async function SignIn({
  params,
  searchParams
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{
    next?: string;
    disable_button?: boolean;
    error?: string;
    error_description?: string;
    status?: string;
    status_description?: string;
  }>;
}) {
  const { allowOauth, allowEmail, allowPassword } = getAuthTypes();
  const viewTypes = getViewTypes();
  const redirectMethod = getRedirectMethod();

  const resolvedSearchParams = await searchParams;

  const {
    next,
    disable_button,
    error,
    error_description,
    status,
    status_description
  } = resolvedSearchParams;

  const nextUrl = next ? decodeURIComponent(next) : '/dashboard';

  let viewProp: string;
  const resolvedParams = await params;

  if (resolvedParams.id && viewTypes.includes(resolvedParams.id)) {
    viewProp = resolvedParams.id;
  } else {
    const cookieStore = await cookies();
    const preferredSignInView =
      cookieStore.get('preferredSignInView')?.value || null;
    viewProp = getDefaultSignInView(preferredSignInView);
    const nextParam = next ? `?next=${encodeURIComponent(next)}` : '';
    return redirect(`/signin/${viewProp}${nextParam}`);
  }

  try {
    const supabase = await createClient();
    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (user && viewProp !== 'update_password') {
      if (next?.startsWith('/application-funnel')) {
        return redirect(next);
      }
      return <AlreadySignedIn redirectPath="/onboarding" />;
    } else if (!user && viewProp === 'update_password') {
      return (
        <SignInRequired
          message="You need to sign in to update your password."
          redirectPath="/signin"
        />
      );
    }

    const sharedProps = {
      redirectMethod,
      next: nextUrl,
      error,
      errorDescription: error_description,
      status,
      statusDescription: status_description,
      disableButton: disable_button
    };

    const renderForm = () => {
      switch (viewProp) {
        case 'password_signin':
          return <PasswordSignIn allowEmail={allowEmail} {...sharedProps} />;
        case 'email_signin':
          return <EmailSignIn allowPassword={allowPassword} {...sharedProps} />;
        case 'forgot_password':
          return <ForgotPassword allowEmail={allowEmail} {...sharedProps} />;
        case 'update_password':
          return <UpdatePassword redirectMethod={redirectMethod} />;
        case 'signup':
          return <SignUp allowEmail={allowEmail} {...sharedProps} />;
        default:
          return null;
      }
    };

    const showOauth =
      viewProp !== 'update_password' &&
      viewProp !== 'signup' &&
      viewProp !== 'forgot_password' &&
      allowOauth;

    return (
      <div className="flex justify-center py-8 min-h-screen relative">
        <div className="absolute inset-0 bg-hero-bg/95 -z-10">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-hero-blue-glow blur-3xl animate-blob"></div>
          <div className="absolute bottom-1/3 right-1/3 w-80 h-80 rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000"></div>
          <div className="absolute top-2/3 left-1/2 w-72 h-72 rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000"></div>
          <div className="absolute top-1/3 right-1/4 w-56 h-56 rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000"></div>
        </div>

        <div className="container mx-auto px-4 z-10">
          <div className="flex justify-center mb-6">
            <Image
              src="/images/logo_light.png"
              alt="Job Space AI Logo"
              width={48}
              height={48}
              className="w-12 h-12"
            />
          </div>
          <div className="flex justify-center">
            <Card className="p-4 w-full md:w-96 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg text-white">
              {renderForm()}
              {showOauth && (
                <>
                  <Separator text="Third-party sign-in" />
                  <OauthSignIn next={nextUrl} />
                </>
              )}
            </Card>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    if (error instanceof Error && error.message.includes('NEXT_REDIRECT')) {
      throw error;
    }
    console.error('Error in SignIn component:', error);
    return (
      <div className="flex min-h-screen flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg text-white px-4 py-8 sm:rounded-lg sm:px-10">
            <h2 className="text-center text-2xl font-bold leading-9 tracking-tight text-white">
              Error Loading Sign In
            </h2>
            <p className="mt-4 text-center text-sm text-white/70">
              Please try again later or contact support if the problem persists.
            </p>
          </div>
        </div>
      </div>
    );
  }
}
