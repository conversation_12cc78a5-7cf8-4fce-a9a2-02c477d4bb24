'use client';

import React from 'react';
import { MarketingNavItems } from '@/components/MarketingNav';

interface FeaturesDropdownContentProps {
  onItemClick?: () => void;
}

export default function FeaturesDropdownContent({
  onItemClick
}: FeaturesDropdownContentProps) {
  return (
    <div
      className="p-0"
      style={{ width: '560px', minWidth: '560px', maxWidth: '560px' }}
    >
      <MarketingNavItems onItemClick={onItemClick} />
    </div>
  );
}
