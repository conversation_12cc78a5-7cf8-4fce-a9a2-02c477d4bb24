export const dynamic = 'force-static';
export const revalidate = 86400;

import type { Metadata } from 'next';
import { CallToAction } from 'components/CallToAction';
import { ThemedLink as Link } from 'components/ui/Link';

export const metadata: Metadata = {
  title: 'CV Checker UK | Free ATS Scanner for British Jobs',
  description:
    'Free UK CV checker that scans for ATS compatibility. Used by NHS, Civil Service & FTSE 100 applicants. Instant results, no signup required.',
  alternates: { canonical: '/cv-checker-uk' }
};

export default function CvCheckerUkPage() {
  return (
    <div className="relative isolate min-h-screen overflow-hidden">
      <div className="relative z-10 container mx-auto px-4 py-16 max-w-[900px] text-slate-200">
        <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mb-6 text-white text-[clamp(2rem,6vw,3.5rem)] sm:text-[clamp(2.5rem,8vw,4.5rem)]">
          CV Checker UK - Free ATS Scanner for British Jobs
        </h1>
        <p className="text-lg mb-8 leading-relaxed">
          Check your CV for UK jobs with our free ATS scanner built specifically for the British job market. Used by applicants to NHS, Civil Service, Barclays, Tesco, and FTSE 100 companies. Get instant feedback on formatting, keywords, and ATS compatibility in under 60 seconds.
        </p>
        <CallToAction label="Scan My CV Free - No Signup Required" href="/signin/signup" />

        <section className="mt-12 space-y-4">
          <h2 className="text-2xl font-semibold text-white">Why UK CVs need different formatting than US resumes</h2>
          <p>
            UK employers expect a concise personal statement, British spelling, and a clean layout without excessive graphics. Our checker ensures your CV meets these local standards so ATS systems can parse it correctly.
          </p>
        </section>

        <section className="mt-8 space-y-4">
          <h2 className="text-2xl font-semibold text-white">Examples of UK employers that use ATS</h2>
          <ul className="list-disc list-inside space-y-1">
            <li>NHS trusts across the UK</li>
            <li>Civil Service departments</li>
            <li>Major retailers like Tesco and Sainsbury&apos;s</li>
            <li>FTSE 100 companies including Barclays and BP</li>
          </ul>
        </section>

        <section className="mt-8 space-y-4">
          <h2 className="text-2xl font-semibold text-white">British English terminology matters</h2>
          <p>
            Spellings such as <em>organisation</em> over <em>organization</em> help your CV align with UK job descriptions. Our scanner checks for these differences to boost relevance.
          </p>
        </section>

        <section className="mt-8 space-y-4">
          <h2 className="text-2xl font-semibold text-white">UK-specific CV sections</h2>
          <p>
            Include a short personal statement instead of an objective and highlight UK qualifications. The tool flags missing sections so hiring managers see exactly what they expect.
          </p>
        </section>

        <div className="mt-12 text-lg text-slate-200">
          Looking for more targeted checks? Try our <Link href="/nhs-cv-checker">NHS CV Checker</Link> for healthcare roles or our <Link href="/ats-resume-checker-uk">ATS Resume Checker</Link> to test system compatibility.
        </div>
      </div>
    </div>
  );
}
