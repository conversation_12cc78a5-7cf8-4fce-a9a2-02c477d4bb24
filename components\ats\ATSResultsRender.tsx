import React from 'react';
import { ATSAnalysisResponse } from '@/app/types/globalTypes';
import { Progress } from '@/components/ui/progress';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, AlertCircle, Info } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipTrigger,
  TooltipProvider
} from '@/components/ui/tooltip';
import ATSSkillImprovementCard from './ATSSkillImprovementCard';

// Interface for format issue objects
interface FormatIssue {
  description: string;
}

// Interface for keyword object
interface KeywordItem {
  keyword: string;
  count?: number;
  context?: string;
  relevance_score?: number;
}

// Interface for skill object
interface SkillItem {
  skill: string;
  experience_level?: string | { level: string };
  last_used?: string;
  importance?: string;
  in_resume?: boolean;
  in_job_description?: boolean;
}

// Define a custom interface that extends ATSAnalysisResult with optional properties
interface ExtendedAnalysisResult {
  overall_match: string | number;
  overall_match_percentage: number;
  section_analysis: Record<
    string,
    {
      score?: number;
      quality_score?: number; // Added to support new format
      feedback?: string[] | string;
      recommendations?: string[]; // Added to support new format
    }
  >;
  keyword_analysis: {
    essential?: (KeywordItem | string)[];
    preferred?: (KeywordItem | string)[];
  };
  skills_analysis: Record<
    string,
    {
      score?: number;
      relevance?: number;
      experience_level?: string;
      confidence?: number;
      feedback?: string[];
      matches?: string[];
      missing?: string[];
    }
  >;
  format_analysis: {
    metrics?: {
      score?: number;
      feedback?: string[];
      suggestions?: string[];
      formatting_consistency?: {
        overall_consistency: number;
        [key: string]: number;
      };
    };
    [key: string]: unknown;
  };
  keyword_match_percentage: number;
  skill_match_percentage: number;
  keyword_matches: (KeywordItem | string)[];
  skill_matches: (SkillItem | string)[];
  missing_keywords: string[];
  missing_skills: string[];
  recommendations?: string[];
  // Additional properties
  readability_score?: number;
  format_issues?: Array<FormatIssue | string>;
}

// Interface for grouped suggestions
interface Suggestion {
  title: string;
  description: string;
  priority?: 'high' | 'medium' | 'low';
}

interface GroupedSuggestions {
  high: Suggestion[];
  medium: Suggestion[];
  low: Suggestion[];
}

// Props that support both naming conventions for backward compatibility
interface ATSResultsRenderProps {
  atsResult?: ATSAnalysisResponse | ExtendedAnalysisResult;
  analysisResult?: ATSAnalysisResponse | ExtendedAnalysisResult;
  activeTab?: string;
}

const ATSResultsRender: React.FC<ATSResultsRenderProps> = ({
  atsResult,
  analysisResult,
  activeTab = 'overview'
}) => {
  // Use either prop, for backward compatibility
  const result = (atsResult || analysisResult) as ExtendedAnalysisResult;

  // Updated early return condition with logging
  if (!result || Object.keys(result).length === 0) {
    console.log('ATSResultsRender: No result data available');
    return (
      <div className="p-4 bg-muted rounded-lg">
        <p className="text-muted-foreground">
          No ATS analysis results available
        </p>
      </div>
    );
  }

  // Add debug logging for result data
  console.log('ATSResultsRender processing result:', {
    hasOverallMatch: 'overall_match' in result,
    hasKeywords: 'keyword_matches' in result,
    hasSectionAnalysis: 'section_analysis' in result,
    activeTab: activeTab,
    resultType: typeof result
  });

  // Extract section scores
  const sectionScores = result.section_analysis || {};

  // Extract keyword lists
  const foundKeywords = result.keyword_matches || [];
  const missingKeywords = result.missing_keywords || [];

  // Extract skill lists
  const matchedSkills = result.skill_matches || [];
  const missingSkills = result.missing_skills || [];

  // Extract improvement suggestions
  const suggestions: Suggestion[] = result.recommendations
    ? result.recommendations.map((text: string) => ({
        title: 'Suggestion',
        description: text,
        priority: 'medium' // Default priority
      }))
    : [];

  // Define a type for skill improvement items
  interface SkillImprovement {
    skill: string;
    originalContent: string;
    enhancedContent: string;
    learnMoreLinks: Array<{ text: string; url: string }>;
    whyThisMatters: string;
    implementationTips: string[];
  }

  // Extract skill improvements
  const skillImprovements: SkillImprovement[] = Object.entries(
    result.skills_analysis || {}
  )
    .map(([skill, analysis]) => {
      // Check if this is a skill that needs improvement (has feedback)
      if (analysis.feedback && analysis.feedback.length > 0) {
        // Create a structured skill improvement object
        return {
          skill,
          originalContent: `Front-end web developer with over six years of experience at multinational companies.`,
          enhancedContent: `**Improved ${skill} Skills**\n\nBased on your CV and the job requirements, enhancing your ${skill} skills will significantly improve your job match. Consider adding specific achievements and projects that demonstrate your proficiency.\n\n**Recommended Learning Resources:**\n1. Modern React with Redux: Comprehensive Udemy course covering React and Redux fundamentals, middleware, and real-world app building. [Learn More](https://www.udemy.com/course/react-redux/)\n2. Redux Essentials Tutorial: Official Redux tutorial series that walks through Redux Toolkit, React-Redux integration, and common patterns. [Learn More](https://redux.js.org/tutorials/essentials/part-1-overview-concepts)\n3. Redux Official Documentation: Comprehensive guide to Redux concepts, usage patterns, and best practices. [Learn More](https://redux.js.org/)`,
          learnMoreLinks: [
            {
              text: 'Modern React with Redux',
              url: 'https://www.udemy.com/course/react-redux/'
            },
            {
              text: 'Redux Essentials Tutorial',
              url: 'https://redux.js.org/tutorials/essentials/part-1-overview-concepts'
            },
            {
              text: 'Redux Official Documentation',
              url: 'https://redux.js.org/'
            }
          ],
          whyThisMatters: `${skill} appears in the job description as a key requirement. Improving and highlighting this skill can increase your match rate by approximately 15-20%. Employers in this field particularly value practical experience with ${skill}.`,
          implementationTips: [
            `Focus on quantifiable achievements when possible`,
            `Use action verbs and industry-specific terminology`,
            `Ensure your formatting is consistent throughout your CV`
          ]
        } as SkillImprovement;
      }
      return null;
    })
    .filter((item): item is SkillImprovement => item !== null); // Type-safe filter

  // Group suggestions by priority
  const groupedSuggestions: GroupedSuggestions = {
    high: suggestions.filter((s) => s.priority === 'high'),
    medium: suggestions.filter((s) => s.priority === 'medium'),
    low: suggestions.filter((s) => s.priority === 'low' || !s.priority) // Include suggestions without priority in low
  };

  console.log(
    'ATSResultsRender - activeTab:',
    activeTab,
    'type:',
    typeof activeTab
  );

  // Force activeTab to be a string
  const tabToUse = String(activeTab || 'overview');
  console.log('ATSResultsRender - tabToUse:', tabToUse);

  if (tabToUse === 'keywords') {
    return (
      <TooltipProvider>
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
              <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
                <CardTitle className="text-lg text-white font-bold">
                  Keyword Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="bg-gray-900">
                <div className="mb-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium text-gray-100">
                      Keyword Match
                    </span>
                    <span className="text-sm font-medium text-gray-100">
                      {result.keyword_match_percentage?.toFixed(1)}%
                    </span>
                  </div>
                  <Progress
                    value={result.keyword_match_percentage || 0}
                    className="h-2.5 bg-gray-700"
                    indicatorClassName="bg-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                  />
                </div>

                {/* Found Keywords */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-100">
                    Found Keywords
                  </h4>
                  <div className="grid gap-1.5">
                    {foundKeywords.map((item, idx) => (
                      <div
                        key={idx}
                        className="flex items-center justify-between py-1 px-2 rounded hover:bg-gray-800/50 transition-colors"
                      >
                        <span className="text-sm text-gray-300">
                          {typeof item === 'string' ? item : item.keyword}
                        </span>
                        {typeof item !== 'string' && item.relevance_score ? (
                          <span className="text-xs px-1.5 py-0.5 rounded bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))]">
                            Found
                          </span>
                        ) : (
                          <span className="text-xs px-1.5 py-0.5 rounded bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))]">
                            Match
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Missing Keywords */}
                <div className="space-y-3 mt-4">
                  <h4 className="text-sm font-medium text-gray-100">
                    Missing Keywords
                  </h4>
                  <div className="grid gap-1.5">
                    {missingKeywords.map((keyword, idx) => (
                      <div
                        key={idx}
                        className="flex items-center py-1 px-2 rounded hover:bg-gray-800/50 transition-colors"
                      >
                        <span className="text-sm text-red-400">{keyword}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Skills Analysis */}
            <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
              <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
                <CardTitle className="text-lg text-white font-bold">
                  Skills Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="bg-gray-900">
                <div className="mb-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium text-gray-100">
                      Skills Match
                    </span>
                    <span className="text-sm font-medium text-gray-100">
                      {result.skill_match_percentage?.toFixed(1)}%
                    </span>
                  </div>
                  <Progress
                    value={result.skill_match_percentage || 0}
                    className="h-2.5 bg-gray-700"
                    indicatorClassName="bg-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                  />
                </div>

                {/* Matched Skills */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-100">
                    Matched Skills
                  </h4>
                  <div className="grid gap-1.5">
                    {matchedSkills.map((item, idx) => (
                      <div
                        key={idx}
                        className="flex items-center justify-between py-1 px-2 rounded hover:bg-gray-800/50 transition-colors"
                      >
                        <span className="text-sm text-gray-300">
                          {typeof item === 'string' ? item : item.skill}
                        </span>
                        {typeof item !== 'string' && (
                          <span className="text-xs px-1.5 py-0.5 rounded bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))]">
                            {typeof item.experience_level === 'string'
                              ? item.experience_level
                              : item.experience_level?.level || 'Intermediate'}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Missing Skills */}
                <div className="space-y-3 mt-4">
                  <h4 className="text-sm font-medium text-gray-100">
                    Missing Skills
                  </h4>
                  <div className="grid gap-1.5">
                    {missingSkills.map((skill, idx) => (
                      <div
                        key={idx}
                        className="flex items-center py-1 px-2 rounded hover:bg-gray-800/50 transition-colors"
                      >
                        <span className="text-sm text-red-400">{skill}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </TooltipProvider>
    );
  } else if (tabToUse === 'improvements') {
    return (
      <TooltipProvider>
        <div>
          <div className="grid grid-cols-1 gap-6">
            {/* Format Analysis */}
            {result.format_analysis && (
              <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
                <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
                  <CardTitle className="text-lg text-white font-bold">
                    Format Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent className="bg-gray-900">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {result.readability_score !== undefined && (
                      <div className="space-y-2 bg-gray-800/30 p-3 rounded-md">
                        <div className="flex justify-between">
                          <span className="flex items-center text-sm font-medium text-gray-100">
                            Readability Score
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-4 w-4 text-gray-400 ml-1.5" />
                              </TooltipTrigger>
                              <TooltipContent
                                side="right"
                                className="bg-gray-800 border-gray-700 text-gray-200"
                              >
                                <p className="w-64">
                                  How easy your CV is to read and parse by ATS
                                  systems
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </span>
                          <span className="text-xs px-1.5 py-0.5 rounded bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))]">
                            {result.readability_score}%
                          </span>
                        </div>
                        <Progress
                          value={result.readability_score}
                          className="h-2 bg-gray-700"
                          indicatorClassName="bg-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                        />
                      </div>
                    )}

                    {result.format_analysis.metrics &&
                      result.format_analysis.metrics.formatting_consistency && (
                        <div className="space-y-2 bg-gray-800/30 p-3 rounded-md">
                          <div className="flex justify-between">
                            <span className="flex items-center text-sm font-medium text-gray-100">
                              Format Consistency
                              <Tooltip>
                                <TooltipTrigger>
                                  <Info className="h-4 w-4 text-gray-400 ml-1.5" />
                                </TooltipTrigger>
                                <TooltipContent
                                  side="right"
                                  className="bg-gray-800 border-gray-700 text-gray-200"
                                >
                                  <p className="w-64">
                                    How consistent your formatting is throughout
                                    your CV
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </span>
                            <span className="text-xs px-1.5 py-0.5 rounded bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))]">
                              {typeof result.format_analysis.metrics
                                .formatting_consistency === 'object' &&
                              'overall_consistency' in
                                result.format_analysis.metrics
                                  .formatting_consistency
                                ? (
                                    Number(
                                      (
                                        result.format_analysis.metrics
                                          .formatting_consistency as {
                                          overall_consistency: number;
                                        }
                                      ).overall_consistency
                                    ) * 100
                                  ).toFixed(0) + '%'
                                : 'N/A'}
                            </span>
                          </div>
                          <Progress
                            value={
                              typeof result.format_analysis.metrics
                                .formatting_consistency === 'object' &&
                              'overall_consistency' in
                                result.format_analysis.metrics
                                  .formatting_consistency
                                ? Number(
                                    (
                                      result.format_analysis.metrics
                                        .formatting_consistency as {
                                        overall_consistency: number;
                                      }
                                    ).overall_consistency
                                  ) * 100
                                : 0
                            }
                            className="h-2 bg-gray-700"
                            indicatorClassName="bg-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                          />
                        </div>
                      )}
                  </div>

                  {/* Format Issues */}
                  {result.format_issues && result.format_issues.length > 0 && (
                    <div className="mt-6 bg-gray-800/30 p-3 rounded-md">
                      <h3 className="text-sm font-medium mb-3 text-gray-100">
                        Format Issues
                      </h3>
                      <ul className="space-y-2">
                        {result.format_issues.map((issue, idx) => (
                          <li key={idx} className="flex gap-2 text-sm">
                            <AlertTriangle className="h-4 w-4 text-amber-400 flex-shrink-0 mt-0.5" />
                            <span className="text-gray-300">
                              {typeof issue === 'string'
                                ? issue
                                : issue.description}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Improvement Suggestions */}
            {suggestions.length > 0 && (
              <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
                <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
                  <CardTitle className="text-lg text-white font-bold">
                    Improvement Suggestions
                  </CardTitle>
                </CardHeader>
                <CardContent className="bg-gray-900">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {groupedSuggestions.high.length > 0 && (
                      <div className="space-y-3 col-span-1 md:col-span-2">
                        <h3 className="flex items-center text-sm font-semibold text-red-400">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          High Priority Improvements
                        </h3>
                        <div className="grid grid-cols-1 gap-3">
                          {groupedSuggestions.high.map((suggestion, index) => (
                            <div
                              key={index}
                              className="border-l-4 border-l-red-500 bg-gray-800/30 p-3 rounded-md"
                            >
                              <p className="text-sm font-medium text-white">
                                {suggestion.title}
                              </p>
                              <p className="text-sm text-gray-300 mt-1.5">
                                {suggestion.description}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {groupedSuggestions.medium.length > 0 && (
                      <div className="space-y-3">
                        <h3 className="flex items-center text-sm font-semibold text-amber-400">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Medium Priority
                        </h3>
                        <div className="grid grid-cols-1 gap-3">
                          {groupedSuggestions.medium.map(
                            (suggestion, index) => (
                              <div
                                key={index}
                                className="border-l-4 border-l-amber-400 bg-gray-800/30 p-3 rounded-md"
                              >
                                <p className="text-sm font-medium text-white">
                                  {suggestion.title}
                                </p>
                                <p className="text-sm text-gray-300 mt-1.5">
                                  {suggestion.description}
                                </p>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    )}

                    {groupedSuggestions.low.length > 0 && (
                      <div className="space-y-3">
                        <h3 className="flex items-center text-sm font-semibold text-[hsl(var(--hero-yellow))]">
                          <Info className="h-4 w-4 mr-2" />
                          Suggested Improvements
                        </h3>
                        <div className="grid grid-cols-1 gap-3">
                          {groupedSuggestions.low.map((suggestion, index) => (
                            <div
                              key={index}
                              className="border-l-4 border-l-[hsl(var(--hero-yellow))] bg-gray-800/30 p-3 rounded-md"
                            >
                              <p className="text-sm font-medium text-white">
                                {suggestion.title}
                              </p>
                              <p className="text-sm text-gray-300 mt-1.5">
                                {suggestion.description}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Skill Improvements */}
            {skillImprovements.length > 0 && (
              <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
                <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
                  <CardTitle className="text-lg text-white font-bold flex items-center">
                    <Info className="h-5 w-5 mr-2 text-[hsl(var(--hero-yellow))]" />
                    Skill Improvement Resources
                  </CardTitle>
                </CardHeader>
                <CardContent className="bg-gray-900">
                  <div className="grid grid-cols-1 gap-4">
                    {skillImprovements.map((improvement, index) => (
                      <ATSSkillImprovementCard
                        key={index}
                        skill={improvement.skill}
                        originalContent={improvement.originalContent}
                        enhancedContent={improvement.enhancedContent}
                        learnMoreLinks={improvement.learnMoreLinks}
                        whyThisMatters={improvement.whyThisMatters}
                        implementationTips={improvement.implementationTips}
                      />
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* If no format issues or suggestions */}
            {(!result.format_analysis ||
              !result.format_issues ||
              result.format_issues.length === 0) &&
              suggestions.length === 0 &&
              skillImprovements.length === 0 && (
                <div className="flex gap-3 items-center text-green-400 p-5 bg-green-900/30 border border-green-700/50 rounded-lg">
                  <CheckCircle className="h-6 w-6" />
                  <span className="font-medium text-white">
                    Your CV looks great! No major improvements needed.
                  </span>
                </div>
              )}
          </div>
        </div>
      </TooltipProvider>
    );
  } else if (tabToUse === 'overview') {
    // Overview tab (default)
    return (
      <TooltipProvider>
        <div>
          <div className="grid grid-cols-1 gap-6">
            <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
              <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
                <CardTitle className="text-lg text-white font-bold">
                  Overall Match
                </CardTitle>
              </CardHeader>
              <CardContent className="bg-gray-900">
                <div className="mb-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm font-medium text-gray-100">
                      Match Score
                    </span>
                    <span className="text-sm font-medium text-gray-100">
                      {Math.round(result.overall_match_percentage || 0)}%
                    </span>
                  </div>
                  <Progress
                    value={result.overall_match_percentage || 0}
                    className="h-2.5 bg-gray-700"
                    indicatorClassName="bg-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
              <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
                <CardTitle className="text-lg text-white font-bold">
                  Section Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="bg-gray-900">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {Object.entries(sectionScores).map(
                    ([section, analysis], idx) => {
                      const score = analysis.quality_score || 0;
                      const recommendations = analysis.recommendations || [];
                      const scaledScore =
                        score < 1
                          ? Math.round(score * 100)
                          : score < 10 && result.overall_match_percentage > 50
                            ? Math.round(score * 10) // Multiply by 10 if score is suspiciously low
                            : Math.round(score);

                      return (
                        <div
                          key={idx}
                          className="space-y-2 bg-gray-800/30 p-3 rounded-md"
                        >
                          <div className="flex justify-between">
                            <span className="text-sm font-medium capitalize text-gray-100">
                              {section}
                            </span>
                            <span className="text-xs px-1.5 py-0.5 rounded bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))]">
                              {scaledScore}%
                            </span>
                          </div>
                          <Progress
                            value={scaledScore}
                            className="h-2 bg-gray-700"
                            indicatorClassName="bg-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                          />
                          {recommendations.length > 0 && (
                            <ul className="mt-3 space-y-1.5 pl-1">
                              {recommendations.map((rec, recIdx) => (
                                <li
                                  key={recIdx}
                                  className="text-sm text-gray-300 flex gap-2 text-left"
                                >
                                  <span className="text-[hsl(var(--hero-yellow))] flex-shrink-0">
                                    •
                                  </span>
                                  <span className="text-left">{rec}</span>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      );
                    }
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </TooltipProvider>
    );
  } else {
    // Fallback for any other value
    console.log('ATSResultsRender - Unknown tab value, defaulting to overview');
    return (
      <TooltipProvider>
        <div className="space-y-8">
          <Card className="shadow-sm backdrop-blur-sm bg-black/80 border-gray-800">
            <CardHeader className="pb-3 bg-gradient-to-r from-gray-900 to-gray-800">
              <CardTitle className="text-lg text-white font-bold">
                Overall Match
              </CardTitle>
            </CardHeader>
            <CardContent className="bg-gray-900">
              <div className="mb-6">
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-gray-100">
                    Match Score
                  </span>
                  <span className="text-sm font-medium text-gray-100">
                    {Math.round(result.overall_match_percentage || 0)}%
                  </span>
                </div>
                <Progress
                  value={result.overall_match_percentage || 0}
                  className="h-2.5 bg-gray-700"
                  indicatorClassName="bg-[hsl(var(--hero-yellow))] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </TooltipProvider>
    );
  }
};

// Add support for both prop names to maintain backward compatibility
const ATSResultsRenderWithFallback: React.FC<ATSResultsRenderProps> = (
  props
) => {
  console.log('ATSResultsRenderWithFallback v2 - props:', props);
  return <ATSResultsRender {...props} />;
};

export default ATSResultsRenderWithFallback;
