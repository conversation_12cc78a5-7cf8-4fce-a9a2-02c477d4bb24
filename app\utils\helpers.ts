/**
 * Gets the base URL for the application, handling both client and server-side rendering
 * by checking browser `window`, Cloud Run env var, and a fallback env var.
 * @returns The base URL (e.g., http://localhost:3000 or https://jobspaceai.com)
 */
export const getBaseURL = (): string => {
  // Client-side: use the browser's origin
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // Server-side: Cloud Run custom domain
  if (process.env.K_SERVICE) {
    return 'https://jobspaceai.com';
  }

  // Fallback to explicit public URL env var or localhost
  const fallback = process.env.NEXT_PUBLIC_URL || 'http://localhost:3000';
  return fallback.replace(/\/+$/, '');
};

/**
 * Gets the full URL for a given path, handling both client and server-side rendering
 * @param path The path to append to the base URL
 * @returns The full URL (e.g., https://jobspaceai.com/auth/reset_password)
 */
export const getURL = (path: string = ''): string => {
  const base = getBaseURL();
  const clean = path.replace(/^\/*/, '');
  return clean ? `${base}/${clean}` : base;
};

// Toast redirect helpers (unchanged)
const toastKeyMap: { [key: string]: string[] } = {
  status: ['status', 'status_description'],
  error: ['error', 'error_description']
};

const getToastRedirect = (
  path: string,
  toastType: string,
  toastName: string,
  toastDescription: string = '',
  disableButton: boolean = false,
  arbitraryParams: string = ''
): string => {
  const [nameKey, descriptionKey] = toastKeyMap[toastType];
  let redirectPath = `${path}?${nameKey}=${encodeURIComponent(toastName)}`;

  if (toastDescription) {
    redirectPath += `&${descriptionKey}=${encodeURIComponent(toastDescription)}`;
  }
  if (disableButton) {
    redirectPath += `&disable_button=true`;
  }
  if (arbitraryParams) {
    redirectPath += `&${arbitraryParams}`;
  }

  return redirectPath;
};

export const getStatusRedirect = (
  path: string,
  statusName: string,
  statusDescription: string = '',
  disableButton: boolean = false,
  arbitraryParams: string = ''
) =>
  getToastRedirect(
    path,
    'status',
    statusName,
    statusDescription,
    disableButton,
    arbitraryParams
  );

export const getErrorRedirect = (
  path: string,
  errorName: string,
  errorDescription: string = '',
  disableButton: boolean = false,
  arbitraryParams: string = ''
) =>
  getToastRedirect(
    path,
    'error',
    errorName,
    errorDescription,
    disableButton,
    arbitraryParams
  );
