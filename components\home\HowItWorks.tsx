// components/home/<USER>
'use client';

import { Check } from 'lucide-react';
import { createClient } from '@/app/utils/supabase/client';
import { useEffect, useState } from 'react';
import FAQSchemaMarkup from '../seo/FAQSchemaMarkup';
import { ThemedLinkButton } from '@/components/ui/themed-button';
import SectionHeading, { Highlight } from '../ui/section-heading';

const features = [
  {
    icon: '🎯',
    title: 'Smart Application Tools',
    description:
      'Save thousands on career coaching with our cost-effective AI-powered tools that analyze your CV, identify skill gaps, generate tailored cover letters, and prepare you for interviews.',
    features: [
      'Save 80% vs traditional coaches',
      'Boost interview callbacks by 3x',
      'Optimize CVs in minutes',
      'Tailor applications perfectly'
    ],
    link: '/onboarding',
    buttonText: 'Start Your Journey'
  },
  {
    icon: '📊',
    title: 'Personalized Dashboard',
    description:
      'Our dashboard works like an à la carte menu - choose exactly which AI tools you need when you need them. Manage all your CVs, job postings, and career resources in one central hub.',
    features: [
      'Pay only for tools you need',
      'Get career-specific guidance',
      'Track all your applications',
      'Stay ahead of market trends'
    ],
    link: '/dashboard', // Base link that will be handled by getFeatureLink
    buttonText: 'Access Dashboard',
    variant: 'secondary' as const
  }
];

export const HowItWorksSection = () => {
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();

    const getUser = async () => {
      try {
        setIsLoading(true);
        const {
          data: { user }
        } = await supabase.auth.getUser();
        setUserId(user?.id || null);
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    getUser();
  }, []);

  // Updated function to properly handle the dashboard link
  const getFeatureLink = (feature: (typeof features)[0]): string => {
    if (feature.title === 'Personalized Dashboard') {
      if (userId) {
        return `/dashboard/${userId}`;
      } else if (isLoading) {
        // While loading user data, return the base link
        return feature.link;
      } else {
        // If no user is logged in, redirect to sign in
        return '/signin?redirect=dashboard';
      }
    }
    return feature.link;
  };

  // FAQ data for schema markup
  const faqs = [
    {
      question: 'How do the Smart Application Tools work?',
      answer:
        'Our AI-powered tools analyze your CV, identify skill gaps, generate tailored cover letters, and prepare you for interviews - all at a fraction of the cost of traditional career coaching.'
    },
    {
      question: 'What is included in the Personalized Dashboard?',
      answer:
        'The dashboard works like an à la carte menu where you can choose exactly which AI tools you need. It allows you to manage all your CVs, job postings, and career resources in one central hub.'
    },
    {
      question: 'How much can I save compared to traditional coaching?',
      answer:
        'Users typically save up to 80% compared to traditional career coaching services while getting more personalized and data-driven guidance.'
    },
    {
      question: 'How quickly can I optimize my CV?',
      answer:
        'Our AI tools can analyze and provide optimization recommendations for your CV in just minutes, allowing you to quickly tailor it for specific job applications.'
    }
  ];

  return (
    <section className="relative isolate py-16 overflow-hidden">
      <div className="relative z-10 mx-auto w-full  px-4">
        <div className=" mx-auto text-center flex flex-col items-center">
          <span className="text-yellow-300 font-semibold">GET STARTED</span>
          <SectionHeading className="text-center max-w-4xl mx-auto">
            Your Path to <Highlight>Reports</Highlight> Success
          </SectionHeading>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8 max-w-4xl mx-auto">
            {features.map((feature) => (
              <div
                key={feature.title}
                className="flex flex-col backdrop-blur-sm bg-white/10 border border-white/20 p-6 sm:p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
              >
                <div className="flex-grow">
                  <div className="text-yellow-400 text-4xl mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-roboto-condensed font-semibold mb-4 text-white">
                    {feature.title}
                  </h3>
                  <div className="space-y-4 mb-6">
                    <p className="text-slate-200 text-left">
                      {feature.description}
                    </p>
                    <ul className="space-y-2">
                      {feature.features.map((item) => (
                        <li
                          key={item}
                          className="flex items-center gap-2 text-slate-200 text-left"
                        >
                          <Check className="w-4 h-4 text-yellow-400" />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <ThemedLinkButton
                  href={getFeatureLink(feature)}
                  variant={feature.variant || 'primary'}
                  size="md"
                  className="w-full font-semibold"
                  showArrow
                >
                  {feature.buttonText}
                </ThemedLinkButton>
              </div>
            ))}
          </div>

          {/* Add FAQ schema markup for SEO */}
          <FAQSchemaMarkup faqs={faqs} url="https://jobspaceai.com/" />
        </div>
      </div>
    </section>
  );
};
