{"name": "job-ai-platform", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "cross-env NODE_ENV=production next build", "start": "node server.js", "start:prod": "cross-env NODE_ENV=production node server.js", "lint": "next lint", "format": "prettier --check --write .", "clean": "rimraf .next && rimraf node_modules/.cache", "build:analyze": "cross-env ANALYZE=true NODE_ENV=production next build", "test:css": "cross-env NODE_ENV=production npx tailwindcss -i ./app/globals.css -o ./test-output.css --content './app/**/*.{js,ts,jsx,tsx}' './components/**/*.{js,ts,jsx,tsx}' --minify"}, "engines": {"node": "20.x", "yarn": "1.x"}, "resolutions": {"string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@hookform/resolvers": "^3.9.0", "@next/third-parties": "^15.2.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@sendgrid/mail": "^8.1.5", "@stripe/stripe-js": "^4.9.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.46.1", "@upstash/redis": "^1.34.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "docx": "^8.5.0", "embla-carousel-react": "^8.6.0", "form-data": "^4.0.0", "framer-motion": "^11.14.4", "html2pdf.js": "^0.10.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.445.0", "mammoth": "^1.8.0", "next": "^15.2.4", "pdfjs-dist": "4.4.168", "puppeteer": "^23.10.4", "rate-limiter-flexible": "^5.0.5", "react": "^18.2.0", "react-cookie-consent": "^9.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.53.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.1", "react-pdf": "9.1.1", "recharts": "^2.13.0", "remark-gfm": "^4.0.1", "sharp": "^0.33.5", "stripe": "^17.3.0", "supabase": "^2.1.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "^0.0.279", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^20.11.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tailwindcss": "^3.13.0", "postcss": "^8.5.6", "prettier": "^3.3.3", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5.3.0", "webpack-bundle-analyzer": "^4.10.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}