// lib/icons.tsx - Selective icon imports (RECOMMENDED)
// This approach uses Next.js optimizePackageImports for automatic tree-shaking

// ✅ Only import icons you actually use across your app
export {
  // ✅ Essential icons used across the homepage
  FileText,
  Download,
  Upload,
  User,
  Settings,
  Menu,
  X,
  ChevronDown,
  Check,
  Plus,
  ArrowRight,
  ArrowLeft,
  Search,
  Eye,
  Edit,
  Trash,
  Star,
  Heart,
  Clock,
  Target,

  // ✅ Homepage-specific icons
  Sparkles,
  ShieldCheck,
  Brain,
  TrendingUp,
  Zap,
  Users,
  Briefcase,
  Award,
  Package,
  Shield,
  CreditCard,
  Lock,

  // ✅ Additional icons from your components
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Calendar,
  Mail,
  Phone,
  Globe,
  Home,
  ChartBar,
  PieChart,
  BarChart,
  LineChart
} from 'lucide-react';

// ✅ Usage in components:
/*
  // Instead of importing from lucide-react directly:
  import { FileText, Download } from 'lucide-react';
  
  // Import from your selective barrel export:
  import { FileText, Download } from '@/lib/icons';
  
  // This way:
  // 1. You only bundle icons you actually use
  // 2. No dynamic loading complexity
  // 3. Full TypeScript support
  // 4. Next.js optimizePackageImports handles tree-shaking
  */

// ✅ For heavy components, still use dynamic imports:
import dynamic from 'next/dynamic';

export const DynamicChart = dynamic(
  () => import('recharts').then((mod) => ({ default: mod.LineChart })),
  {
    loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded" />,
    ssr: false
  }
);

export const DynamicMarkdown = dynamic(() => import('react-markdown'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-32 rounded" />
});

export const DynamicMotionDiv = dynamic(
  () => import('framer-motion').then((mod) => ({ default: mod.motion.div })),
  {
    ssr: false
  }
);
