// types/analytics.ts

// ✅ Google Analytics Types
export interface GtagConfig {
  debug_mode?: boolean;
  send_page_view?: boolean;
  page_path?: string;
  page_title?: string;
  custom_map?: Record<string, string>;
  groups?: string;
  linker?: {
    domains: string[];
    accept_incoming?: boolean;
    url_position?: 'query' | 'fragment';
  };
  custom_parameter?: string;
  optimize_id?: string;
  transport_type?: 'beacon' | 'xhr' | 'image';
  sample_rate?: number;
  site_speed_sample_rate?: number;
  allow_google_signals?: boolean;
  allow_ad_personalization_signals?: boolean;
}

export interface GtagEventParams {
  event_category?: string;
  event_label?: string;
  value?: number;
  currency?: string;
  transaction_id?: string;
  page_path?: string;
  page_title?: string;
  page_location?: string;
  user_id?: string;
  session_id?: string;
  engagement_time_msec?: number;
  screen_name?: string;
  // E-commerce specific
  items?: GtagItem[];
  coupon?: string;
  shipping?: number;
  tax?: number;
  // Custom parameters (excluding items which is handled above)
  [key: string]: string | number | boolean | undefined | GtagItem[];
}

export interface GtagConsentParams {
  analytics_storage?: 'granted' | 'denied';
  ad_storage?: 'granted' | 'denied';
  ad_user_data?: 'granted' | 'denied';
  ad_personalization?: 'granted' | 'denied';
  functionality_storage?: 'granted' | 'denied';
  security_storage?: 'granted' | 'denied';
  personalization_storage?: 'granted' | 'denied';
  wait_for_update?: number;
  region?: string[];
}

export interface GtagPurchaseParams {
  // Required purchase fields
  transaction_id: string;
  value: number;
  currency: string;
  // Optional purchase fields
  items?: GtagItem[];
  coupon?: string;
  shipping?: number;
  tax?: number;
  // Inherited event params (excluding items to avoid conflict)
  event_category?: string;
  event_label?: string;
  page_path?: string;
  page_title?: string;
  page_location?: string;
  user_id?: string;
  session_id?: string;
  engagement_time_msec?: number;
  screen_name?: string;
}

export interface GtagItem {
  item_id?: string;
  item_name?: string;
  item_category?: string;
  item_category2?: string;
  item_category3?: string;
  item_category4?: string;
  item_category5?: string;
  item_brand?: string;
  item_variant?: string;
  price?: number;
  quantity?: number;
  index?: number;
  coupon?: string;
  item_list_id?: string;
  item_list_name?: string;
  location_id?: string;
}

// ✅ Microsoft Clarity Types
export interface ClarityEventParams {
  [key: string]: string | number | boolean;
}

export interface ClarityIdentifyParams {
  userId: string;
  sessionId?: string;
  pageId?: string;
  userHint?: string;
}

// ✅ Google Tag Manager Types
export interface DataLayerEvent {
  event: string;
  [key: string]: unknown;
}

export interface GTMConfig {
  gtm_id: string;
  data_layer_name?: string;
  auth?: string;
  preview?: string;
  cookies_win?: string;
}

// ✅ Analytics Manager Types
export interface AnalyticsConfig {
  gaId?: string;
  gtmId?: string;
  clarityId?: string;
  gadsId?: string;
  debugMode?: boolean;
  enableConsent?: boolean;
}

export interface TrackingEvent {
  name: string;
  parameters?: GtagEventParams;
  timestamp?: number;
  source?: 'ga' | 'gtm' | 'clarity';
}

export interface ConversionEvent extends TrackingEvent {
  conversion_id?: string;
  conversion_label?: string;
  conversion_value?: number;
  conversion_currency?: string;
}

// ✅ Consent Management Types
export interface ConsentState {
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
  necessary: boolean;
  timestamp?: number;
  version?: string;
}

export interface ConsentUpdateEvent {
  consent_state: ConsentState;
  updated_at: string;
  source: 'banner' | 'settings' | 'api';
}

// ✅ Component Props Types
export interface AnalyticsWrapperProps {
  gaId?: string;
  gtmId?: string;
  clarityId?: string;
  gadsId?: string;
  children?: React.ReactNode;
  enableDebug?: boolean;
}

export interface ConsentBannerProps {
  onAccept?: (consent: ConsentState) => void;
  onDecline?: () => void;
  onCustomize?: () => void;
  position?: 'top' | 'bottom';
  theme?: 'light' | 'dark' | 'auto';
}

// ✅ Error Types
export interface AnalyticsError {
  code: string;
  message: string;
  timestamp: number;
  source: 'ga' | 'gtm' | 'clarity' | 'consent';
  details?: Record<string, unknown>;
}

// ✅ Performance Monitoring Types
export interface AnalyticsPerformance {
  loadTime: number;
  initTime: number;
  firstEvent: number;
  errors: AnalyticsError[];
  status: 'loading' | 'ready' | 'error' | 'disabled';
}

// ✅ User Session Types
export interface UserSession {
  session_id: string;
  user_id?: string;
  start_time: number;
  page_views: number;
  events: TrackingEvent[];
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
}

// ✅ A/B Testing Types
export interface ExperimentConfig {
  experiment_id: string;
  variant_id: string;
  experiment_name?: string;
  variant_name?: string;
}

// ✅ E-commerce Types
export interface EcommerceEvent {
  currency: string;
  value: number;
  items: GtagItem[];
  transaction_id?: string;
  coupon?: string;
  shipping?: number;
  tax?: number;
}

// ✅ Custom Dimension Types
export interface CustomDimension {
  index: number;
  value: string;
}

export interface CustomMetric {
  index: number;
  value: number;
}

// ✅ Utility Types
export type EventCallback = (
  eventName: string,
  parameters?: GtagEventParams
) => void;
export type ConsentCallback = (consent: ConsentState) => void;
export type ErrorCallback = (error: AnalyticsError) => void;

// ✅ Configuration validation
export interface ConfigValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface AnalyticsHooks {
  useAnalytics: () => {
    trackEvent: EventCallback;
    trackPageView: (path: string, title?: string) => void;
    updateConsent: (consent: GtagConsentParams) => void;
    isReady: () => boolean;
  };
  useConsent: () => {
    consent: ConsentState;
    updateConsent: ConsentCallback;
    hasConsent: (type: keyof ConsentState) => boolean;
  };
  useExperiment: (experimentId: string) => {
    variant: string | null;
    isLoading: boolean;
    error: string | null;
  };
}
