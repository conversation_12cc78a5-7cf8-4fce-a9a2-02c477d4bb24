// app/credits/success/page.tsx
import { createClient } from '@/app/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { constructMetadata } from '@/lib/seo-config';

import Stripe from 'stripe';
import { SuccessClient } from './SuccessClient';

export const metadata: Metadata = constructMetadata({
  title: 'Payment Success | Job Space AI',
  description:
    'Your payment has been successfully processed. Thank you for your purchase.',
  path: '/credits/success'
});

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia'
});

export default async function SuccessPage({
  searchParams
}: Readonly<{
  searchParams: Promise<{ session_id?: string }>;
}>) {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError
  } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/signin');
  }

  const resolvedSearchParams = await searchParams;
  const stripeSessionId = resolvedSearchParams.session_id;

  if (!stripeSessionId) {
    redirect('/credits?error=missing_session');
  }

  try {
    const stripeSession =
      await stripe.checkout.sessions.retrieve(stripeSessionId);

    if (stripeSession.metadata?.userId !== user.id) {
      redirect('/credits?error=invalid_session');
    }

    return (
      <SuccessClient
        stripeSessionId={stripeSessionId}
        userId={user.id}
        amount={stripeSession.amount_total ?? 0}
        credits={parseInt(stripeSession.metadata?.credits ?? '0')}
        returnUrl={stripeSession.metadata?.returnUrl || '/credits'}
      />
    );
  } catch (error) {
    console.error('Stripe session verification error:', error);
    redirect('/credits?error=verification_failed');
  }
}
