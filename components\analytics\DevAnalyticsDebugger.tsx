// components/analytics/DevAnalyticsDebugger.tsx
'use client';

import { useEffect, useState } from 'react';
import { useAnalytics } from '@/lib/analytics-manager';

interface DebugInfo {
  gaReady: boolean;
  clarityReady: boolean;
  dataLayerLength: number;
  hasGtag: boolean;
  hasClarity: boolean;
  timestamp: string;
}

export default function DevAnalyticsDebugger() {
  const analytics = useAnalytics();
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    gaReady: false,
    clarityReady: false,
    dataLayerLength: 0,
    hasGtag: false,
    hasClarity: false,
    timestamp: new Date().toLocaleTimeString()
  });

  useEffect(() => {
    const updateDebugInfo = () => {
      setDebugInfo({
        gaReady: analytics.isGAReady(),
        clarityReady: analytics.isClarityReady(),
        dataLayerLength: window.dataLayer?.length || 0,
        hasGtag: typeof window.gtag === 'function',
        hasClarity: typeof window.clarity === 'function',
        timestamp: new Date().toLocaleTimeString()
      });
    };

    // Update immediately
    updateDebugInfo();

    // Update every 2 seconds
    const interval = setInterval(updateDebugInfo, 2000);

    return () => clearInterval(interval);
  }, []); // ✅ Empty dependency array - analytics functions are stable

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-black/90 text-white p-3 text-xs z-50 rounded-lg opacity-75 hover:opacity-100 transition-opacity max-w-xs">
      <div className="font-bold mb-2">📊 Analytics Debug</div>
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>GA Ready:</span>
          <span
            className={debugInfo.gaReady ? 'text-green-400' : 'text-red-400'}
          >
            {debugInfo.gaReady ? '✅' : '❌'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>Clarity Ready:</span>
          <span
            className={
              debugInfo.clarityReady ? 'text-green-400' : 'text-red-400'
            }
          >
            {debugInfo.clarityReady ? '✅' : '❌'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>DataLayer:</span>
          <span className="text-blue-400">
            {debugInfo.dataLayerLength} items
          </span>
        </div>
        <div className="flex justify-between">
          <span>gtag:</span>
          <span
            className={debugInfo.hasGtag ? 'text-green-400' : 'text-red-400'}
          >
            {debugInfo.hasGtag ? '✅' : '❌'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>clarity:</span>
          <span
            className={debugInfo.hasClarity ? 'text-green-400' : 'text-red-400'}
          >
            {debugInfo.hasClarity ? '✅' : '❌'}
          </span>
        </div>
        <div className="text-gray-400 text-xs pt-1 border-t border-gray-600">
          Updated: {debugInfo.timestamp}
        </div>
      </div>
    </div>
  );
}
