import React, { useState } from 'react';
import { get } from '@/app/utils/get';
import {
  PlanType,
  BasicPlanResult,
  ProPlanResult,
  UltimatePlanResult,
  Job,
  Resume,
  ComprehensiveAnalysisResult,
  DashboardCoverLetterResult,
  SkillsGapResult,
  SkillImprovementResponse
} from '@/app/types/globalTypes';
import InterviewDialog from '../interview/InterviewDialog';
import { DebugInfo } from './DebugComponents';
import { FeatureRenderer } from '@/components/shared/FeatureRenderer';
import { Card } from '@/components/ui/card';
import { ThemedButton } from '@/components/ui/themed-button';
import { Book, Code, FileText, ExternalLink } from 'lucide-react';
import { SHOW_MARKET_TRENDS } from '@/lib/featureFlags';

interface ResultsDisplayProps {
  results: BasicPlanResult | ProPlanResult | UltimatePlanResult;
  selectedPlan: string;
  job: Job;
  resume: Resume;
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  results,
  selectedPlan,
  job,
  resume
}) => {
  const [isInterviewDialogOpen, setIsInterviewDialogOpen] = useState(false);

  if (!results) return null;

  const skillsGap =
    get<
      BasicPlanResult | ProPlanResult | UltimatePlanResult,
      SkillsGapResult[]
    >(results, 'skillsGap', 'skills_gap') ?? [];

  const skillImprovements =
    get<
      BasicPlanResult | ProPlanResult | UltimatePlanResult,
      { skill_resources: SkillImprovementResponse[] }
    >(results, 'skillImprovements', 'skill_improvements') ?? null;

  const hasSkillsGap = (
    _: BasicPlanResult | ProPlanResult | UltimatePlanResult
  ): _ is ProPlanResult | UltimatePlanResult =>
    skillsGap.length > 0 || skillImprovements !== null;

  const isUltimatePlan = (
    results: BasicPlanResult | ProPlanResult | UltimatePlanResult
  ): results is UltimatePlanResult => {
    return results.planType === PlanType.ULTIMATE;
  };

  const getResourceIcon = (type: string | undefined) => {
    // Handle undefined case with a default value
    const resourceType = type?.toLowerCase() || 'default';

    switch (resourceType) {
      case 'course':
        return <Book className="h-5 w-5" />;
      case 'project':
        return <Code className="h-5 w-5" />;
      case 'documentation':
        return <FileText className="h-5 w-5" />;
      default:
        return <Book className="h-5 w-5" />;
    }
  };

  return (
    <div className="relative isolate space-y-8">
      {/* Using global background from LayoutWrapper instead of duplicating here */}

      <DebugInfo
        selectedPlan={selectedPlan as PlanType}
        results={results}
        effectivePlanType={results.planType}
      />

      {results.atsResult && (
        <FeatureRenderer
          type="ats"
          result={results.atsResult as unknown as ComprehensiveAnalysisResult}
          mode="card"
          job={job}
          resume={resume}
        />
      )}

      {results.coverLetter && (
        <FeatureRenderer
          type="coverLetter"
          result={results.coverLetter as unknown as DashboardCoverLetterResult}
          mode="card"
          job={job}
          resume={resume}
        />
      )}

      {hasSkillsGap(results) && results.skillsGap && (
        <FeatureRenderer
          type="skillsGap"
          result={
            {
              skills_gaps: results.skillsGap,
              match_percentage: 0
            } as SkillsGapResult
          }
          mode="card"
          job={job}
          resume={resume}
        />
      )}

      {/* Debug skill improvements */}
      {(() => {
        // Use IIFE to avoid React node issues with console.log
        console.log('Skill Improvements:', {
          hasSkillsGap: hasSkillsGap(results),
          hasSkillImprovements:
            hasSkillsGap(results) && !!results.skillImprovements,
          skillImprovementsType: hasSkillsGap(results)
            ? typeof results.skillImprovements
            : 'N/A',
          isArray:
            hasSkillsGap(results) && Array.isArray(results.skillImprovements),
          length:
            hasSkillsGap(results) && Array.isArray(results.skillImprovements)
              ? results.skillImprovements.length
              : 'N/A',
          data: hasSkillsGap(results) ? results.skillImprovements : null
        });
        return null; // Return null to avoid rendering issues
      })()}

      {/* Combine all skill improvements into a single component */}
      {hasSkillsGap(results) && results.skillImprovements && (
        <div className="rounded-lg border backdrop-blur-sm bg-black/90 border-gray-800 shadow-lg overflow-hidden mb-8">
          <div className="p-6 pb-4 flex flex-row items-center justify-between border-b border-gray-800 bg-gradient-to-r from-blue-900/30 to-black">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Skill Improvement Resources
              </h2>
              <p className="text-sm text-slate-300 mt-1">
                Resources to help you develop the skills you need
              </p>
            </div>
          </div>
          <div className="px-6 py-6 backdrop-blur-sm bg-black/90">
            {/* For Pro plan with a single skill improvement response */}
            {!Array.isArray(results.skillImprovements) &&
              results.skillImprovements.skill_resources && (
                <>
                  {results.skillImprovements.skill_resources.map(
                    (skillGroup, groupIndex) => (
                      <div key={groupIndex} className="mb-8 last:mb-0">
                        <h3 className="text-xl font-semibold mb-4 text-white">
                          {skillGroup.skill}
                        </h3>
                        <div className="grid gap-4 md:grid-cols-2">
                          {skillGroup.resources.map(
                            (resource, resourceIndex) => {
                              const resourceUrl = resource.url || resource.link;
                              const handleCardClick = () => {
                                if (resourceUrl) {
                                  window.open(
                                    resourceUrl,
                                    '_blank',
                                    'noopener,noreferrer'
                                  );
                                }
                              };
                              return (
                                <Card
                                  key={resourceIndex}
                                  interactive
                                  className="p-4 cursor-pointer backdrop-blur-sm bg-black/80 border-gray-800 hover:bg-black/90 hover:border-[hsl(var(--hero-yellow))]/50 hover:shadow-[0_0_15px_rgba(246,160,60,0.2)]"
                                  onClick={handleCardClick}
                                >
                                  <div className="flex items-start gap-3">
                                    <div className="mt-1 flex-shrink-0 text-[hsl(var(--hero-yellow))]">
                                      {getResourceIcon(resource.type)}
                                    </div>
                                    <div className="flex-1">
                                      <div className="flex items-start justify-between">
                                        <span className="text-[hsl(var(--hero-yellow))] font-medium inline-flex items-center gap-1 hover:text-[hsl(var(--hero-yellow-light))]">
                                          {resource.title}
                                          <ExternalLink className="h-3 w-3" />
                                        </span>
                                        {resource.difficulty && (
                                          <span className="text-xs px-2 py-1 rounded bg-[hsl(var(--hero-yellow))]/20 text-[hsl(var(--hero-yellow))]">
                                            {resource.difficulty}
                                          </span>
                                        )}
                                      </div>
                                      <p className="text-sm text-slate-300 mt-1">
                                        {resource.description}
                                      </p>
                                      {resource.type && (
                                        <p className="text-xs text-slate-400 mt-2">
                                          Type: {resource.type}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </Card>
                              );
                            }
                          )}
                        </div>
                      </div>
                    )
                  )}
                </>
              )}

            {/* For Ultimate plan with multiple skill improvement responses */}
            {Array.isArray(results.skillImprovements) && (
              <>
                {results.skillImprovements.flatMap(
                  (skillImprovement, responseIndex) =>
                    skillImprovement.skill_resources.map(
                      (skillGroup, groupIndex) => (
                        <div
                          key={`${responseIndex}-${groupIndex}`}
                          className="mb-8 last:mb-0"
                        >
                          <h3 className="text-xl font-semibold mb-4 text-white">
                            {skillGroup.skill}
                          </h3>
                          <div className="grid gap-4 md:grid-cols-2">
                            {skillGroup.resources.map(
                              (resource, resourceIndex) => {
                                const resourceUrl =
                                  resource.url || resource.link;
                                const handleCardClick = () => {
                                  if (resourceUrl) {
                                    window.open(
                                      resourceUrl,
                                      '_blank',
                                      'noopener,noreferrer'
                                    );
                                  }
                                };
                                return (
                                  <Card
                                    key={resourceIndex}
                                    interactive
                                    className="p-4 cursor-pointer backdrop-blur-sm bg-black/80 border-gray-800 hover:bg-black/90 hover:border-[hsl(var(--hero-yellow))]/50 hover:shadow-[0_0_15px_rgba(246,160,60,0.2)]"
                                    onClick={handleCardClick}
                                  >
                                    <div className="flex items-start gap-3">
                                      <div className="mt-1 flex-shrink-0 text-[hsl(var(--hero-yellow))]">
                                        {getResourceIcon(resource.type)}
                                      </div>
                                      <div className="flex-1">
                                        <div className="flex items-start justify-between">
                                          <span className="text-[hsl(var(--hero-yellow))] font-medium inline-flex items-center gap-1 hover:text-[hsl(var(--hero-yellow-light))]">
                                            {resource.title}
                                            <ExternalLink className="h-3 w-3" />
                                          </span>
                                          {resource.difficulty && (
                                            <span className="text-xs px-2 py-1 rounded bg-[hsl(var(--hero-yellow))]/20 text-[hsl(var(--hero-yellow))]">
                                              {resource.difficulty}
                                            </span>
                                          )}
                                        </div>
                                        <p className="text-sm text-slate-300 mt-1">
                                          {resource.description}
                                        </p>
                                        {resource.type && (
                                          <p className="text-xs text-slate-400 mt-2">
                                            Type: {resource.type}
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                  </Card>
                                );
                              }
                            )}
                          </div>
                        </div>
                      )
                    )
                )}
              </>
            )}
          </div>
        </div>
      )}

      {SHOW_MARKET_TRENDS &&
        isUltimatePlan(results) &&
        results.marketTrends && (
          <FeatureRenderer
            type="marketTrends"
            result={results.marketTrends}
            mode="card"
            job={job}
            resume={resume}
          />
        )}

      {isUltimatePlan(results) && results.careerMatches && (
        <FeatureRenderer
          type="careerMatching"
          result={results.careerMatches}
          mode="card"
          job={job}
          resume={resume}
        />
      )}

      {isUltimatePlan(results) && results.careerCoaching && (
        <FeatureRenderer
          type="careerCoach"
          result={results.careerCoaching}
          mode="card"
          job={job}
          resume={resume}
        />
      )}

      {hasSkillsGap(results) && results.mockInterview && (
        <div className="rounded-lg border backdrop-blur-sm bg-black/90 border-gray-800 shadow-lg overflow-hidden mb-8">
          <div className="p-6 pb-4 flex flex-row items-center justify-between border-b border-gray-800 bg-gradient-to-r from-blue-900/30 to-black">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Mock Interview
              </h2>
              <p className="text-sm text-slate-300 mt-1">
                Practice interview questions for your target role
              </p>
            </div>
          </div>

          <div className="px-6 py-6 backdrop-blur-sm bg-black/90">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-white">
                  Welcome to Your Mock Interview
                </h3>
                <p className="text-slate-300">
                  This AI-powered mock interview will help you practice and
                  prepare for your actual job interview.
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-white">How it works:</h4>
                <ul className="list-disc pl-5 space-y-2 text-slate-300">
                  <li>
                    You&apos;ll be asked a series of relevant interview
                    questions based on the job requirements
                  </li>
                  <li>Take your time to think and respond to each question</li>
                  <li>
                    You can navigate between questions using the Previous and
                    Next buttons
                  </li>
                  <li>
                    At the end, you&apos;ll receive feedback on your performance
                  </li>
                </ul>
              </div>

              <div className="pt-4">
                <ThemedButton
                  onClick={() => setIsInterviewDialogOpen(true)}
                  variant="primary"
                  fullWidth
                >
                  Start Interview
                </ThemedButton>
              </div>
            </div>

            <InterviewDialog
              isOpen={isInterviewDialogOpen}
              onClose={() => setIsInterviewDialogOpen(false)}
              resume={resume}
              job={job}
              skipSetup={false}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsDisplay;
