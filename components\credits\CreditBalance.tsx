import { useEffect, useRef, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { animate, motion } from 'framer-motion';
import { Sparkles, TrendingUp, History, RefreshCw } from 'lucide-react';
// Import useClientOnly to prevent hydration errors
import { useClientOnly } from '@/hooks/useClientOnly';
import { getBaseURL } from '@/app/utils/helpers';

interface CreditBalanceProps {
  credits: number | undefined;
  isLoading: boolean;
  error: string | null;
}

export function CreditBalance({
  credits: initialCredits,
  isLoading: initialLoading,
  error: initialError
}: Readonly<CreditBalanceProps>) {
  // Use internal state to track credits that can be updated by events
  const [credits, setCredits] = useState<number | undefined>(initialCredits);
  const [isLoading, setIsLoading] = useState<boolean>(initialLoading);
  const [error, setError] = useState<string | null>(initialError);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  // Use the useClientOnly hook to prevent hydration errors
  const isClient = useClientOnly();

  const creditRef = useRef<HTMLSpanElement>(null);
  const prevCredits = useRef<number | undefined>(0);

  // Update internal state when props change
  useEffect(() => {
    setCredits(initialCredits);
    setIsLoading(initialLoading);
    setError(initialError);
  }, [initialCredits, initialLoading, initialError]);

  // Listen for credit updates from localStorage and custom events
  useEffect(() => {
    const handleCreditUpdate = (e: Event) => {
      console.log('Credit update event received in CreditBalance');

      // For custom credit-update events
      if (e instanceof CustomEvent && e.detail?.credits !== undefined) {
        console.log('Setting credits from custom event:', e.detail.credits);
        setCredits(e.detail.credits);
        setLastUpdated(new Date());
        return;
      }

      // For storage events or direct calls
      try {
        // Try to get credits from localStorage
        const storedCredits = localStorage.getItem('credits');
        if (storedCredits) {
          const parsedCredits = parseInt(storedCredits, 10);
          if (!isNaN(parsedCredits)) {
            console.log('Setting credits from localStorage:', parsedCredits);
            setCredits(parsedCredits);
            setLastUpdated(new Date());
          }
        }

        // Also fetch from API for the most up-to-date value
        setIsLoading(true);
        // Use the helper function to get the base URL
        fetch(`${getBaseURL()}/api/credits`)
          .then((response) => {
            if (!response.ok) throw new Error('Failed to fetch credits');
            return response.json();
          })
          .then((data) => {
            if (data.credits !== undefined) {
              console.log('Setting credits from API:', data.credits);
              setCredits(data.credits);
              setLastUpdated(new Date());
            }
          })
          .catch((err) => {
            console.error('Error fetching credits:', err);
            setError('Failed to fetch credits');
          })
          .finally(() => {
            setIsLoading(false);
          });
      } catch (err) {
        console.error('Error handling credit update:', err);
      }
    };

    // Listen for both storage and custom events
    window.addEventListener('storage', handleCreditUpdate);
    window.addEventListener('credit-update', handleCreditUpdate);

    // Fetch credits on mount
    handleCreditUpdate(new Event('mount'));

    return () => {
      window.removeEventListener('storage', handleCreditUpdate);
      window.removeEventListener('credit-update', handleCreditUpdate);
    };
  }, []);

  // Animate credit changes
  useEffect(() => {
    if (credits !== undefined) {
      const node = creditRef.current;

      if (node && prevCredits.current !== credits) {
        animate(
          prevCredits.current === undefined ? 0 : prevCredits.current,
          credits,
          {
            duration: 0.5,
            onUpdate: (value) => {
              node.textContent = Math.floor(value).toString();
            }
          }
        );

        prevCredits.current = credits;
      }
    }
  }, [credits]);

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="bg-white/10 text-white overflow-hidden relative backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300 rounded-xl">
          <CardContent className="p-8 flex items-center justify-center">
            <div className="flex flex-col items-center gap-3">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              >
                <RefreshCw className="h-6 w-6 text-[hsl(var(--hero-yellow))]" />
              </motion.div>
              <motion.span
                className="text-slate-300"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                Loading credits...
              </motion.span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // Function to manually refresh credits
  const refreshCredits = () => {
    console.log('Manual credit refresh requested');
    setIsLoading(true);
    // Use the helper function to get the base URL
    fetch(`${getBaseURL()}/api/credits`)
      .then((response) => {
        if (!response.ok) throw new Error('Failed to fetch credits');
        return response.json();
      })
      .then((data) => {
        if (data.credits !== undefined) {
          console.log('Credits refreshed manually:', data.credits);
          setCredits(data.credits);
          setLastUpdated(new Date());

          // Also update localStorage and dispatch events
          localStorage.setItem('credits', data.credits.toString());
          window.dispatchEvent(
            new CustomEvent('credit-update', {
              detail: { credits: data.credits }
            })
          );
        }
      })
      .catch((err) => {
        console.error('Error refreshing credits:', err);
        setError('Failed to refresh credits');
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="bg-white/10 text-white overflow-hidden relative backdrop-blur-sm border border-red-500/30 shadow-lg hover:shadow-red-500/10 transition-all duration-300 rounded-xl">
          <CardContent className="p-8 text-center">
            <motion.div
              className="flex flex-col items-center gap-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              <div className="text-red-400 font-medium">
                Error loading credits
              </div>
              <div className="text-sm text-slate-300">{error}</div>
              <motion.button
                onClick={refreshCredits}
                className="mt-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-md text-sm transition-colors shadow-[0_0_10px_rgba(239,68,68,0.2)]"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Try Again
              </motion.button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <Card className="bg-white/10 text-white overflow-hidden relative backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300 rounded-xl h-[320px]">
        <CardContent className="pt-6 pb-6 relative">
          <div className="text-center space-y-5">
            <motion.div
              className="flex items-center justify-center gap-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Sparkles className="h-5 w-5 text-[hsl(var(--hero-yellow))]" />
              <h2 className="text-lg font-medium text-white">
                Available Credits
              </h2>
              <button
                onClick={refreshCredits}
                className="ml-1 p-1 rounded-full hover:bg-white/10 transition-colors"
                title="Refresh credits"
              >
                <RefreshCw className="h-4 w-4 text-[hsl(var(--hero-yellow))]" />
              </button>
            </motion.div>

            <motion.div
              className="relative py-4"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 100 }}
            >
              <div className="absolute inset-0 bg-[hsl(var(--hero-yellow))]/10 blur-xl rounded-full" />
              <div className="relative flex items-center justify-center space-x-3">
                <span
                  ref={creditRef}
                  className="text-6xl font-bold tabular-nums tracking-tight text-[hsl(var(--hero-yellow))]"
                  style={{ textShadow: '0 0 20px rgba(246, 160, 60, 0.4)' }}
                >
                  {credits || 0}
                </span>
                <span className="text-lg text-white/90 font-medium">
                  credits
                </span>
              </div>
            </motion.div>

            <div className="w-full px-4 mt-4">
              <div className="border-t border-white/20 w-full"></div>
            </div>

            <motion.div
              className="grid grid-cols-2 gap-4 pt-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center justify-center gap-2 text-slate-300 pt-3">
                <TrendingUp className="h-4 w-4 text-[hsl(var(--hero-yellow))]" />
                <span className="text-sm">Premium Access</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-slate-300 pt-3">
                <History className="h-4 w-4 text-[hsl(var(--hero-yellow))]" />
                <span className="text-sm">Never Expires</span>
              </div>
            </motion.div>

            {/* Only render the timestamp on the client side to prevent hydration errors */}
            {isClient && (
              <motion.div
                className="text-xs text-white/40 mt-1"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                Last updated:{' '}
                {lastUpdated.toLocaleString(undefined, {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false
                })}
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export default CreditBalance;
