'use client';

import { motion } from 'framer-motion';
import { Star } from 'lucide-react';
import Image from 'next/image';

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Software Engineer',
    company: 'Tech Giant Corp',
    image: '/images/testimonials/sarah.jpg',
    content:
      'The AI-powered CV analysis helped me optimize my application and land my dream job at a top tech company. The skill gap analysis was incredibly accurate!',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Marketing Director',
    company: 'Creative Agency',
    image: '/images/testimonials/james.jpg',
    content:
      "Using Job Space AI doubled my interview callback rate. The platform's insights helped me highlight the right experiences and skills for each application.",
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Product Manager',
    company: 'Startup Inc',
    image: '/images/testimonials/ian.jpg',
    content:
      'The interview preparation tools were game-changing. I felt so much more confident, and it showed in my interviews. Landed a great position within weeks!',
    rating: 5
  }
];

export const Testimonials = () => {
  return (
    <section className="relative isolate py-16 overflow-hidden">
      {/* Background with blobs similar to Hero */}
      <div className="absolute inset-0 bg-hero-bg/95 home-section-bg">
        {/* Subtle background features */}
        <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-hero-blue-glow blur-3xl animate-blob"></div>
        <div className="absolute bottom-1/3 left-1/3 w-80 h-80 rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000"></div>
        <div className="absolute top-2/3 right-1/2 w-72 h-72 rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000"></div>
        <div className="absolute top-1/3 left-1/4 w-56 h-56 rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000"></div>
      </div>

      <div className="relative z-10 mx-auto w-full max-w-[1200px] px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <span className="text-yellow-300 font-semibold">TESTIMONIALS</span>
          <h2 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
            Success{' '}
            <span className="text-yellow-400 text-shadow-yellow">Stories</span>
          </h2>
          <p className="text-slate-200 max-w-2xl mx-auto text-[16px] md:text-[18px]">
            Join thousands of professionals who have transformed their careers
            using our AI-powered platform
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="backdrop-blur-sm bg-white/10 border border-white/20 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02] cursor-pointer"
            >
              <div className="flex items-center mb-4">
                <div className="relative w-12 h-12 mr-4">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    fill
                    sizes="(max-width: 768px) 48px, 48px"
                    className="rounded-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-semibold text-white">
                    {testimonial.name}
                  </h3>
                  <p className="text-sm text-yellow-300">
                    {testimonial.role} at {testimonial.company}
                  </p>
                </div>
              </div>
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-4 h-4 fill-yellow-400 text-yellow-400"
                  />
                ))}
              </div>
              <p className="text-slate-200">{testimonial.content}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
