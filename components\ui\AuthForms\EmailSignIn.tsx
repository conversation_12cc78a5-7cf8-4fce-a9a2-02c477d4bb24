'use client';

import { ThemedButton } from '@/components/ui/themed-button';
import Link from 'next/link';
import { signInWithEmail } from '@/app/utils/auth-helpers/server';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Input } from '../input';
import FormAlert from './FormAlert';
import { trackEvent, trackCTAClick } from '@/lib/ga-events';

// Define prop type with allowPassword boolean
interface EmailSignInProps {
  allowPassword: boolean;
  redirectMethod: string;
  disableButton?: boolean;
  next?: string;
}

export default function EmailSignIn({
  allowPassword,
  disableButton,
  next
}: Readonly<EmailSignInProps>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const searchParams = useSearchParams();

  // Get status and error messages from URL parameters
  const status = searchParams.get('status');
  const statusDescription = searchParams.get('status_description');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const isFromSignup = searchParams.get('signup') === 'true';

  // 🆕 Track page view when component mounts
  useEffect(() => {
    // Track magic link signin page view
    trackEvent('page_view', {
      page_title: 'Magic Link Sign In',
      user_journey: 'dashboard_direct',
      signin_method: 'magic_link',
      from_signup: isFromSignup
    });

    // Track if user came from signup flow
    if (isFromSignup) {
      trackEvent('post_signup_flow', {
        event_category: 'authentication',
        flow_type: 'signup_to_signin',
        signin_method: 'magic_link'
      });
    }
  }, [isFromSignup]);

  // Reset loading state on mount and clear on unmount
  useEffect(() => {
    // Reset loading state when component mounts
    setIsSubmitting(false);

    // Clear loading state when component unmounts
    return () => {
      if (isSubmitting) {
        console.log('Clearing loading state on unmount');
        setIsSubmitting(false);
      }
    };
  }, [isSubmitting]);

  // Set up safety timeout to clear loading state
  useEffect(() => {
    let safetyTimeout: NodeJS.Timeout | null = null;

    if (isSubmitting) {
      safetyTimeout = setTimeout(() => {
        console.log('Safety timeout triggered to clear loading state');
        setIsSubmitting(false);
      }, 5000); // 5 seconds max wait time
    }

    return () => {
      if (safetyTimeout) {
        clearTimeout(safetyTimeout);
      }
    };
  }, [isSubmitting]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // If already submitting, don't allow another submission
    if (isSubmitting) return;

    // 🆕 Track magic link signin attempt
    trackEvent('login_attempt', {
      event_category: 'authentication',
      method: 'magic_link',
      user_journey: 'dashboard_direct',
      from_signup: isFromSignup
    });

    trackCTAClick('magic_link_signin', 'signin_form', 'Sign in');

    setIsSubmitting(true);

    // Create a local variable to track if we should update state
    // This helps prevent state updates after component unmount
    let isMounted = true;

    try {
      // Use a direct approach instead of handleRequest
      const formData = new FormData(e.currentTarget);

      // Get next parameter from URL if present
      const nextParam =
        next || new URLSearchParams(window.location.search).get('next');
      if (nextParam) {
        formData.append('next', nextParam);
      }

      // Call the server action directly
      const redirectPath = await signInWithEmail(formData);

      // 🆕 Track successful magic link request
      trackEvent('magic_link_sent', {
        event_category: 'authentication',
        method: 'magic_link',
        user_journey: 'dashboard_direct',
        success: true
      });

      // Clear any logged out flag
      try {
        sessionStorage.removeItem('logged_out');
      } catch (e) {
        console.error('Error clearing sessionStorage:', e);
      }

      // Navigate immediately to avoid getting stuck
      console.log('Redirecting to:', redirectPath);
      window.location.replace(redirectPath);

      // Don't clear loading state since we're navigating away
    } catch (error) {
      console.error('Error during email sign-in:', error);

      // 🆕 Track magic link failure
      trackEvent('login_failure', {
        event_category: 'error',
        method: 'magic_link',
        error_type: 'magic_link_failed',
        error_message:
          error instanceof Error
            ? error.message.substring(0, 100)
            : 'Unknown error',
        user_journey: 'dashboard_direct'
      });

      if (isMounted) {
        setIsSubmitting(false);
      }
    }

    // Set cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  };

  return (
    <div className="my-4 text-white">
      {/* Use FormAlert component */}
      <FormAlert
        status={status}
        statusDescription={statusDescription}
        error={error}
        errorDescription={errorDescription}
      />

      {/* Show additional context for users coming from signup */}
      {isFromSignup && !error && (
        <div className="mb-4 p-3 bg-blue-50/20 backdrop-blur-sm border border-blue-200/30 rounded-md">
          <p className="text-sm text-blue-100">
            ✨ <strong>Account created successfully!</strong> Once you confirm
            your email, you can sign in below or use the magic link option.
          </p>
        </div>
      )}

      <form
        noValidate={true}
        className="mb-4"
        onSubmit={(e) => handleSubmit(e)}
      >
        <div className="grid gap-2">
          <div className="grid gap-1">
            <label htmlFor="email" className="text-white">
              Email
            </label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              name="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              className="w-full p-3 rounded-md bg-white/10 border-white/20 text-white auth-input"
            />
          </div>
          <ThemedButton
            type="submit"
            variant="primary"
            loading={isSubmitting}
            disabled={disableButton}
            className="mt-1"
          >
            Sign in
          </ThemedButton>
        </div>
      </form>
      {allowPassword && (
        <>
          <p>
            <Link
              href={`/signin/password_signin?next=${encodeURIComponent(next ?? '/dashboard')}`}
              className="font-light text-sm text-[hsl(var(--hero-yellow))] hover:underline"
              onClick={() => {
                // 🆕 Track navigation to password signin
                trackCTAClick(
                  'navigation_password_signin',
                  'signin_links',
                  'Sign in with email and password'
                );
                trackEvent('signin_method_switch', {
                  event_category: 'authentication',
                  from_method: 'magic_link',
                  to_method: 'password',
                  user_journey: 'dashboard_direct'
                });
              }}
            >
              Sign in with email and password
            </Link>
          </p>
          <p>
            <Link
              href={`/signin/signup?next=${encodeURIComponent(next ?? '/dashboard')}`}
              className="font-light text-sm text-[hsl(var(--hero-yellow))] hover:underline"
              onClick={() => {
                // 🆕 Track navigation to signup
                trackCTAClick(
                  'navigation_signup',
                  'signin_links',
                  "Don't have an account? Sign up"
                );
                trackEvent('signin_to_signup_flow', {
                  event_category: 'authentication',
                  from_page: 'magic_link_signin',
                  user_journey: 'onboarding'
                });
              }}
            >
              Don&apos;t have an account? Sign up
            </Link>
          </p>
        </>
      )}
    </div>
  );
}
