'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/utils/supabase/client';
import { User } from '@supabase/supabase-js';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { ThemedButton } from '@/components/ui/themed-button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { signOut } from '@/app/actions/auth';

const UserSection = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const router = useRouter();
  const supabase = createClient();
  const { toast } = useToast();

  // Define checkSession as a useCallback to avoid recreation on each render
  const checkSession = useCallback(async () => {
    try {
      console.log('Checking session...');
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error checking session:', error);
        setUser(null);
        return;
      }

      if (data?.session) {
        console.log('Session found, user:', data.session.user.email);
        setUser(data.session.user);
      } else {
        console.log('No active session found');
        setUser(null);
      }
    } catch (e) {
      console.error('Unexpected error checking session:', e);
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [supabase.auth]);

  useEffect(() => {
    // Check session on component mount
    checkSession();

    // Set up auth state change listener
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (session) {
        setUser(session.user);
      } else {
        setUser(null);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [checkSession, supabase.auth]);

  const handleSignIn = () => {
    // Set next param to dashboard for redirect after login
    const nextParam = encodeURIComponent('dashboard');

    // Mark that we're trying to log in (for session detection later)
    localStorage.setItem('auth_redirect_pending', 'true');

    // Add a small delay before navigation to ensure the menu closes first
    setTimeout(() => {
      // Navigate to login page
      router.push(`/signin/password_signin?next=${nextParam}`);
    }, 100);
  };

  const handleSignOut = async () => {
    try {
      setLoading(true);
      setDropdownOpen(false);

      // Clear all application state from localStorage
      localStorage.clear();

      // Sign out with Supabase client first to clear client-side auth state
      await supabase.auth.signOut();

      // Clear user state
      setUser(null);

      // Try the server action to clear cookies
      try {
        const redirectUrl = await signOut();

        // Success message
        toast({
          title: 'Signed Out',
          description: 'You have been successfully logged out.'
        });

        // Force page refresh to clear any cached state
        window.location.href = redirectUrl || '/';
      } catch (serverError) {
        console.error('Error with server-side logout:', serverError);

        // We've already done client-side logout, just redirect
        toast({
          title: 'Signed Out',
          description: 'You have been successfully logged out.'
        });
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Error with client-side logout:', error);
      toast({
        title: 'Error',
        description: 'Failed to sign out. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleNavigation = (path: string) => {
    setDropdownOpen(false);
    router.push(path);
  };

  const handleDropdownToggle = () => {
    console.log('Dropdown toggle clicked, current state:', dropdownOpen);
    setDropdownOpen(!dropdownOpen);
  };

  // Simple test version using regular Button
  const UserMenu = () => (
    <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-full hidden sm:flex border border-white/20 bg-white/10 hover:bg-white/20 text-white"
          onClick={handleDropdownToggle}
        >
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={user?.user_metadata?.avatar_url}
              alt={`Profile picture for ${user?.email || 'user'}`}
            />
            <AvatarFallback className="bg-hero-yellow text-[#111827] text-sm">
              {user?.email ? user.email.charAt(0).toUpperCase() : 'U'}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-48 bg-hero-bg/95 backdrop-blur-sm border border-white/20 text-white"
        side="bottom"
        sideOffset={5}
      >
        {user?.email && (
          <div className="px-2 py-1.5 text-sm text-slate-200 break-all border-b border-white/10">
            {user.email}
          </div>
        )}
        {user?.id && (
          <DropdownMenuItem
            onSelect={() => handleNavigation(`/dashboard/${user.id}`)}
            className="text-white hover:text-[hsl(var(--hero-yellow))] hover:bg-white/10 focus:bg-white/10 cursor-pointer group"
          >
            <span className="group-hover:text-[hsl(var(--hero-yellow))]">
              Dashboard
            </span>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem
          onSelect={handleSignOut}
          className="text-white hover:text-[hsl(var(--hero-yellow))] hover:bg-white/10 focus:bg-white/10 cursor-pointer group"
        >
          <span className="group-hover:text-[hsl(var(--hero-yellow))]">
            Sign Out
          </span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <>
      {loading ? (
        <Button
          variant="ghost"
          size="sm"
          disabled
          className="hidden sm:inline-flex text-white"
        >
          Loading...
        </Button>
      ) : user ? (
        <>
          {/* Try the regular dropdown first */}
          <UserMenu />
          {/* Uncomment this and comment above if dropdown still doesn't work */}
          {/* <SimpleUserMenu /> */}
        </>
      ) : (
        <ThemedButton
          variant="primary"
          size="sm"
          onClick={handleSignIn}
          className="hidden sm:inline-flex font-semibold"
        >
          Login
        </ThemedButton>
      )}
    </>
  );
};

export default UserSection;
