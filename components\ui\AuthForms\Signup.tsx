'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ThemedButton } from '@/components/ui/themed-button';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { signUp } from '@/app/utils/auth-helpers/server';
import { signInWithOAuth } from '@/app/utils/auth-helpers/client';
import { trackFreeTrialSignup } from '@/lib/ga-events';
import {
  trackSignupAttempt,
  trackSignupFailure,
  trackCTAClick,
  trackOnboardingStart
} from '@/lib/ga-events';
import { Input } from '../input';
import { Linkedin, Sparkles, Shield, Clock } from 'lucide-react';
import GoogleIcon from '@/components/icons/google';
import { type Provider } from '@supabase/supabase-js';
import { useToast } from '@/hooks/use-toast';
import { useSearchParams } from 'next/navigation';
import FormAlert from './FormAlert';

interface SignUpProps {
  allowEmail: boolean;
  redirectMethod: string;
  next?: string;
}

type OAuthProviders = {
  name: Provider;
  displayName: string;
  icon: JSX.Element;
  bgColor: string;
  hoverColor: string;
  textColor: string;
};

export default function SignUp({ allowEmail, next }: Readonly<SignUpProps>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [oauthSubmitting, setOAuthSubmitting] = useState<string | null>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{
    email?: string;
    password?: string;
    general?: string;
  }>({});
  const { toast } = useToast();
  const searchParams = useSearchParams();

  // Get status and error messages from URL parameters
  const status = searchParams.get('status');
  const statusDescription = searchParams.get('status_description');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  // 🆕 Track onboarding start when component mounts
  useEffect(() => {
    trackOnboardingStart();
  }, []);

  // Validate form inputs
  const validateForm = (): boolean => {
    const newErrors: { email?: string; password?: string } = {};
    let isValid = true;

    // Email validation
    if (!email) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
      isValid = false;
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    // 🆕 Track signup attempt
    trackSignupAttempt('email');
    trackCTAClick(
      'email_signup',
      'signup_form',
      'Get Started with Free Credits'
    );

    setIsSubmitting(true);

    try {
      // build our own FormData to include the next
      const formData = new FormData(e.currentTarget);
      formData.append('next', next || '/dashboard');
      const redirectTo = await signUp(formData);
      trackFreeTrialSignup();
      window.location.replace(redirectTo);
    } catch (error) {
      // Handle error
      const errorMessage =
        error instanceof Error ? error.message : 'An unknown error occurred';

      // 🆕 Track signup failure
      trackSignupFailure('email', 'validation_error');

      // Show error in UI
      setErrors({ general: errorMessage });

      // Also show toast for better visibility
      toast({
        title: 'Sign up failed',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOAuthSubmit = async (
    e: React.FormEvent<HTMLFormElement>,
    providerName: string
  ) => {
    e.preventDefault();
    setOAuthSubmitting(providerName);

    // 🆕 Track OAuth signup attempt
    trackSignupAttempt(providerName);
    trackCTAClick(
      'oauth_signup',
      'signup_form',
      `Continue with ${providerName}`
    );

    const formData = new FormData(e.currentTarget);

    // Add next parameter to OAuth form data
    if (next) {
      formData.append('next', next);
    }

    try {
      await signInWithOAuth(formData);
      trackFreeTrialSignup();
      setOAuthSubmitting(null);
    } catch (error) {
      // 🆕 Track OAuth failure
      trackSignupFailure(providerName, 'oauth_error');
      console.error(`OAuth signup failed for ${providerName}:`, error);
      setOAuthSubmitting(null);
    }
  };

  const oAuthProviders: OAuthProviders[] = [
    {
      name: 'google',
      displayName: 'Continue with Google',
      icon: (
        <GoogleIcon className="h-5 w-5 group-hover:text-[hsl(var(--hero-yellow))]" />
      ),
      bgColor: 'bg-[hsl(var(--secondary))]',
      hoverColor: 'hover:bg-[hsl(var(--secondary))/90]',
      textColor:
        'text-[hsl(var(--secondary-foreground))] group-hover:text-[hsl(var(--hero-yellow))]'
    },
    {
      name: 'linkedin_oidc',
      displayName: 'Continue with LinkedIn',
      icon: (
        <Linkedin className="h-5 w-5 group-hover:text-[hsl(var(--hero-yellow))]" />
      ),
      bgColor: 'bg-[hsl(var(--secondary))]',
      hoverColor: 'hover:bg-[hsl(var(--secondary))/90]',
      textColor:
        'text-[hsl(var(--secondary-foreground))] group-hover:text-[hsl(var(--hero-yellow))]'
    }
  ];

  return (
    <Card className="w-full max-w-md mx-auto shadow-none border-0 bg-transparent">
      <CardHeader className="space-y-3 items-center pb-8">
        <div className="inline-flex items-center gap-2 bg-[hsl(var(--hero-yellow))]/10 backdrop-blur-sm px-4 py-1.5 rounded-full mb-2">
          <Sparkles className="w-4 h-4 text-[hsl(var(--hero-yellow))]" />
          <span className="text-sm font-medium text-[hsl(var(--hero-yellow))]">
            10 free credits included
          </span>
        </div>
        <h2 className="text-2xl font-semibold tracking-tight text-center text-white">
          Unlock Your Career Potential
        </h2>
        <p className="text-sm text-white/70 text-center max-w-sm">
          Join thousands of professionals using AI to land their dream jobs
        </p>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* URL-based alerts using FormAlert component */}
        <FormAlert
          status={status}
          statusDescription={statusDescription}
          error={error}
          errorDescription={errorDescription}
        />

        {/* Social Login Buttons */}
        <div className="space-y-3">
          {oAuthProviders.map((provider) => (
            <form
              key={provider.name}
              onSubmit={(e) => handleOAuthSubmit(e, provider.name)}
            >
              <Input type="hidden" name="provider" value={provider.name} />
              <Button
                type="submit"
                className={`w-full h-11 font-medium relative group ${provider.bgColor} ${provider.hoverColor} ${provider.textColor} transition-all duration-150 ease-in-out border hover:border-[hsl(var(--hero-yellow))]`}
                disabled={oauthSubmitting !== null}
              >
                <div className="flex items-center justify-center">
                  <span className="absolute left-4">{provider.icon}</span>
                  <span className="group-hover:text-[hsl(var(--hero-yellow))]">
                    {provider.displayName}
                  </span>
                </div>
                {oauthSubmitting === provider.name && (
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-t-2 border-[hsl(var(--hero-yellow))] rounded-full animate-spin" />
                  </div>
                )}
              </Button>
            </form>
          ))}
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-white/20" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-transparent text-white/70">
              Or continue with
            </span>
          </div>
        </div>

        {/* General Error Alert - Using FormAlert component for form-specific errors */}
        <FormAlert
          error={errors.general ? 'Sign up failed' : null}
          errorDescription={errors.general}
        />

        {/* Email/Password Form */}
        <form noValidate={true} className="space-y-3" onSubmit={handleSubmit}>
          <div className="space-y-3">
            <div className="space-y-2">
              <label
                htmlFor="email"
                className="text-sm font-medium leading-none text-white"
              >
                Email
              </label>
              <Input
                id="email"
                placeholder="<EMAIL>"
                type="email"
                name="email"
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect="off"
                className={`w-full bg-white/10 border-white/20 text-white auth-input ${errors.email ? 'border-red-500' : ''}`}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email}</p>
              )}
            </div>
            <div className="space-y-2">
              <label
                htmlFor="password"
                className="text-sm font-medium leading-none text-white"
              >
                Password
              </label>
              <Input
                id="password"
                placeholder="Create a password (min. 6 characters)"
                type="password"
                name="password"
                autoComplete="new-password"
                className={`w-full bg-white/10 border-white/20 text-white auth-input ${errors.password ? 'border-red-500' : ''}`}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">{errors.password}</p>
              )}
            </div>
          </div>
          <ThemedButton
            type="submit"
            variant="primary"
            fullWidth
            disabled={isSubmitting}
            className="font-medium py-6"
          >
            {isSubmitting ? 'Signing up...' : 'Get Started with Free Credits'}
          </ThemedButton>
        </form>

        <div className="flex items-center justify-center gap-4 mt-3">
          <div className="flex items-center gap-1 text-xs text-white/70">
            <Shield className="w-3 h-3" />
            <span>Secure Sign-up</span>
          </div>
          <div className="flex items-center gap-1 text-xs text-white/70">
            <Clock className="w-3 h-3" />
            <span>2-min Setup</span>
          </div>
        </div>

        <p className="text-center text-sm text-white/70 mt-2">
          Already have an account?{' '}
          <Link
            href={`/signin/password_signin?next=${encodeURIComponent(next ?? '/dashboard')}`}
            className="text-[hsl(var(--hero-yellow))] hover:underline font-medium"
            onClick={() => {
              // 🆕 Track navigation to signin
              trackCTAClick('navigation_signin', 'signup_form', 'Sign in');
            }}
          >
            Sign in
          </Link>
          {allowEmail && (
            <span className="ml-1">
              or{' '}
              <Link
                href={`/signin/email_signin?next=${encodeURIComponent(next ?? '/dashboard')}`}
                className="text-[hsl(var(--hero-yellow))] hover:underline font-medium"
                onClick={() => {
                  // 🆕 Track navigation to magic link signin
                  trackCTAClick(
                    'navigation_magic_link',
                    'signup_form',
                    'sign in via magic link'
                  );
                }}
              >
                sign in via magic link
              </Link>
            </span>
          )}
        </p>
      </CardContent>
    </Card>
  );
}
