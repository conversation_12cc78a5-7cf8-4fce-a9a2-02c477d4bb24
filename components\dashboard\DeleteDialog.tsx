import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { ThemedButton } from '@/components/ui/themed-button';

interface DeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const DeleteDialog: React.FC<DeleteDialogProps> = ({
  isOpen,
  onClose,
  onConfirm
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg">
        <DialogHeader>
          <DialogTitle className="text-white">Confirm Deletion</DialogTitle>
          <DialogDescription className="text-slate-300">
            Are you sure you want to delete this item? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="border-t border-white/20 pt-4 mt-4">
          <ThemedButton variant="secondary" onClick={onClose}>
            Cancel
          </ThemedButton>
          <ThemedButton variant="danger" onClick={onConfirm}>
            Delete
          </ThemedButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
