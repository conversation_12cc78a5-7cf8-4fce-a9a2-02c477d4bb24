// app/(marketing)/layout.tsx
import { ReactNode } from 'react';

export const dynamic = 'force-static'; // pre-render at build
export const revalidate = 86400; // 1-day ISR if you deploy via Vercel

export const metadata = {
  title: 'JobSpaceAI — AI Job-Application Platform',
  description: 'End-to-end AI tools for CVs, cover letters and interview prep.'
};

import Link from 'next/link';

import { FEATURES } from 'app/constants/features';

export default function MarketingLayout({ children }: { children: ReactNode }) {
  return (
    <>
      <div className="relative isolate min-h-screen overflow-hidden">
        <div className="fixed inset-0 -z-10 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-50"></div>
          <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] rounded-full bg-hero-purple-glow blur-3xl animate-blob delay-2000 opacity-50"></div>
          <div className="absolute top-2/3 left-1/2 w-[550px] h-[550px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob delay-4000 opacity-50"></div>
          <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob delay-6000 opacity-50"></div>
        </div>

        <main className="flex-grow relative z-10">{children}</main>

        <footer className="p-4 border-t text-xs text-center space-x-4 relative z-10">
          {FEATURES.filter((f) => f.public).map(({ slug, title }) => (
            <Link key={slug} href={`/${slug!}`} className="hover:underline">
              {title}
            </Link>
          ))}
        </footer>
      </div>
    </>
  );
}
