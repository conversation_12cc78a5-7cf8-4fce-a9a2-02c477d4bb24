'use client';
import React, { useState, useRef, useEffect } from 'react';
import type {
  ChatMessage,
  Resume,
  Job,
  ResumeAnalysisResult,
  CareerCoachingResponse
} from '@/app/types/globalTypes';
import { Button } from '@/components/ui/button';
import { Send, Loader2 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { performCareerCoaching } from '@/app/utils/careerCoaching';

interface AICareerCoachProps {
  // Support both new and legacy prop patterns
  careerCoachData?: CareerCoachingResponse | null;
  resumeData?: Resume;
  jobData?: Job;
  // Legacy props
  resumeAnalysis?: ResumeAnalysisResult;
  jobDescription?: string;
  // Common props
  errorMessage?: string | null;
  onSendMessage?: (
    message: string,
    chatHistory?: ChatMessage[]
  ) => Promise<CareerCoachingResponse>;
  isLoading?: boolean;
}

export const AICareerCoach: React.FC<AICareerCoachProps> = ({
  careerCoachData,
  resumeData,
  jobData,
  resumeAnalysis,
  jobDescription,
  errorMessage: externalError = null,
  onSendMessage,
  isLoading: externalLoading = false
}) => {
  // Log job data for debugging
  console.log(
    'AICareerCoach - Job Data:',
    jobData
      ? {
          id: jobData.id,
          title: jobData.title,
          company: jobData.company,
          descriptionLength: jobData.description?.length
        }
      : 'No job data'
  );
  const [input, setInput] = useState('');
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [localLoading, setLocalLoading] = useState(false);
  const [internalError, setInternalError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Reference to track if this is the first render
  const isFirstRender = useRef(true);

  const isLoading = externalLoading || localLoading;
  const displayError = externalError || internalError;

  // Instead of showing a message when careerCoachData is null,
  // we'll provide a default value to allow the component to render
  const initialData = careerCoachData || {
    answer: 'How can I help with your career questions today?',
    relevant_sections: [],
    confidence_score: 1.0,
    follow_up_questions: [
      'How do my skills match this job description?',
      'What should I emphasize in my interview?',
      'How can I address gaps in my experience?',
      'What skills should I highlight on my resume?'
    ],
    analysis: {},
    recommendations: []
  };

  // Track if this is the initial load
  const isInitialLoad = useRef(true);

  // Scroll to bottom only when new messages are added (not on initial load)
  useEffect(() => {
    // On initial load, force scroll to top
    if (isInitialLoad.current) {
      isInitialLoad.current = false;

      // Force scroll to top on initial load
      if (chatContainerRef.current) {
        console.log('AICareerCoach: Initial load - forcing scroll to top');
        chatContainerRef.current.scrollTop = 0;

        // Try multiple times to ensure it works
        setTimeout(() => {
          if (chatContainerRef.current) chatContainerRef.current.scrollTop = 0;
        }, 50);

        setTimeout(() => {
          if (chatContainerRef.current) chatContainerRef.current.scrollTop = 0;
        }, 200);
      }
      return;
    }

    if (messagesEndRef.current) {
      // Use a small timeout to ensure the DOM has updated
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'end'
        });

        // Also try to scroll the parent container
        if (messagesEndRef.current) {
          const chatContainer =
            messagesEndRef.current.closest('.overflow-y-scroll');
          if (chatContainer) {
            chatContainer.scrollTop = chatContainer.scrollHeight;
          }
        }
      }, 100);
    }
  }, [chatHistory]);

  // Reset chat history when component mounts or when job/resume changes
  useEffect(() => {
    console.log('AICareerCoach: Component mounted or job/resume changed');
    console.log(
      'Job data:',
      jobData
        ? { id: jobData.id, title: jobData.title, company: jobData.company }
        : 'No job data'
    );
    console.log(
      'Resume data:',
      resumeData ? { id: resumeData.id } : 'No resume data'
    );
    console.log(
      'Career coach data:',
      careerCoachData ? 'Present' : 'Not present'
    );

    // Always reset chat history when component mounts or job/resume changes
    setChatHistory([]);

    // Reset input field
    setInput('');

    // Reset error state
    setInternalError(null);

    // Skip if no job data is available
    if (!jobData?.id) return;

    const currentJobId = String(jobData.id);
    console.log(
      `Current job ID: ${currentJobId}, Title: ${jobData.title}, Company: ${jobData.company || 'unknown'}`
    );

    // Mark that we've handled the first render
    isFirstRender.current = false;
  }, [jobData?.id, resumeData?.id, careerCoachData, jobData, resumeData]);

  // Add the initial coach message if chat history is empty
  useEffect(() => {
    // Only add initial message if chat history is empty and we have an initial answer
    if (chatHistory.length === 0 && initialData.answer) {
      console.log('Adding initial message');

      const initialMessage = {
        role: 'assistant',
        content: initialData.answer
      };

      // Set the initial message in chat history
      setChatHistory([initialMessage]);
    }
  }, [chatHistory.length, initialData.answer]);

  // Reference to the chat container
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Scroll to top when component mounts - with multiple attempts to ensure it works
  useEffect(() => {
    // Function to force scroll to top
    const forceScrollToTop = () => {
      console.log('AICareerCoach: Forcing scroll to top');

      // Explicitly scroll the chat container to top
      if (chatContainerRef.current) {
        console.log('AICareerCoach: Scrolling chat container to top');
        chatContainerRef.current.scrollTop = 0;
      }

      // Also find any other scrollable containers and scroll them to top
      const scrollableContainers = document.querySelectorAll(
        '.overflow-y-scroll, .overflow-y-auto'
      );
      scrollableContainers.forEach((container) => {
        console.log('AICareerCoach: Scrolling container to top');
        (container as HTMLElement).scrollTop = 0;
      });

      // Also try to scroll parent containers
      let parent = chatContainerRef.current?.parentElement;
      while (parent) {
        parent.scrollTop = 0;
        parent = parent.parentElement;
      }
    };

    // Immediate scroll
    forceScrollToTop();

    // Scroll after a short delay to ensure content is rendered
    setTimeout(forceScrollToTop, 50);

    // Scroll after a longer delay to catch any late-loading content
    setTimeout(forceScrollToTop, 300);

    // Scroll after an even longer delay
    setTimeout(forceScrollToTop, 1000);
  }, []);

  // Handle legacy mode vs new mode
  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: ChatMessage = { role: 'user', content: input };
    const newChatHistory = [...chatHistory, userMessage];
    setChatHistory(newChatHistory);
    setInput('');
    setLocalLoading(true);
    setInternalError(null);

    // Ensure we scroll to bottom when user sends a message
    setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end'
        });

        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }
    }, 100);

    try {
      let response: CareerCoachingResponse;

      if (onSendMessage) {
        // New mode - use the callback provided by parent component
        response = await onSendMessage(input, newChatHistory);
      } else if (resumeAnalysis && jobDescription) {
        // Legacy mode - directly call the API
        response = await performCareerCoaching(
          resumeAnalysis,
          jobDescription,
          input,
          newChatHistory
        );
      } else {
        throw new Error('Invalid configuration: missing required props');
      }

      if (response?.answer) {
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: response.answer,
          timestamp: new Date().toISOString()
        };

        // Update chat history
        const updatedHistory = [...newChatHistory, assistantMessage];
        setChatHistory(updatedHistory);
      } else {
        throw new Error('No answer received from API');
      }
    } catch (err) {
      console.error('Error in chat:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'An unknown error occurred';
      setInternalError(errorMessage);
      const errorChatHistory = [
        ...newChatHistory,
        {
          role: 'assistant',
          content:
            'Sorry, I encountered an error processing your question. Please try again.'
        }
      ];

      setChatHistory(errorChatHistory);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const MessageContent: React.FC<{ message: ChatMessage }> = ({ message }) => {
    if (message.role === 'user') {
      return (
        <div className="whitespace-pre-wrap break-words text-base font-medium tracking-normal leading-relaxed">
          {message.content}
        </div>
      );
    }

    return (
      <ReactMarkdown
        className="prose-sm sm:prose-md max-w-none dark:prose-invert break-words tracking-normal"
        components={{
          h1: ({ children }) => (
            <h1 className="text-lg sm:text-xl font-bold mt-3 sm:mt-4 mb-2 sm:mb-3 text-white">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-base sm:text-lg font-bold mt-3 sm:mt-4 mb-2 text-white">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-sm sm:text-base font-semibold mt-2 sm:mt-3 mb-1 sm:mb-2 text-white">
              {children}
            </h3>
          ),
          p: ({ children }) => (
            <p className="mb-3 sm:mb-4 leading-relaxed text-base font-medium tracking-normal">
              {children}
            </p>
          ),
          ul: ({ children }) => (
            <ul className="list-disc list-inside mb-3 sm:mb-4 space-y-2 sm:space-y-3 text-base">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside mb-3 sm:mb-4 space-y-2 sm:space-y-3 text-base">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="ml-2 sm:ml-4 font-medium text-base mb-1">
              {children}
            </li>
          ),
          strong: ({ children }) => (
            <strong className="font-bold text-[#F6A03C]">{children}</strong>
          ),
          code: ({ children }) => (
            <code className="bg-gray-700/80 px-1 py-0.5 rounded text-sm font-medium border border-gray-600/50">
              {children}
            </code>
          ),
          pre: ({ children }) => (
            <pre className="bg-gray-700/80 p-2 rounded text-sm my-2 whitespace-pre-wrap border border-gray-600/50">
              {children}
            </pre>
          )
        }}
      >
        {message.content}
      </ReactMarkdown>
    );
  };

  const renderSuggestedQuestions = () => {
    if (
      !initialData.follow_up_questions ||
      initialData.follow_up_questions.length === 0
    )
      return null;

    return (
      <div className="mt-3 sm:mt-5 mb-3 sm:mb-5">
        <p className="text-sm sm:text-base font-medium mb-2 sm:mb-3 text-white">
          Suggested questions:
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
          {initialData.follow_up_questions.map((question, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              className="text-xs py-2 px-3 sm:py-3 sm:px-4 min-h-[40px] sm:min-h-[50px] h-auto w-full text-left justify-start whitespace-normal border border-[#F6A03C]/50 bg-[#F6A03C]/20 text-[#F6A03C] font-medium hover:bg-[#F6A03C]/30 hover:text-[#F6A03C] hover:border-[#F6A03C] hover:shadow-[0_0_10px_rgba(246,160,60,0.4)] shadow-md leading-tight"
              onClick={() => {
                setInput(question);
              }}
            >
              {question}
            </Button>
          ))}
        </div>
      </div>
    );
  };

  // We're now using a more flexible layout with flex and min-h-0

  return (
    <div
      className="w-full h-full flex flex-col mx-auto"
      style={{ maxWidth: '1200px', minHeight: '500px' }}
    >
      {displayError && (
        <div className="bg-destructive/10 border border-destructive text-destructive p-2 rounded-md mb-2 text-xs sm:text-sm shrink-0">
          {displayError}
        </div>
      )}

      <div
        className="flex-1 flex flex-col"
        style={{ height: 'calc(100% - 150px)' }}
      >
        <div
          ref={chatContainerRef}
          className="w-full rounded-md bg-black border border-gray-800 overflow-y-scroll shadow-lg"
          style={{
            height: chatHistory.length <= 2 ? 'calc(100% - 80px)' : '100%'
          }}
        >
          <div className="space-y-4 p-3 sm:p-5">
            {chatHistory.map((message, index) => (
              <div
                key={index}
                className={`message-container flex ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`rounded-lg p-3 sm:p-5 max-w-[90%] ${
                    message.role === 'user'
                      ? 'bg-[#F6A03C] text-gray-900 font-medium shadow-md border border-[#F6A03C]/80'
                      : 'bg-black text-white shadow-md border border-gray-800'
                  }`}
                >
                  <div className="prose-sm sm:prose-md max-w-none break-words leading-relaxed tracking-normal text-base font-medium">
                    <MessageContent message={message} />
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {chatHistory.length <= 2 && (
          <div className="mt-3 sm:mt-4">{renderSuggestedQuestions()}</div>
        )}
      </div>

      <div className="flex flex-col gap-1 sm:gap-2 w-full mt-2 sm:mt-3 shrink-0 min-h-[120px]">
        <div className="relative">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask about the job fit, interview prep, or skill alignment..."
            disabled={isLoading || !resumeData || !jobData}
            className="w-full min-h-[50px] sm:min-h-[80px] p-2 sm:p-3 rounded-md border border-gray-800 bg-black text-xs sm:text-sm text-white ring-offset-background placeholder:text-slate-400 placeholder:text-xs focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#F6A03C] focus-visible:border-[#F6A03C] focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none shadow-md"
            rows={2}
          />
        </div>
        <div className="flex justify-end mt-2">
          <Button
            onClick={handleSendMessage}
            disabled={isLoading || !input.trim() || !resumeData || !jobData}
            className="w-full sm:w-auto px-2 sm:px-4 h-8 sm:h-10 text-xs sm:text-sm bg-[#F6A03C] text-[#111827] hover:bg-[#f8b05c] active:scale-95 transition-all"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin mr-1 sm:mr-2" />
                <span>Sending...</span>
              </>
            ) : (
              <>
                <Send className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span>Send</span>
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AICareerCoach;
