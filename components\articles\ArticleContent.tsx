'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Article } from '@/app/types/globalTypes';
import OptimizedImage from '@/components/OptimizedImage';
import ArticleSchemaMarkup from '@/components/seo/ArticleSchemaMarkup';
import dynamic from 'next/dynamic';

// Use dynamic import with ssr: false for MediaPlayer
const MediaPlayerWrapper = dynamic(
  () => import('@/components/article/MediaPlayerWrapper'),
  {
    ssr: false,
    loading: () => (
      <div className="my-4 backdrop-blur-sm bg-white/5 border border-white/10 rounded-xl p-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="w-4 h-4 rounded-full bg-hero-yellow/30 animate-pulse" />
          <span className="text-sm font-medium text-slate-200 animate-pulse">
            Loading audio...
          </span>
        </div>
        <div className="w-full h-12 bg-white/10 rounded animate-pulse" />
      </div>
    )
  }
);

interface ArticleContentProps {
  article: Article;
}

const ArticleContent: React.FC<ArticleContentProps> = ({ article }) => {
  return (
    <div className="relative w-full overflow-hidden min-h-screen">
      {/* Full-width hero image with teal gradient */}
      {article.schema.image && (
        <div className="w-full relative overflow-hidden">
          {/* Background image - centered with contain */}
          <div className="h-[80vh] w-full flex items-center justify-center relative">
            {/* Teal gradient overlay that matches the background */}
            <div className="absolute inset-0 z-10"></div>

            <div className="w-full h-full flex items-center justify-center">
              <div className="relative w-full h-full mx-auto flex items-center justify-center">
                <OptimizedImage
                  src={article.schema.image}
                  alt={article.title}
                  width={1920}
                  height={1080}
                  className="w-auto h-full max-h-full object-contain"
                  priority
                />
              </div>
            </div>
          </div>

          {/* Title area below the image */}
          <div className="relative z-20 bg-hero-bg/30 px-4 py-8">
            <div className="max-w-4xl mx-auto">
              <h1 className="font-roboto-condensed font-[700] leading-[1.1] tracking-[-0.05em] mb-4 text-white text-[clamp(2rem,5vw,3.5rem)] text-shadow-md">
                <span className="text-hero-yellow text-shadow-yellow">
                  {article.title}
                </span>
              </h1>
              <p className="text-white text-lg md:text-xl mb-2">
                {article.author} •{' '}
                {new Date(article.schema.datePublished).toLocaleDateString(
                  'en-GB'
                )}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="relative z-10 mx-auto w-full max-w-[1200px] px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <div className="flex flex-wrap gap-2 mb-6">
              {article.tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="bg-white/10 text-slate-200 hover:bg-hero-yellow/20 hover:text-hero-yellow"
                >
                  {tag}
                </Badge>
              ))}
            </div>

            {/* Audio player */}
            {article.audio && (
              <div className="mb-8">
                <MediaPlayerWrapper media={article.audio} type="audio" />
              </div>
            )}
          </div>

          <div
            className="prose prose-lg max-w-none prose-invert prose-headings:font-roboto-condensed prose-headings:font-bold prose-headings:text-white prose-p:text-slate-200 prose-a:text-hero-yellow prose-a:no-underline hover:prose-a:underline prose-strong:text-hero-yellow backdrop-blur-sm bg-white/5 border border-white/10 rounded-xl p-8"
            dangerouslySetInnerHTML={{ __html: article.content }}
          />
        </div>

        {/* Add schema markup for SEO */}
        <ArticleSchemaMarkup article={article} />
      </div>
    </div>
  );
};

export default ArticleContent;
