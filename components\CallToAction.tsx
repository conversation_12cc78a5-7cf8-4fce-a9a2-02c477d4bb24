'use client';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { createClient } from '@/app/utils/supabase/client';

interface Props {
  label?: string; // headline
  sublabel?: string; // one-liner
  href?: string; // destination
}

export function CallToAction({
  label = 'Generate your AI-optimised CV now',
  sublabel = 'No credit card required',
  href = '/signup'
}: Props) {
  const pathname = usePathname();
  const router = useRouter();
  const [userId, setUserId] = useState<string | null>(null);

  useEffect(() => {
    const supabase = createClient();

    const getUser = async () => {
      try {
        const {
          data: { user }
        } = await supabase.auth.getUser();
        setUserId(user?.id || null);
      } catch (error) {
        console.error('Error fetching user:', error);
        setUserId(null);
      }
    };

    getUser();
  }, []);

  // If user is in the funnel, set next param to current path, else default to funnel start
  const nextParam = pathname.startsWith('/application-funnel')
    ? `?next=${encodeURIComponent(pathname)}`
    : `?next=/application-funnel/start`;

  return (
    <section className="relative mx-auto isolate py-16 max-w-4xl overflow-hidden scroll-mt-16 bg-home-cta-bg w-full">
      <div className="relative z-10 mx-auto w-full  px-4 animate-in fade-in duration-700">
        <div className=" mx-auto text-center">
          <h2 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
            {label}
          </h2>
          <p className="text-xl mb-6 text-slate-200">{sublabel}</p>
          <div className="flex flex-wrap justify-center gap-4">
            {userId ? (
              <button
                onClick={() => router.push(`/dashboard/${userId}`)}
                className="group px-8 py-4 md:py-5 bg-hero-yellow text-[#111827] font-semibold rounded-full shadow-lg flex items-center justify-center gap-3 hover:bg-hero-yellow-light active:scale-95 text-[16px] md:text-[18px]"
                data-cta={label.toLowerCase().replace(/\s+/g, '-')}
              >
                Go to Dashboard →
              </button>
            ) : (
              <Link
                href={`${href}${nextParam}`}
                className="group px-8 py-4 md:py-5 bg-hero-yellow text-[#111827] font-semibold rounded-full shadow-lg flex items-center justify-center gap-3 hover:bg-hero-yellow-light active:scale-95 text-[16px] md:text-[18px]"
                prefetch={false}
                data-cta={label.toLowerCase().replace(/\s+/g, '-')}
              >
                Try JobSpaceAI →
              </Link>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
