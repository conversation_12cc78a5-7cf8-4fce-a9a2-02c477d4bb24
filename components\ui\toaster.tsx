'use client';

import React from 'react';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport
} from '@/components/ui/toast';
import { useToast } from '@/hooks/use-toast';
import { Portal } from '@radix-ui/react-portal'; // ✨ Added import

// Main Toaster component with simplified implementation
export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider swipeDirection="right">
      {toasts.map(({ id, title, description, action, ...props }) => {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <Portal>
        <ToastViewport className="fixed top-0 right-0 z-[100] flex flex-col-reverse gap-2 p-4 sm:bottom-0 sm:top-auto md:max-w-[420px]" />
      </Portal>
    </ToastProvider>
  );
}
