/* app/globals.css - PERFORMANCE OPTIMIZED */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 187 96% 32%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 187 96% 32%;
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --failure: 0 84.2% 60.2%;
    --failure-foreground: 210 40% 98%;
    --radius: 0.5rem;

    /* ✅ Simplified hero variables */
    --hero-yellow: 33 91% 60%;
    --hero-text: 0 0% 100%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 187 96% 42%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 187 96% 42%;
    --success: 142 69% 58%;
    --success-foreground: 210 40% 98%;
    --failure: 0 62.8% 30.6%;
    --failure-foreground: 210 40% 98%;

    --hero-yellow: 33 91% 60%;
    --hero-text: 0 0% 100%;
  }

  /* ✅ CRITICAL: Essential typography only */
  * {
    @apply border-[color:hsl(var(--border))];
  }

  html {
    /* ✅ PREVENT BLOB OVERFLOW - Hide blobs that extend beyond viewport */
    overflow-x: hidden;
    /* ✅ PREVENT DOUBLE SCROLLBARS - Ensure only one vertical scrollbar */
    overflow-y: auto;
  }
  
  body {
    /* ✅ FIXED: Proper background layering for dark theme */
    background-color: hsl(var(--background));
    background-image: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 15%, transparent 35%, transparent 65%, rgba(0,0,0,0.3) 85%, rgba(0,0,0,0.7) 100%);
    background-size: 100% 100vh; /* Each gradient spans one viewport height */
    background-repeat: repeat-y; /* Repeats vertically as content extends */
    background-attachment: local; /* Scrolls with content */
    color: hsl(var(--foreground));
    /* ✅ Allow normal vertical scrolling but prevent horizontal overflow */
    overflow-x: hidden;
    /* ✅ PREVENT DOUBLE SCROLLBARS - Let html handle vertical scrolling */
    overflow-y: visible;
  }

  /* ✅ Remove clamp() for better performance - use responsive classes instead */
}

@layer components {
  /* ✅ CRITICAL: Only include styles used on initial page load */
  .headline-gradient {
    @apply bg-gradient-to-r from-amber-300 to-amber-400 bg-clip-text text-transparent;
  }

  /* ✅ Blob container for animated background elements */
  .blob-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: -50;
  }

  /* ✅ Alternative: Scrolling blob container */
  .blob-container-scrolling {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 300vh; /* Adjust based on your content height */
    pointer-events: none;
    z-index: -50;
  }

  /* ✅ Remove heavy animations and complex styles from critical CSS */
  /* These should be loaded dynamically by components that need them */
}

@layer utilities {
  /* ✅ CRITICAL: Only essential utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* ✅ Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* ✅ MOVE TO SEPARATE FILES: Component-specific styles */
/* 
  - Move .fixed-cta to components/ui/FixedCTA.module.css
  - Move .auth-input to components/auth/AuthInput.module.css  
  - Move .animate-blob to components/hero/HeroBackground.module.css
*/