import React, { useState } from 'react';
import { CareerMatchingResponse } from '@/app/types/globalTypes';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface CareerMatchDisplayProps {
  careerMatches: CareerMatchingResponse;
}

const CareerMatchDisplay: React.FC<CareerMatchDisplayProps> = ({
  careerMatches
}) => {
  const [selectedCareerIndex, setSelectedCareerIndex] = useState(0);

  if (
    !careerMatches ||
    !careerMatches.alternative_careers ||
    careerMatches.alternative_careers.length === 0
  ) {
    return (
      <div className="text-muted-foreground">
        No career matches found. Please try again with a more detailed CV.
      </div>
    );
  }

  const selectedCareer = careerMatches.alternative_careers[selectedCareerIndex];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
        {/* Career List */}
        <div className="md:col-span-4 space-y-2">
          <h3 className="font-medium mb-2">Alternative Careers</h3>
          {careerMatches.alternative_careers.map((career, index) => (
            <div
              key={index}
              onClick={() => setSelectedCareerIndex(index)}
              className={`p-3 rounded-lg cursor-pointer transition-all ${
                index === selectedCareerIndex
                  ? 'bg-primary/10 border border-primary'
                  : 'bg-muted hover:bg-muted/80'
              }`}
            >
              <div className="flex justify-between items-center">
                <h4 className="font-medium">{career.title}</h4>
                <Badge
                  variant={
                    index === selectedCareerIndex ? 'default' : 'secondary'
                  }
                >
                  {Math.round(career.match_percentage)}%
                </Badge>
              </div>
            </div>
          ))}
        </div>

        {/* Career Details */}
        <div className="md:col-span-8">
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-bold">{selectedCareer.title}</h3>
              <div className="mt-2">
                <div className="flex justify-between text-sm mb-1">
                  <span>Match Score</span>
                  <span>{Math.round(selectedCareer.match_percentage)}%</span>
                </div>
                <Progress
                  value={selectedCareer.match_percentage}
                  className="h-2"
                />
              </div>
            </div>

            <div>
              <p className="text-muted-foreground">
                {selectedCareer.description}
              </p>
            </div>

            {/* Skills Match */}
            {selectedCareer.skills_match &&
              selectedCareer.skills_match.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Skills You Already Have</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedCareer.skills_match.map((skill, i) => (
                      <Badge key={i} variant="outline" className="bg-primary/5">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

            {/* Skills to Develop */}
            {selectedCareer.additional_skills_needed &&
              selectedCareer.additional_skills_needed.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Skills to Develop</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedCareer.additional_skills_needed.map((skill, i) => (
                      <Badge key={i} variant="outline" className="bg-muted">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CareerMatchDisplay;
