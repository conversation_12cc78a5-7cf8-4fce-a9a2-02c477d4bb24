'use client';

import React, { ErrorInfo, ReactNode } from 'react';
import UploadResumeForm from 'components/resume/uploadResumeForm';
import { Resume } from '@/app/types/globalTypes';

class ErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div role="alert">
          <p>Something went wrong:</p>
          <pre>{this.state.error?.message}</pre>
        </div>
      );
    }

    return this.props.children;
  }
}

interface UploadResumeClientProps {
  userId?: string;
  allowAnonymous?: boolean;
  onComplete?: (resume: Resume) => void;
}

import { useSelectionStore } from 'stores/useSelectionStore';
import { trackFileUploadSuccess, trackUserInteraction } from '@/lib/ga-events';

export default function UploadResumeClient({
  userId,
  allowAnonymous = false,
  onComplete
}: Readonly<UploadResumeClientProps>) {
  // Default callback: persist the freshly-uploaded resume
  const { setSelectedResume } = useSelectionStore();

  // Track page view for standalone upload page
  React.useEffect(() => {
    trackUserInteraction({
      interaction_type: 'page_view',
      element_type: 'standalone_upload_page',
      user_journey: userId ? 'dashboard' : 'marketing'
    });
  }, [userId]);

  const handleComplete = (resume: Resume) => {
    // Track successful upload in standalone context
    trackFileUploadSuccess({
      file_type: 'cv_resume',
      upload_method: 'standalone_page',
      file_source: 'direct_navigation',
      file_size_chars: resume.resume?.length || 0,
      user_journey: userId ? 'dashboard' : 'marketing',
      user_type: userId ? 'authenticated' : 'anonymous'
    });

    // Track completion
    trackUserInteraction({
      interaction_type: 'upload_complete',
      element_type: 'standalone_upload_page',
      user_journey: userId ? 'dashboard' : 'marketing'
    });

    setSelectedResume(resume);
    onComplete?.(resume);
  };

  if (!userId && !allowAnonymous) {
    return (
      <div className="text-center text-white">
        Please sign in to upload your résumé.
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <UploadResumeForm
        userId={userId ?? ''}
        allowAnonymous={allowAnonymous}
        onComplete={handleComplete}
      />
    </ErrorBoundary>
  );
}
