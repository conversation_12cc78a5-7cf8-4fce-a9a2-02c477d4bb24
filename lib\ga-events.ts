import { GA4_EVENTS, type JobSpaceEventParams } from '@/types/events';

// ===== ONBOARDING JOURNEY EVENTS =====

export const trackOnboardingStart = () =>
  trackEvent(GA4_EVENTS.ONBOARDING_START, {
    event_category: 'engagement',
    user_journey: 'onboarding',
    journey_stage: 'consideration'
  });

export const trackSignupAttempt = (method: string = 'email') =>
  trackEvent(GA4_EVENTS.SIGNUP_ATTEMPT, {
    event_category: 'authentication',
    method,
    user_journey: 'onboarding'
  });

export const trackSignupFailure = (
  method: string = 'email',
  errorType?: string
) =>
  trackEvent(GA4_EVENTS.SIGNUP_FAILURE, {
    event_category: 'error',
    method,
    error_type: errorType || 'signup_failure',
    user_journey: 'onboarding'
  });

// ===== FILE UPLOAD EVENTS =====

// Legacy function for backward compatibility
export const trackFileUploadStart = (
  fileTypeOrParams:
    | 'job_description'
    | 'cv_resume'
    | {
        file_type: 'job_description' | 'cv_resume';
        upload_method?: string;
        file_source?: string;
        user_journey?: string;
        file_size_kb?: number;
        user_type?: string;
      },
  fileSizeKb?: number
) => {
  if (typeof fileTypeOrParams === 'string') {
    // Legacy API
    trackEvent(GA4_EVENTS.FILE_UPLOAD_START, {
      event_category: 'engagement',
      file_type: fileTypeOrParams,
      file_size_kb: fileSizeKb,
      user_journey: 'onboarding'
    });
  } else {
    // New object-based API
    trackEvent(GA4_EVENTS.FILE_UPLOAD_START, {
      event_category: 'engagement',
      file_type: fileTypeOrParams.file_type,
      upload_method: fileTypeOrParams.upload_method,
      file_source: fileTypeOrParams.file_source,
      user_journey: fileTypeOrParams.user_journey || 'onboarding',
      file_size_kb: fileTypeOrParams.file_size_kb,
      user_type: fileTypeOrParams.user_type
    });
  }
};

// Legacy function for backward compatibility
export const trackFileUploadSuccess = (
  fileTypeOrParams:
    | 'job_description'
    | 'cv_resume'
    | {
        file_type: 'job_description' | 'cv_resume';
        upload_method?: string;
        file_source?: string;
        upload_duration_ms?: number;
        file_size_chars?: number;
        user_journey?: string;
        user_type?: string;
      },
  fileSizeKb?: number,
  uploadDurationMs?: number
) => {
  if (typeof fileTypeOrParams === 'string') {
    // Legacy API
    trackEvent(GA4_EVENTS.FILE_UPLOAD_SUCCESS, {
      event_category: 'engagement',
      file_type: fileTypeOrParams,
      file_size_kb: fileSizeKb,
      upload_duration_ms: uploadDurationMs,
      user_journey: 'onboarding',
      onboarding_step: fileTypeOrParams === 'job_description' ? 3 : 4
    });
  } else {
    // New object-based API
    trackEvent(GA4_EVENTS.FILE_UPLOAD_SUCCESS, {
      event_category: 'engagement',
      file_type: fileTypeOrParams.file_type,
      upload_method: fileTypeOrParams.upload_method,
      file_source: fileTypeOrParams.file_source,
      upload_duration_ms: fileTypeOrParams.upload_duration_ms,
      file_size_chars: fileTypeOrParams.file_size_chars,
      user_journey: fileTypeOrParams.user_journey || 'onboarding',
      user_type: fileTypeOrParams.user_type,
      onboarding_step: fileTypeOrParams.file_type === 'job_description' ? 3 : 4
    });
  }
};

// Legacy function for backward compatibility
export const trackFileUploadFailure = (
  fileTypeOrParams:
    | 'job_description'
    | 'cv_resume'
    | {
        file_type: 'job_description' | 'cv_resume';
        upload_method?: string;
        error_type?: string;
        error_message?: string;
        file_source?: string;
        upload_duration_ms?: number;
        user_journey?: string;
        user_type?: string;
      },
  errorMessage?: string
) => {
  if (typeof fileTypeOrParams === 'string') {
    // Legacy API
    trackEvent(GA4_EVENTS.FILE_UPLOAD_FAILURE, {
      event_category: 'error',
      file_type: fileTypeOrParams,
      error_type: 'upload_failed',
      error_message: errorMessage?.substring(0, 100),
      user_journey: 'onboarding'
    });
  } else {
    // New object-based API
    trackEvent(GA4_EVENTS.FILE_UPLOAD_FAILURE, {
      event_category: 'error',
      file_type: fileTypeOrParams.file_type,
      upload_method: fileTypeOrParams.upload_method,
      error_type: fileTypeOrParams.error_type || 'upload_failed',
      error_message: fileTypeOrParams.error_message?.substring(0, 100),
      file_source: fileTypeOrParams.file_source,
      upload_duration_ms: fileTypeOrParams.upload_duration_ms,
      user_journey: fileTypeOrParams.user_journey || 'onboarding',
      user_type: fileTypeOrParams.user_type
    });
  }
};

// Legacy function for backward compatibility
export const trackUploadAbandon = (fileType: 'job_description' | 'cv_resume') =>
  trackEvent(GA4_EVENTS.UPLOAD_ABANDON, {
    event_category: 'behavior',
    file_type: fileType,
    user_journey: 'onboarding'
  });

// New object-based function
export const trackFileUploadAbandon = (params: {
  file_type: 'job_description' | 'cv_resume';
  abandon_stage?: string;
  time_spent_ms?: number;
  user_journey?: string;
  user_type?: string;
}) =>
  trackEvent(GA4_EVENTS.UPLOAD_ABANDON, {
    event_category: 'behavior',
    file_type: params.file_type,
    abandon_stage: params.abandon_stage,
    time_spent_ms: params.time_spent_ms,
    user_journey: params.user_journey || 'onboarding',
    user_type: params.user_type
  });

// ===== ANALYSIS EVENTS =====

// Legacy function for backward compatibility
export const trackAnalysisStart = (
  analysisTypeOrParams:
    | string
    | {
        analysis_type: string;
        feature_used?: string;
        user_journey?: string;
        user_type?: string;
        resume_file_size?: number;
        job_description_length?: number;
        [key: string]: string | number | undefined;
      }
) => {
  if (typeof analysisTypeOrParams === 'string') {
    // Legacy API
    trackEvent(GA4_EVENTS.ANALYSIS_START, {
      event_category: 'engagement',
      analysis_type: analysisTypeOrParams,
      user_journey: 'onboarding'
    });
  } else {
    // New object-based API
    trackEvent(GA4_EVENTS.ANALYSIS_START, {
      event_category: 'engagement',
      analysis_type: analysisTypeOrParams.analysis_type,
      feature_used: analysisTypeOrParams.feature_used,
      user_journey: analysisTypeOrParams.user_journey || 'onboarding',
      user_type: analysisTypeOrParams.user_type,
      resume_file_size: analysisTypeOrParams.resume_file_size,
      job_description_length: analysisTypeOrParams.job_description_length,
      ...Object.fromEntries(
        Object.entries(analysisTypeOrParams).filter(
          ([key]) =>
            ![
              'analysis_type',
              'feature_used',
              'user_journey',
              'user_type',
              'resume_file_size',
              'job_description_length'
            ].includes(key)
        )
      )
    });
  }
};

// Legacy function for backward compatibility
export const trackAnalysisComplete = (
  analysisTypeOrParams:
    | string
    | {
        analysis_type: string;
        analysis_time_ms?: number;
        feature_used?: string;
        user_journey?: string;
        user_type?: string;
        [key: string]: string | number | undefined;
      },
  analysisTimeMs?: number,
  userJourney: 'onboarding' | 'dashboard_direct' = 'onboarding'
) => {
  if (typeof analysisTypeOrParams === 'string') {
    // Legacy API
    trackEvent(GA4_EVENTS.ANALYSIS_COMPLETE, {
      event_category: 'conversion',
      analysis_type: analysisTypeOrParams,
      analysis_time_ms: analysisTimeMs,
      user_journey: userJourney,
      analysis_success: true
    });
  } else {
    // New object-based API
    trackEvent(GA4_EVENTS.ANALYSIS_COMPLETE, {
      event_category: 'conversion',
      analysis_type: analysisTypeOrParams.analysis_type,
      analysis_time_ms: analysisTypeOrParams.analysis_time_ms,
      feature_used: analysisTypeOrParams.feature_used,
      user_journey: analysisTypeOrParams.user_journey || 'onboarding',
      user_type: analysisTypeOrParams.user_type,
      analysis_success: true,
      ...Object.fromEntries(
        Object.entries(analysisTypeOrParams).filter(
          ([key]) =>
            ![
              'analysis_type',
              'analysis_time_ms',
              'feature_used',
              'user_journey',
              'user_type'
            ].includes(key)
        )
      )
    });
  }
};

// Legacy function for backward compatibility
export const trackAnalysisFailure = (
  analysisTypeOrParams:
    | string
    | {
        analysis_type: string;
        error_type?: string;
        error_message?: string;
        analysis_time_ms?: number;
        feature_used?: string;
        user_journey?: string;
        user_type?: string;
        step_failed?: number;
        [key: string]: string | number | undefined;
      },
  errorMessage?: string
) => {
  if (typeof analysisTypeOrParams === 'string') {
    // Legacy API
    trackEvent(GA4_EVENTS.ANALYSIS_FAILURE, {
      event_category: 'error',
      analysis_type: analysisTypeOrParams,
      error_type: 'analysis_failed',
      error_message: errorMessage?.substring(0, 100),
      analysis_success: false
    });
  } else {
    // New object-based API
    trackEvent(GA4_EVENTS.ANALYSIS_FAILURE, {
      event_category: 'error',
      analysis_type: analysisTypeOrParams.analysis_type,
      error_type: analysisTypeOrParams.error_type || 'analysis_failed',
      error_message: analysisTypeOrParams.error_message?.substring(0, 100),
      analysis_time_ms: analysisTypeOrParams.analysis_time_ms,
      feature_used: analysisTypeOrParams.feature_used,
      user_journey: analysisTypeOrParams.user_journey || 'onboarding',
      user_type: analysisTypeOrParams.user_type,
      step_failed: analysisTypeOrParams.step_failed,
      analysis_success: false,
      ...Object.fromEntries(
        Object.entries(analysisTypeOrParams).filter(
          ([key]) =>
            ![
              'analysis_type',
              'error_type',
              'error_message',
              'analysis_time_ms',
              'feature_used',
              'user_journey',
              'user_type',
              'step_failed'
            ].includes(key)
        )
      )
    });
  }
};

// New job-specific analysis functions
export const trackJobAnalysisStart = (params: {
  analysis_type: string;
  file_source?: string;
  input_method?: string;
  character_count?: number;
  user_journey?: string;
  user_type?: string;
}) =>
  trackEvent(GA4_EVENTS.ANALYSIS_START, {
    event_category: 'engagement',
    analysis_type: params.analysis_type,
    file_source: params.file_source,
    input_method: params.input_method,
    character_count: params.character_count,
    user_journey: params.user_journey || 'onboarding',
    user_type: params.user_type
  });

export const trackJobAnalysisComplete = (params: {
  analysis_type: string;
  analysis_time_ms?: number;
  file_source?: string;
  input_method?: string;
  character_count?: number;
  user_journey?: string;
  user_type?: string;
}) =>
  trackEvent(GA4_EVENTS.ANALYSIS_COMPLETE, {
    event_category: 'conversion',
    analysis_type: params.analysis_type,
    analysis_time_ms: params.analysis_time_ms,
    file_source: params.file_source,
    input_method: params.input_method,
    character_count: params.character_count,
    user_journey: params.user_journey || 'onboarding',
    user_type: params.user_type,
    analysis_success: true
  });

export const trackJobAnalysisFailure = (params: {
  analysis_type: string;
  error_type?: string;
  error_message?: string;
  analysis_time_ms?: number;
  file_source?: string;
  input_method?: string;
  character_count?: number;
  user_journey?: string;
  user_type?: string;
}) =>
  trackEvent(GA4_EVENTS.ANALYSIS_FAILURE, {
    event_category: 'error',
    analysis_type: params.analysis_type,
    error_type: params.error_type || 'analysis_failed',
    error_message: params.error_message?.substring(0, 100),
    analysis_time_ms: params.analysis_time_ms,
    file_source: params.file_source,
    input_method: params.input_method,
    character_count: params.character_count,
    user_journey: params.user_journey || 'onboarding',
    user_type: params.user_type,
    analysis_success: false
  });

// ===== CTA & ENGAGEMENT EVENTS =====

export const trackCTAClick = (
  ctaType: string,
  ctaLocation: string,
  ctaText?: string
) =>
  trackEvent(GA4_EVENTS.CTA_CLICK, {
    event_category: 'engagement',
    cta_type: ctaType,
    cta_location: ctaLocation,
    button_text: ctaText,
    journey_stage: 'interest'
  });

export const trackPricingPageView = (userJourney?: string) =>
  trackEvent(GA4_EVENTS.PRICING_PAGE_VIEW, {
    event_category: 'ecommerce',
    page_section: 'pricing',
    user_journey: userJourney || 'onboarding'
  });

export const trackMarketingPageView = (
  pageName: string,
  userJourney?: string
) =>
  trackEvent('page_view', {
    event_category: 'engagement',
    page_title: pageName,
    user_journey: userJourney || 'marketing'
  });

export const trackMarketingConversion = (params: {
  conversion_type: string;
  conversion_stage: string;
  user_type?: string;
  time_to_conversion_ms?: number;
}) =>
  trackEvent('marketing_conversion', {
    event_category: 'conversion',
    conversion_type: params.conversion_type,
    conversion_stage: params.conversion_stage,
    user_type: params.user_type,
    time_to_conversion_ms: params.time_to_conversion_ms
  });

export const trackFeatureUsage = (params: {
  feature_name: string;
  feature_category: string;
  usage_duration_ms?: number;
  user_journey?: string;
  completion_status?: string;
  error_message?: string;
}) =>
  trackEvent('feature_usage', {
    event_category: 'engagement',
    feature_name: params.feature_name,
    feature_category: params.feature_category,
    usage_duration_ms: params.usage_duration_ms,
    user_journey: params.user_journey,
    completion_status: params.completion_status,
    error_message: params.error_message
  });

export const trackPlanCardView = (planId: string, planType: string) =>
  trackEvent(GA4_EVENTS.PLAN_CARD_VIEW, {
    event_category: 'ecommerce',
    plan_id: planId,
    plan_type: planType
  });

export const trackPlanSelect = (
  planId: string,
  planType: string,
  planValue: number
) =>
  trackEvent(GA4_EVENTS.PLAN_SELECT, {
    event_category: 'ecommerce',
    plan_id: planId,
    plan_type: planType,
    value: planValue,
    currency: 'GBP'
  });

// ===== INTERACTION & VALIDATION EVENTS =====

export const trackButtonClick = (params: {
  button_name: string;
  button_location: string;
  user_journey?: string;
  user_type?: string;
}) =>
  trackEvent(GA4_EVENTS.CTA_CLICK, {
    event_category: 'engagement',
    button_name: params.button_name,
    button_location: params.button_location,
    user_journey: params.user_journey || 'onboarding',
    user_type: params.user_type
  });

export const trackUserInteraction = (params: {
  interaction_type: string;
  element_type: string;
  user_journey?: string;
  user_type?: string;
  time_spent_ms?: number;
  error_message?: string;
  [key: string]: string | number | undefined;
}) =>
  trackEvent('user_interaction', {
    event_category: 'engagement',
    interaction_type: params.interaction_type,
    element_type: params.element_type,
    user_journey: params.user_journey || 'onboarding',
    user_type: params.user_type,
    time_spent_ms: params.time_spent_ms,
    error_message: params.error_message,
    ...Object.fromEntries(
      Object.entries(params).filter(
        ([key]) =>
          ![
            'interaction_type',
            'element_type',
            'user_journey',
            'user_type',
            'time_spent_ms',
            'error_message'
          ].includes(key)
      )
    )
  });

export const trackValidationFailure = (params: {
  field_name: string;
  error_message: string;
  validation_value?: string;
  user_journey?: string;
  user_type?: string;
}) =>
  trackEvent('validation_failure', {
    event_category: 'error',
    field_name: params.field_name,
    error_message: params.error_message?.substring(0, 100),
    validation_value: params.validation_value,
    user_journey: params.user_journey || 'onboarding',
    user_type: params.user_type
  });

// ===== DASHBOARD JOURNEY EVENTS =====

export const trackDashboardView = (returningUser: boolean = false) =>
  trackEvent(GA4_EVENTS.DASHBOARD_VIEW, {
    event_category: 'engagement',
    user_journey: 'dashboard_direct',
    returning_user: returningUser,
    page_title: 'Dashboard'
  });

export const trackFeatureDiscover = (featureName: string) =>
  trackEvent(GA4_EVENTS.FEATURE_DISCOVER, {
    event_category: 'engagement',
    feature_name: featureName,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureSelect = (
  featureName: string,
  featureLocation: string
) =>
  trackEvent(GA4_EVENTS.FEATURE_SELECT, {
    event_category: 'engagement',
    feature_name: featureName,
    feature_location: featureLocation,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureUseStart = (featureName: string) =>
  trackEvent(GA4_EVENTS.FEATURE_USE_START, {
    event_category: 'engagement',
    feature_name: featureName,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureUseComplete = (
  featureName: string,
  timeSpentSeconds?: number
) =>
  trackEvent(GA4_EVENTS.FEATURE_USE_COMPLETE, {
    event_category: 'conversion',
    feature_name: featureName,
    time_spent_seconds: timeSpentSeconds,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureAbandon = (featureName: string) =>
  trackEvent(GA4_EVENTS.FEATURE_ABANDON, {
    event_category: 'behavior',
    feature_name: featureName,
    user_journey: 'dashboard_direct'
  });

// ===== DOCUMENT & CONTENT EVENTS =====

export const trackDocumentGenerated = (
  documentType: string,
  featureUsed: string
) =>
  trackEvent('document_generated', {
    event_category: 'conversion',
    document_type: documentType,
    feature_name: featureUsed
  });

export const trackDocumentDownload = (
  documentType: string,
  fileFormat: string = 'pdf'
) =>
  trackEvent(GA4_EVENTS.DOCUMENT_DOWNLOAD, {
    event_category: 'engagement',
    document_type: documentType,
    file_format: fileFormat,
    journey_stage: 'retention'
  });

export const trackDocumentShare = (documentType: string, shareMethod: string) =>
  trackEvent(GA4_EVENTS.DOCUMENT_SHARE, {
    event_category: 'engagement',
    document_type: documentType,
    method: shareMethod
  });

// ===== SUPPORT & HELP EVENTS =====

export const trackHelpView = (helpTopic: string, helpType: string = 'faq') =>
  trackEvent(GA4_EVENTS.HELP_VIEW, {
    event_category: 'support',
    help_type: helpType,
    help_topic: helpTopic
  });

export const trackSiteSearch = (searchTerm: string, resultCount?: number) =>
  trackEvent(GA4_EVENTS.SITE_SEARCH, {
    event_category: 'engagement',
    search_term: searchTerm,
    result_count: resultCount
  });

export const trackFeedbackSubmit = (rating?: number, commentLength?: number) =>
  trackEvent(GA4_EVENTS.FEEDBACK_SUBMIT, {
    event_category: 'support',
    rating: rating,
    comment_length: commentLength
  });

// ===== ERROR & TECHNICAL EVENTS =====

export const trackTechError = (
  errorType: string,
  errorLocation: string,
  errorMessage?: string
) =>
  trackEvent(GA4_EVENTS.TECH_ERROR, {
    event_category: 'error',
    error_type: errorType,
    error_location: errorLocation,
    error_message: errorMessage?.substring(0, 100)
  });

export const trackAPIError = (
  endpoint: string,
  statusCode: number,
  errorMessage?: string
) =>
  trackEvent(GA4_EVENTS.API_ERROR, {
    event_category: 'error',
    error_type: 'api_error',
    api_endpoint: endpoint,
    status_code: statusCode,
    error_message: errorMessage?.substring(0, 100)
  });

export const trackJourneyDropOff = (stepName: string, userJourney: string) =>
  trackEvent(GA4_EVENTS.JOURNEY_DROP_OFF, {
    event_category: 'behavior',
    drop_off_step: stepName,
    user_journey: userJourney,
    journey_stage: 'drop_off'
  });

// ===== UTILITY EVENTS =====

export const trackPageView = (
  pageName: string,
  userJourney?: string,
  additionalParams?: JobSpaceEventParams
) =>
  trackEvent('page_view', {
    page_title: pageName,
    user_journey: userJourney,
    ...additionalParams
  });

export const trackScrollDepth = (depthPercent: number, pageName: string) =>
  trackEvent('scroll_depth', {
    event_category: 'engagement',
    scroll_depth_percent: depthPercent,
    page_title: pageName
  });

export const trackTimeOnPage = (pageName: string, timeSpentSeconds: number) =>
  trackEvent('time_on_page', {
    event_category: 'engagement',
    page_title: pageName,
    time_spent_seconds: timeSpentSeconds,
    engagement_level:
      timeSpentSeconds > 60 ? 'high' : timeSpentSeconds > 30 ? 'medium' : 'low'
  });
export type GAEventParams = Record<string, unknown>;

/**
 * Safely dispatch a GA4 event using gtag.
 * If gtag isn't available (e.g., during SSR), the event is logged to the console.
 */
export function trackEvent(
  eventName: string,
  params: GAEventParams = {}
): void {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    window.gtag('event', eventName, params);
  } else {
    console.debug(`gtag not found. Event '${eventName}' not sent`, params);
  }
}

/** Primary Conversion Events */
export const trackFreeTrialSignup = () =>
  trackEvent('sign_up', { method: 'website' });

export const trackCVGeneration = () =>
  trackEvent('generate_cv', { tool_type: 'cv_builder', user_type: 'free' });

export const trackATSCheckCompletion = () =>
  trackEvent('ats_check_complete', {
    tool_type: 'ats_checker',
    user_type: 'free'
  });

export const trackCoverLetterGeneration = () =>
  trackEvent('generate_cover_letter', {
    tool_type: 'cover_letter',
    user_type: 'free'
  });

/** Secondary Events */
export const trackNewsletterSignup = () =>
  trackEvent('newsletter_signup', { method: 'website' });

export const trackDemoRequest = () =>
  trackEvent('request_quote', { item_type: 'demo' });
