// app/sitemap.ts
import type { MetadataRoute } from 'next';
import { articles } from './articles/articles-data';
import { FEATURES } from '@/app/constants/features';

export const revalidate = 86400; // 24 h ISR

export default function sitemap(): MetadataRoute.Sitemap {
  // ① fixed, always-present pages
  const basePaths = [
    '',
    '/about',
    '/contact',
    '/cookies',
    '/privacy',
    '/terms',
    '/onboarding',
    '/articles'
  ];

  // ② UK-specific landing pages (high priority for SEO)
  const ukLandingPages = [
    '/cv-checker-uk',
    '/ats-resume-checker-uk',
    '/nhs-cv-checker'
  ];

  // ③ marketing feature pages pulled from FEATURES config
  const featurePaths = FEATURES.filter((f) => f.public && f.slug).map(
    (f) => `/${f.slug}`
  );

  const staticPages: MetadataRoute.Sitemap = [
    ...basePaths,
    ...featurePaths
  ].map((path) => ({
    url: `https://jobspaceai.com${path}`,
    changeFrequency: 'daily',
    priority: 0.7,
    lastModified: '2025-06-20' // Updated to current date
  }));

  // ④ UK landing pages with higher priority
  const ukPages: MetadataRoute.Sitemap = ukLandingPages.map((path) => ({
    url: `https://jobspaceai.com${path}`,
    changeFrequency: 'weekly', // Less frequent than daily for static content
    priority: 0.9, // Higher priority for key landing pages
    lastModified: '2025-06-20' // Current implementation date
  }));

  // ⑤ dynamic article slugs
  const articlePages: MetadataRoute.Sitemap = articles.map(
    ({ slug, schema: { dateModified } }) => ({
      url: `https://jobspaceai.com/articles/${slug}`,
      lastModified: dateModified,
      changeFrequency: 'daily',
      priority: 0.7
    })
  );

  return [...staticPages, ...ukPages, ...articlePages];
}
