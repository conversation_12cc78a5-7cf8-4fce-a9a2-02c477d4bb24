// hooks/useFeatureUsageTracking.ts
'use client';

import { useEffect, useRef, useState } from 'react';
import {
  trackFeatureUseStart,
  trackFeatureUseComplete,
  trackFeatureAbandon
} from '@/lib/ga-events';

interface UseFeatureUsageTrackingOptions {
  featureName: string;
  autoTrackStart?: boolean;
  timeoutMs?: number;
}

interface FeatureUsageTracking {
  startTracking: () => void;
  completeTracking: () => void;
  abandonTracking: () => void;
  isTracking: boolean;
}

export function useFeatureUsageTracking({
  featureName,
  autoTrackStart = true,
  timeoutMs = 300000 // 5 minutes default timeout
}: UseFeatureUsageTrackingOptions): FeatureUsageTracking {
  const [isTracking, setIsTracking] = useState(false);
  const startTimeRef = useRef<number | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startTracking = () => {
    if (isTracking) return;

    setIsTracking(true);
    startTimeRef.current = Date.now();

    // Track feature start
    trackFeatureUseStart(featureName);

    // Set timeout for abandonment
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (isTracking) {
        abandonTracking();
      }
    }, timeoutMs);
  };

  const completeTracking = () => {
    if (!isTracking) return;

    const timeSpent = startTimeRef.current
      ? Math.round((Date.now() - startTimeRef.current) / 1000)
      : undefined;

    // Track completion
    trackFeatureUseComplete(featureName, timeSpent);

    // Cleanup
    setIsTracking(false);
    startTimeRef.current = null;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  const abandonTracking = () => {
    if (!isTracking) return;

    // Track abandonment
    trackFeatureAbandon(featureName);

    // Cleanup
    setIsTracking(false);
    startTimeRef.current = null;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  // Auto-start tracking when component mounts
  useEffect(() => {
    if (autoTrackStart) {
      startTracking();
    }

    // Cleanup on unmount
    return () => {
      if (isTracking) {
        abandonTracking();
      }
    };
  }, [featureName, autoTrackStart]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    startTracking,
    completeTracking,
    abandonTracking,
    isTracking
  };
}

// Simplified hook for common use cases
export function useSimpleFeatureTracking(featureName: string) {
  const { completeTracking } = useFeatureUsageTracking({
    featureName,
    autoTrackStart: true
  });

  return { onFeatureComplete: completeTracking };
}
