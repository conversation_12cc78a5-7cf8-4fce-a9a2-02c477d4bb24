'use client';

import { CallToAction } from 'components/CallToAction';
import { RecentArticles } from 'components/articles/RecentArticles';
import { FAQItem } from 'lib/faq';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from 'components/ui/accordion';
import {
  FileX,
  Image,
  Users,
  Table,
  Type,
  MapPin,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Download
} from 'lucide-react';
import AudioPlayer from '../../components/AudioPlayer';
import clsx from 'clsx';

interface AtsCvFormattingContentProps {
  faqs: FAQItem[];
}

/**
 * Section metadata (helps if we ever add a sticky TOC component)
 */
const sections = [
  { id: 'introduction', h2: 'The Silent CV Rejection Crisis in the UK' },
  {
    id: 'ats-explained',
    h2: 'Why Modern ATS Systems Are More Sophisticated But Still Limited'
  },
  {
    id: 'seven-mistakes',
    h2: 'The Seven Deadly CV Formatting Sins That Cause Instant Rejection'
  },
  {
    id: 'uk-challenges',
    h2: 'UK‑Specific Challenges Make ATS Optimisation Even More Critical'
  },
  {
    id: 'expert-recommendations',
    h2: 'Expert Recommendations for Beating ATS Systems Ethically'
  },
  {
    id: 'take-action',
    h2: 'Take Action Now to Rescue Your Job Search from ATS Rejection'
  },
  { id: 'faqs', h2: 'Frequently Asked Questions' }
] as const;

// Tailwind colour helpers
const colourClass = {
  red: {
    card: 'bg-red-500/10 border-red-400/20',
    text: 'text-red-400'
  },
  blue: {
    card: 'bg-blue-500/10 border-blue-400/20',
    text: 'text-blue-400'
  },
  yellow: {
    card: 'bg-yellow-500/10 border-yellow-400/20',
    text: 'text-yellow-400'
  },
  green: {
    card: 'bg-green-500/10 border-green-400/20',
    text: 'text-green-400'
  },
  purple: {
    card: 'bg-purple-500/20 border-purple-400/30',
    text: 'text-purple-400'
  }
} as const;

type ColourKey = keyof typeof colourClass;

export default function AtsCvFormattingContent({
  faqs
}: AtsCvFormattingContentProps) {
  return (
    <div className="relative z-10 w-full overflow-x-hidden ">
      <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 md:px-8 pb-20">
        <div className="relative isolate min-h-screen scroll-smooth">
          {/* HERO */}
          <section className="mx-auto  text-center max-w-5xl py-16 sm:py-20">
            <div className="inline-flex items-center gap-2 bg-red-500/20 border border-red-400/30 rounded-full px-4 py-2 mb-6">
              <AlertTriangle className="w-4 h-4 text-red-400" />
              <span className="text-red-300 text-sm font-medium">
                75% CV Rejection Rate
              </span>
            </div>
            <h1 className="font-roboto-condensed font-bold leading-tight tracking-[-0.05em] text-white text-4xl md:text-5xl lg:text-6xl">
              ATS CV{' '}
              <span className="text-yellow-400 text-shadow-yellow">
                Formatting Guide
              </span>
              <br />7 Mistakes That Get UK CVs Rejected
            </h1>
            <p className="mt-4 text-slate-200 mx-auto  text-base sm:text-lg leading-relaxed mb-6">
              Your CV might be perfect for humans but failing every ATS scan. In
              the UK job market,{' '}
              <strong className="text-red-400">
                75% of CVs are rejected by Applicant Tracking Systems before
                reaching human eyes
              </strong>
              , with only 25% ever making it to hiring managers. This silent
              rejection crisis affects thousands of qualified UK job seekers who
              never understand why their applications vanish into digital black
              holes.
            </p>
            <div className="w-full mx-auto mb-6">
              <AudioPlayer
                storageKey="audio/Beating-the-ATS-UK-CV-Formatting-Guide.wav"
                title="Listen to this guide"
                duration="18:42"
              />
            </div>
            <CallToAction
              label="Test My CV for UK Jobs Now"
              sublabel="Free ATS compatibility check"
              href="/ats-resume-checker"
            />
          </section>

          {/* KEY STATS */}
          <section className="mb-16">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6  mx-auto">
              {[
                {
                  value: '75%',
                  desc: 'CVs rejected by UK ATS systems',
                  colour: 'red'
                },
                {
                  value: '70%',
                  desc: 'UK businesses use ATS software',
                  colour: 'blue'
                },
                {
                  value: '37%',
                  desc: 'UK employers struggle to fill vacancies',
                  colour: 'yellow'
                }
              ].map((s) => (
                <div
                  key={s.value}
                  className={clsx(
                    'rounded-xl p-6',
                    colourClass[s.colour as ColourKey].card
                  )}
                >
                  <div
                    className={clsx(
                      'text-3xl font-bold mb-2',
                      colourClass[s.colour as ColourKey].text
                    )}
                  >
                    {s.value}
                  </div>
                  <div className="text-slate-300">{s.desc}</div>
                </div>
              ))}
            </div>
          </section>

          {/* SECTION LOOP */}
          {sections.map(({ id, h2 }) => {
            if (id === 'introduction') {
              return (
                <section id={id} key={id} className="mt-16 text-left  mx-auto">
                  <h2 className="text-2xl sm:text-3xl font-semibold mb-6 text-yellow-400">
                    {h2}
                  </h2>
                  <div className="space-y-6 text-slate-200 text-base sm:text-lg">
                    <p>
                      ATS technology now dominates UK recruitment, with{' '}
                      <strong>
                        70% of enterprise businesses using automated CV
                        screening software
                      </strong>
                      . Major employers — including the NHS, UK Civil Service,
                      Amazon UK, Tesco, and HSBC — rely on these systems to
                      manage overwhelming application volumes. Yet most job
                      seekers remain unaware that their beautifully designed CVs
                      are being systematically rejected by machines that
                      can&apos;t read complex formatting, leaving qualified
                      candidates invisible to recruiters who would otherwise be
                      interested.
                    </p>
                    <p>
                      The financial stakes are significant — with the average
                      cost of hiring an employee in the UK reaching £6,125
                      according to CIPD research. Employers depend on ATS
                      efficiency while job seekers face an estimated{' '}
                      <strong>
                        37% of UK employers currently struggling with
                        hard‑to‑fill vacancies
                      </strong>
                      . Understanding and avoiding these seven critical
                      formatting mistakes could be the difference between
                      landing interviews and experiencing endless rejection.
                    </p>
                  </div>
                </section>
              );
            }

            if (id === 'ats-explained') {
              return (
                <section id={id} key={id} className="mt-16 text-left  mx-auto">
                  <h2 className="text-2xl sm:text-3xl font-semibold mb-6 text-yellow-400">
                    {h2}
                  </h2>
                  <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 space-y-6">
                    <p className="text-slate-200 text-base sm:text-lg">
                      <strong>
                        Applicant Tracking Systems have evolved significantly in
                        2025, with 79% of organisations now integrating AI
                        capabilities
                      </strong>{' '}
                      into their recruitment processes. The NHS operates Oleeo
                      and Tribepad integrated with NHS Jobs; the Civil Service
                      rolled out its custom platform “Civil Service Jobs vX” in
                      2024; Amazon UK runs a Salesforce‑based stack; Tesco
                      relies on Tribepad for streamlined hiring. Despite these
                      advances,{' '}
                      <strong>
                        ATS parsing accuracy remains disappointingly low at
                        60‑80%
                      </strong>{' '}
                      even for well‑formatted resumes.
                    </p>
                    <p className="text-slate-200 text-base sm:text-lg">
                      Current market leaders include{' '}
                      <strong>
                        Workday (37% of Fortune 500), Oracle Taleo (22.4% share)
                        and SAP SuccessFactors (13.4% usage)
                      </strong>
                      . Amy O’Callaghan, Head of Talent at Ciphr, warns: “If you
                      have outdated or inefficient recruitment technology, or a
                      poorly set‑up ATS, all your processes are going to take
                      longer than they should.”
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <h3 className="font-semibold text-white mb-3">
                          Major UK ATS Platforms
                        </h3>
                        {[
                          'NHS: Oleeo & Tribepad',
                          'Civil Service Jobs vX',
                          'Amazon UK: Salesforce‑based',
                          'Tesco: Tribepad system'
                        ].map((p) => (
                          <div
                            key={p}
                            className="flex items-center gap-2 text-sm"
                          >
                            <CheckCircle2 className="h-4 w-4 text-green-400" />
                            <span className="text-slate-300">{p}</span>
                          </div>
                        ))}
                      </div>
                      <div>
                        <h3 className="font-semibold text-white mb-3">
                          Typical Parsing Stages
                        </h3>
                        {[
                          'Text extraction via OCR',
                          'Section identification',
                          'Keyword matching'
                        ].map((step, i) => (
                          <div
                            key={step}
                            className="flex items-center gap-2 text-sm"
                          >
                            <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                              {i + 1}
                            </span>
                            <span className="text-slate-300">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </section>
              );
            }

            if (id === 'seven-mistakes') {
              const mistakes = [
                {
                  icon: FileX,
                  title:
                    'Mistake 1: Complex Multi‑Column Layouts Destroy Content Organisation',
                  problem:
                    'Two‑column template with work experience on the left and skills/education on the right — visually appealing but unparseable.',
                  solution:
                    'Single‑column, reverse‑chronological format with clear section breaks so ATS can categorise data.',
                  impact:
                    'Filters out thousands of qualified candidates before human review. Andrew Fennell (reviewed 10,000+ CVs) confirms reverse‑chronological is the only reliably ATS‑friendly layout.'
                },
                {
                  icon: Image,
                  title:
                    'Mistake 2: Graphics and Images Create Information Blackouts',
                  problem:
                    'Skill bars, logos, headshot, infographic timeline — all rendered invisible to ATS.',
                  solution:
                    'Convert visuals to plain text: “Skills: Microsoft Excel (Advanced – 5 yrs), …”.',
                  impact:
                    '100% of graphics stripped. TopCV UK: “If your CV isn’t formatted for ATS, recruiters will never know you exist.”'
                },
                {
                  icon: Users,
                  title:
                    'Mistake 3: Creative Section Headers Confuse Categorisation',
                  problem:
                    'Headers like “My Journey”, “Academic Adventures”, etc.',
                  solution:
                    'Use standard labels: “Work Experience”, “Education”, “Skills”, “Certifications”.',
                  impact:
                    'Non‑standard headers break field mapping causing CVs to look incomplete.'
                },
                {
                  icon: Table,
                  title: 'Mistake 4: Tables/Text Boxes Scramble Information',
                  problem:
                    'Education/skills laid out in tables with multiple columns.',
                  solution: 'Linear bullet or sentence format.',
                  impact:
                    'Approx. 15% of UK CVs from popular templates fail here.'
                },
                {
                  icon: Type,
                  title:
                    'Mistake 5: Fancy Fonts & Formatting Cause Encoding Chaos',
                  problem: 'Custom fonts, coloured text, varied sizing.',
                  solution:
                    'Arial/Calibri, 10‑12pt body, 14‑16pt headers, black text.',
                  impact:
                    'Characters convert to symbols, breaking keyword matching.'
                },
                {
                  icon: MapPin,
                  title: 'Mistake 6: Header/Footer Contact Info Is Unreadable',
                  problem: 'Details placed in decorative header/footer.',
                  solution: 'Move contact info into main body near top.',
                  impact:
                    '25% of ATS skip headers/footers – recruiters cannot reach candidates.'
                },
                {
                  icon: Download,
                  title: 'Mistake 7: Wrong File Formats Cause Upload Failures',
                  problem:
                    'Scanned image‑PDF, .pages, .odt or password‑protected files.',
                  solution:
                    '.docx (preferred) or clean text‑based PDF under 5MB, no passwords.',
                  impact:
                    '1.2% of submissions fail outright due to incompatible format.'
                }
              ];
              return (
                <section id={id} key={id} className="mt-16 max-w-6xl mx-auto">
                  <h2 className="text-2xl sm:text-3xl font-semibold mb-8 text-yellow-400">
                    {h2}
                  </h2>
                  <div className="space-y-12">
                    {mistakes.map((m, idx) => {
                      const Icon = m.icon;
                      return (
                        <div
                          key={idx}
                          className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8"
                        >
                          <div className="flex items-start gap-4 mb-6">
                            <div className="flex-shrink-0 w-12 h-12 bg-red-500/20 border border-red-400/30 rounded-xl flex items-center justify-center">
                              <Icon className="h-6 w-6 text-red-400" />
                            </div>
                            <div className="flex-1">
                              <h3 className="text-xl font-semibold text-white mb-3">
                                {m.title}
                              </h3>
                              <p className="text-slate-200 mb-6">{m.problem}</p>
                            </div>
                          </div>
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                            {/* Before */}
                            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
                              <div className="flex items-center gap-2 mb-3">
                                <XCircle className="h-5 w-5 text-red-400" />
                                <h4 className="font-semibold text-red-400">
                                  Before (Problematic)
                                </h4>
                              </div>
                              <p className="text-slate-300 text-sm">
                                {m.problem}
                              </p>
                            </div>
                            {/* After */}
                            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-6">
                              <div className="flex items-center gap-2 mb-3">
                                <CheckCircle2 className="h-5 w-5 text-green-400" />
                                <h4 className="font-semibold text-green-400">
                                  After (ATS‑Friendly)
                                </h4>
                              </div>
                              <p className="text-slate-300 text-sm">
                                {m.solution}
                              </p>
                            </div>
                          </div>
                          <div className="bg-yellow-500/10 border border-yellow-400/20 rounded-lg p-4">
                            <p className="text-yellow-200 text-sm">
                              <strong>Impact:</strong> {m.impact}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </section>
              );
            }

            if (id === 'uk-challenges') {
              return (
                <section id={id} key={id} className="mt-16 text-left  mx-auto">
                  <h2 className="text-2xl sm:text-3xl font-semibold mb-6 text-yellow-400">
                    {h2}
                  </h2>
                  <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 space-y-6">
                    <p className="text-slate-200 text-base sm:text-lg">
                      <strong>
                        The UK job market presents unique ATS challenges
                      </strong>{' '}
                      that compound standard formatting issues. Civil Service
                      roles demand anonymised CVs, NHS Jobs mixes long
                      application forms with CV uploads, and right‑to‑work
                      verification post‑Brexit now feeds directly into many ATS
                      platforms.
                    </p>
                    <p className="text-slate-200 text-base sm:text-lg">
                      Regional stats:{' '}
                      <strong>112,000 NHS vacancies (March 2023)</strong>; Civil
                      Service employs 542,840 people via its own platform; CIPD
                      finds{' '}
                      <strong>
                        44% of businesses fail to reply to candidates within
                        four weeks
                      </strong>
                      , often due to ATS filtering qualified applicants.
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <h3 className="font-semibold text-white mb-3">
                          UK‑Specific Factors
                        </h3>
                        {[
                          'Civil Service anonymisation',
                          'British terminology (A‑levels, GCSEs)',
                          'DD/MM/YYYY date format',
                          'GDPR compliance requirements',
                          'Brexit right‑to‑work feeds'
                        ].map((f) => (
                          <div
                            key={f}
                            className="flex items-center gap-2 text-sm"
                          >
                            <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                            <span className="text-slate-300">{f}</span>
                          </div>
                        ))}
                      </div>
                      <div>
                        <h3 className="font-semibold text-white mb-3">
                          Major UK Employers Using ATS
                        </h3>
                        {[
                          'Civil Service – custom platform (542k staff)',
                          'NHS – Oleeo & Tribepad',
                          'Barclays – SuccessFactors',
                          'HSBC – Workday'
                        ].map((e) => (
                          <div key={e} className="text-slate-300 text-sm">
                            {e}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </section>
              );
            }

            if (id === 'expert-recommendations') {
              return (
                <section id={id} key={id} className="mt-16 text-left  mx-auto">
                  <h2 className="text-2xl sm:text-3xl font-semibold mb-6 text-yellow-400">
                    {h2}
                  </h2>
                  <p className="text-slate-200 mb-6 text-base sm:text-lg">
                    Industry professionals agree: optimise for both ATS parsing
                    and human readability. Andrew Fennell notes,{' '}
                    <strong>“Every CV can be ATS‑optimised”</strong> through
                    strategic formatting and genuine, keyword‑rich content (no
                    stuffing).
                  </p>
                  <div className="space-y-6">
                    {/* Keyword optimisation */}
                    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                      <h3 className="font-semibold text-green-400 mb-4">
                        Strategic Keyword Optimisation
                      </h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                        {[
                          'Include exact job titles in headings',
                          'Use full terms & acronyms',
                          'Mirror language from job post',
                          'Maintain authenticity'
                        ].map((tip) => (
                          <div key={tip} className="flex items-center gap-2">
                            <CheckCircle2 className="h-4 w-4 text-green-400" />
                            <span className="text-slate-300">{tip}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    {/* Technical checklist */}
                    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                      <h3 className="font-semibold text-blue-400 mb-4">
                        Technical Optimisation Checklist
                      </h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                        {[
                          'Reverse‑chronological format',
                          'Standard section headers',
                          'Consistent DD/MM/YYYY dates',
                          'Arial/Calibri fonts',
                          'Single‑column layout',
                          '.docx or clean PDF'
                        ].map((check) => (
                          <div key={check} className="flex items-center gap-2">
                            <CheckCircle2 className="h-4 w-4 text-green-400" />
                            <span className="text-slate-300">{check}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </section>
              );
            }

            if (id === 'take-action') {
              return (
                <section id={id} key={id} className="mt-16  mx-auto">
                  <h2 className="text-2xl sm:text-3xl font-semibold mb-6 text-yellow-400">
                    {h2}
                  </h2>
                  <div className="bg-gradient-to-r from-yellow-400/20 to-yellow-600/20 border border-yellow-400/30 rounded-xl p-8 space-y-8">
                    <p className="text-slate-200 text-base sm:text-lg">
                      <strong>
                        Your next career opportunity depends on implementing
                        these ATS fixes now.
                      </strong>{' '}
                      Audit your CV against the seven mistakes, remove
                      graphics/tables, adopt a single‑column layout, and save as
                      .docx.
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 text-left">
                      <div>
                        <h3 className="font-semibold text-yellow-400 mb-4">
                          Immediate Action Steps
                        </h3>
                        {[
                          'Run our free ATS checker',
                          'Remove visual elements',
                          'Switch to single‑column format',
                          'Move contact info into body',
                          'Save in .docx format',
                          'Tailor keywords for every job'
                        ].map((step, i) => (
                          <div
                            key={step}
                            className="flex items-start gap-2 text-sm mb-2"
                          >
                            <span className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center text-black text-xs font-bold">
                              {i + 1}
                            </span>
                            <span className="text-slate-300">{step}</span>
                          </div>
                        ))}
                      </div>
                      <div>
                        <h3 className="font-semibold text-yellow-400 mb-4">
                          Why This Matters
                        </h3>
                        {[
                          '37% of employers can’t fill roles',
                          '£43 bn recruitment industry',
                          'Optimised CV unlocks hidden jobs'
                        ].map((w) => (
                          <div
                            key={w}
                            className="flex items-center gap-2 text-sm mb-2"
                          >
                            <AlertTriangle className="h-4 w-4 text-yellow-400" />
                            <span className="text-slate-300">{w}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="bg-blue-500/10 border border-blue-400/20 rounded-lg p-6 text-left space-y-2 text-sm">
                      {[
                        'Use parsing‑test tools before submitting',
                        'Research each employer’s ATS & process',
                        'Incorporate keywords naturally',
                        'Quantify achievements (e.g. “increased sales 28%”)'
                      ].map((tip) => (
                        <div key={tip} className="flex items-center gap-2">
                          <CheckCircle2 className="h-4 w-4 text-green-400" />
                          <span className="text-slate-300">{tip}</span>
                        </div>
                      ))}
                    </div>
                    <CallToAction
                      label="Check Your CV's ATS Compatibility Now"
                      sublabel="Free analysis – no signup"
                      href="/ats-resume-checker"
                    />
                    <p className="text-slate-300 text-xs">
                      Transform your job search by ensuring both ATS and human
                      recruiters can access your qualifications.
                    </p>
                  </div>
                </section>
              );
            }

            if (id === 'faqs') {
              return (
                <section id={id} key={id} className="mt-16">
                  <h2 className="text-2xl sm:text-3xl font-semibold mb-6 text-yellow-400">
                    {h2}
                  </h2>
                  <Accordion
                    type="single"
                    collapsible
                    className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden  mx-auto"
                  >
                    {faqs.map(({ question, answer }, i) => (
                      <AccordionItem
                        key={i}
                        value={`item-${i}`}
                        className="border-white/10"
                      >
                        <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-sm sm:text-lg font-medium text-left">
                          {question}
                        </AccordionTrigger>
                        <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed text-sm sm:text-base">
                          {answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </section>
              );
            }
            return null;
          })}

          {/* ADDITIONAL FEATURES */}
          <section className="mt-16">
            <h2 className="text-2xl sm:text-3xl font-semibold mb-6 text-yellow-400">
              Complete Your UK Job Application Success
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6  mx-auto">
              {[
                {
                  title: 'AI CV Builder',
                  desc: 'Generate ATS‑optimised CVs tailor‑made for the UK market.',
                  href: '/ai-cv-builder',
                  colour: 'blue',
                  Icon: FileX
                },
                {
                  title: 'Cover Letter Generator',
                  desc: 'Create tailored cover letters that complement your CV.',
                  href: '/ai-cover-letter',
                  colour: 'green',
                  Icon: Type
                },
                {
                  title: 'Interview Coach',
                  desc: 'Practise mock interviews with AI feedback to ace UK interviews.',
                  href: '/ai-interview-coach',
                  colour: 'purple',
                  Icon: Users
                }
              ].map((card) => (
                <div
                  key={card.title}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center"
                >
                  <div
                    className={clsx(
                      'w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4',
                      colourClass[card.colour as ColourKey].card
                    )}
                  >
                    <card.Icon
                      className={clsx(
                        'h-6 w-6',
                        colourClass[card.colour as ColourKey].text
                      )}
                    />
                  </div>
                  <h3 className="font-semibold text-white mb-2">
                    {card.title}
                  </h3>
                  <p className="text-slate-300 text-sm mb-4">{card.desc}</p>
                  <a
                    href={card.href}
                    className={clsx(
                      'transition-colors text-sm font-medium',
                      colourClass[card.colour as ColourKey].text,
                      'hover:opacity-80'
                    )}
                  >
                    Learn More →
                  </a>
                </div>
              ))}
            </div>
          </section>

          {/* RECENT ARTICLES */}
          <section className="mt-16  mx-auto">
            <RecentArticles />
          </section>

          {/* FINAL CTA */}
          <section className="mt-16 bg-gradient-to-r from-slate-800/80 to-slate-900/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Beat UK ATS Systems?
            </h2>
            <p className="text-lg text-slate-300 mb-8 max-w-2xl mx-auto">
              Don&apos;t let your perfect CV get lost in the ATS black hole. Our
              free UK ATS checker simulates the exact systems used by the NHS,
              FTSE 100 companies and major UK employers.
            </p>
            <CallToAction
              label="Test My CV for UK Jobs Now"
              sublabel="Free ATS compatibility check"
              href="/ats-resume-checker"
            />
            <p className="text-xs text-slate-300 mt-4">
              No credit card required • Instant results • Used by 10,000+ UK job
              seekers
            </p>
          </section>
        </div>
      </div>
    </div>
  );
}
