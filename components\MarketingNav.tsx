import Link from 'next/link';
import {
  FileText,
  CheckCircle,
  PenTool,
  Users,
  Puzzle,
  BookOpen,
  Sparkles,
  ArrowRight,
  Zap,
  Star,
  LucideIcon,
  AlertTriangle,
  Search
} from 'lucide-react';

// ============================================================================
// LIGHTWEIGHT FEATURE DATA - NO HEAVY IMPORTS
// ============================================================================

// Define features inline instead of importing from heavy constants file
const LIGHTWEIGHT_FEATURES = [
  {
    title: 'CV Improvement',
    slug: 'ai-cv-builder',
    description: 'Create professional CVs with AI assistance',
    icon: '📝',
    public: true,
    hidden: false
  },
  {
    title: 'ATS Checker',
    slug: 'ats-resume-checker',
    description: 'Check if your CV passes ATS systems',
    icon: '🔍',
    public: true,
    hidden: false
  },
  {
    title: 'Cover Letters',
    slug: 'ai-cover-letter',
    description: 'Generate tailored cover letters instantly',
    icon: '✉️',
    public: true,
    hidden: false
  },
  {
    title: 'Career Coach',
    slug: 'ai-interview-coach',
    description: 'Practice interviews with AI feedback',
    icon: '🎤',
    public: true,
    hidden: false
  },
  {
    title: 'What is ATS?',
    slug: 'what-is-ats-uk',
    description: 'Learn about Applicant Tracking Systems',
    icon: '❓',
    public: true,
    hidden: false
  },
  {
    title: 'ATS CV Guide',
    slug: 'ats-cv-formatting-guide',
    description: 'Format your CV for ATS compatibility',
    icon: '📋',
    public: true,
    hidden: false
  },
  {
    title: 'CV Scanner Guide',
    slug: 'free-cv-scanner-guide-uk-2025',
    description: 'Step-by-step CV scanning instructions',
    icon: '🔍',
    public: true,
    hidden: false
  },
  {
    title: 'Full Platform',
    slug: 'platform',
    description: 'Access all our AI-powered job search tools in one place',
    icon: '🚀',
    public: true,
    hidden: false
  }
] as const;

// Icon mapping for your specific features
const getFeatureIcon = (slug: string): LucideIcon => {
  const iconMap: Record<string, LucideIcon> = {
    'ai-cv-builder': FileText,
    'ats-resume-checker': CheckCircle,
    'ai-cover-letter': PenTool,
    'ai-interview-coach': Users,
    platform: Puzzle,
    'what-is-ats-uk': BookOpen,
    'ats-cv-formatting-guide': AlertTriangle,
    'free-cv-scanner-guide-uk-2025': Search
  };

  return iconMap[slug] || FileText;
};

// ============================================================================
// LIGHTWEIGHT COMPONENT - NO HEAVY DEPENDENCIES
// ============================================================================

interface MarketingNavItemsProps {
  asDropdownItem?: boolean;
  onItemClick?: () => void;
}

export function MarketingNavItems({
  asDropdownItem = true,
  onItemClick
}: MarketingNavItemsProps) {
  // Use lightweight features instead of importing heavy constants
  const publicFeatures = LIGHTWEIGHT_FEATURES.filter(
    (f) => f.public && !f.hidden
  );

  if (!asDropdownItem) {
    // Fallback to simple list for mobile/other cases
    return publicFeatures.map(({ title, slug, description }) => (
      <li key={slug}>
        <Link
          href={`/${slug}`}
          prefetch={false}
          className="block w-full p-3 rounded-lg hover:bg-white/10 focus:bg-white/10 transition-colors duration-200"
          onClick={onItemClick}
        >
          <span className="block text-white font-medium text-sm leading-tight">
            {title}
          </span>
          {description && (
            <span className="block text-white/70 text-xs mt-1 leading-relaxed">
              {description}
            </span>
          )}
        </Link>
      </li>
    ));
  }

  // Get core features for grid display - main tools only
  const coreFeatures = publicFeatures.filter(
    (f) =>
      f.slug &&
      [
        'ai-cv-builder',
        'ats-resume-checker',
        'ai-cover-letter',
        'ai-interview-coach'
      ].includes(f.slug)
  );

  const platformFeature = publicFeatures.find((f) => f.slug === 'platform');

  const resourceFeatures = publicFeatures.filter(
    (f) =>
      f.slug &&
      [
        'what-is-ats-uk',
        'ats-cv-formatting-guide',
        'free-cv-scanner-guide-uk-2025'
      ].includes(f.slug)
  );

  return (
    <div
      className="p-4"
      style={{ width: '560px', minWidth: '560px', maxWidth: '560px' }}
    >
      {/* Header */}
      <div className="flex items-center gap-2 mb-4">
        <div className="p-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-600">
          <Zap className="w-4 h-4 text-white" />
        </div>
        <h3 className="text-sm font-semibold text-white">
          AI-Powered Job Tools
        </h3>
        <Star className="w-3 h-3 text-yellow-400 ml-1" />
      </div>

      {/* Main Features Grid - Now shows 4 items properly */}
      <div className="mb-6">
        <div className="grid grid-cols-2 gap-3" style={{ width: '100%' }}>
          {coreFeatures
            .slice(0, 4)
            .map(({ title, slug, description, icon }) => {
              if (!slug) return null;

              const Icon = getFeatureIcon(slug);
              const isPopular = [
                'ats-resume-checker',
                'ai-cv-builder'
              ].includes(slug);

              return (
                <div key={slug} className="contents">
                  <Link
                    href={`/${slug}`}
                    prefetch={false}
                    className="relative flex flex-col p-3 rounded-xl hover:bg-white/10 focus:bg-white/10 transition-all duration-200 group border border-white/10 hover:border-white/20 aspect-square"
                    onClick={onItemClick}
                  >
                    {/* Popular badge */}
                    {isPopular && (
                      <div className="absolute -top-1 -right-1 px-1.5 py-0.5 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full">
                        <span className="text-xs font-medium text-white">
                          ★
                        </span>
                      </div>
                    )}

                    {/* Icon */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20 group-hover:from-blue-500/30 group-hover:to-purple-500/30 transition-all duration-200">
                        {icon ? (
                          <span className="text-base">{icon}</span>
                        ) : (
                          <Icon className="w-4 h-4 text-white" />
                        )}
                      </div>
                      <ArrowRight className="w-3 h-3 text-white/30 group-hover:text-white/60 group-hover:translate-x-0.5 transition-all duration-200" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 flex flex-col">
                      <h4 className="text-white font-medium text-sm leading-tight mb-1">
                        {title}
                      </h4>
                      {description && (
                        <p className="text-white/60 text-xs leading-relaxed flex-1">
                          {description.length > 60
                            ? `${description.substring(0, 60)}...`
                            : description}
                        </p>
                      )}
                    </div>
                  </Link>
                </div>
              );
            })}
        </div>
      </div>

      {/* Platform Feature - Full Width */}
      {platformFeature && (
        <div className="mb-6">
          <Link
            href={`/${platformFeature.slug}`}
            prefetch={false}
            className="flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-blue-600/20 to-purple-600/20 hover:from-blue-600/30 hover:to-purple-600/30 border border-blue-500/20 hover:border-blue-500/30 transition-all duration-200 group"
            onClick={onItemClick}
          >
            <div className="p-3 rounded-xl bg-white/10 group-hover:bg-white/15 transition-colors">
              {platformFeature.icon ? (
                <span className="text-xl">{platformFeature.icon}</span>
              ) : (
                <Puzzle className="w-6 h-6 text-white" />
              )}
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="text-white font-semibold text-sm">
                  {platformFeature.title}
                </h4>
                <span className="px-2 py-0.5 bg-green-500/20 text-green-300 text-xs font-medium rounded-full">
                  Complete Suite
                </span>
              </div>
              <p className="text-white/70 text-xs leading-relaxed">
                {platformFeature.description}
              </p>
            </div>

            <ArrowRight className="w-4 h-4 text-white/60 group-hover:translate-x-1 transition-transform duration-200" />
          </Link>
        </div>
      )}

      {/* Quick Links - Now includes Free CV Scanner Guide */}
      {resourceFeatures.length > 0 && (
        <div className="mb-6">
          <h4 className="text-xs font-medium text-white/60 uppercase tracking-wide mb-3">
            Quick Links
          </h4>
          <div className="flex flex-wrap gap-2">
            {resourceFeatures.map(({ title, slug, icon }) => {
              if (!slug) return null;

              const Icon = getFeatureIcon(slug);
              return (
                <div key={slug} className="contents">
                  <Link
                    href={`/${slug}`}
                    prefetch={false}
                    className="flex items-center gap-2 px-3 py-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 group"
                    onClick={onItemClick}
                  >
                    {icon ? (
                      <span className="text-sm">{icon}</span>
                    ) : (
                      <Icon className="w-3 h-3 text-white/80" />
                    )}
                    <span className="text-white/80 text-xs font-medium">
                      {title}
                    </span>
                  </Link>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Footer CTA */}
      <div className="border-t border-white/10 pt-4">
        <Link
          href="/credits"
          className="flex items-center justify-center gap-2 w-full p-3 bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 rounded-lg transition-all duration-200 group shadow-lg"
          onClick={onItemClick}
        >
          <Sparkles className="w-4 h-4 text-white group-hover:scale-110 transition-transform" />
          <span className="text-white font-medium text-sm">
            Upgrade to Premium
          </span>
          <ArrowRight className="w-3 h-3 text-white/80 group-hover:translate-x-0.5 transition-transform" />
        </Link>
      </div>
    </div>
  );
}
