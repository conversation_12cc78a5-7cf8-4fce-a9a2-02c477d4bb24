# Builder stage
FROM node:20-slim AS builder
WORKDIR /app

# Install Python and build dependencies for canvas
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    build-essential \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Copy package files and set Python path
COPY package*.json yarn.lock ./
ENV PYTHON=/usr/bin/python3

# Install dependencies with specific flags for canvas
RUN yarn install --frozen-lockfile --network-timeout 1000000 && \
    yarn add canvas@2.11.2 --build-from-source

# Copy the rest of the application code
COPY . .

# Set environment variables
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_URL
ARG NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ARG STRIPE_SECRET_KEY
ARG STRIPE_WEBHOOK_SECRET
ARG PYTHON_BACKEND_URL
ARG BRIGHT_DATA_API_KEY
ARG SUPABASE_SERVICE_ROLE_KEY
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY
ARG NEXT_PUBLIC_GA_MEASUREMENT_ID
ARG SENDGRID_API_KEY
ARG CONTACT_FORM_TO_EMAIL
ARG CONTACT_FORM_FROM_EMAIL
ARG NEXT_PUBLIC_CLARITY_PROJECT_ID
ARG NEXT_PUBLIC_GOOGLE_ADS_ID

ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_URL=${NEXT_PUBLIC_URL}
ENV NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
ENV STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
ENV STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
ENV PYTHON_BACKEND_URL=${PYTHON_BACKEND_URL}
ENV BRIGHT_DATA_API_KEY=${BRIGHT_DATA_API_KEY}
ENV NODE_ENV=production
ENV SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
ENV NEXT_PUBLIC_GA_MEASUREMENT_ID=${NEXT_PUBLIC_GA_MEASUREMENT_ID}
# Add SendGrid configuration environment variables
ENV SENDGRID_API_KEY=${SENDGRID_API_KEY}
ENV CONTACT_FORM_TO_EMAIL=${CONTACT_FORM_TO_EMAIL}
ENV CONTACT_FORM_FROM_EMAIL=${CONTACT_FORM_FROM_EMAIL}
ENV NEXT_PUBLIC_CLARITY_PROJECT_ID=${NEXT_PUBLIC_CLARITY_PROJECT_ID}
ENV NEXT_PUBLIC_GOOGLE_ADS_ID=${NEXT_PUBLIC_GOOGLE_ADS_ID}

# Create .env.production file (with fallback if secret mount fails)
RUN --mount=type=secret,id=env_production,target=/app/.env.production.template \
    if [ -f /app/.env.production.template ]; then \
      cp /app/.env.production.template .env.production; \
    else \
      echo "Warning: .env.production.template not found, creating empty .env.production"; \
      touch .env.production; \
    fi

# Make sure output is set to standalone in next.config.mjs
RUN grep -q "output: 'standalone'" next.config.mjs || echo "Warning: Make sure 'output: \"standalone\"' is in your next.config.mjs"

# Build the application
RUN yarn build

# Production stage
FROM node:20-slim AS runner
WORKDIR /app

# Install runtime dependencies for canvas
RUN apt-get update && apt-get install -y \
    libcairo2 \
    libpango-1.0-0 \
    libjpeg62-turbo \
    libgif7 \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    PORT=8080

# Create a non-root user
RUN groupadd --system --gid 1001 nodejs && \
    useradd --system --uid 1001 --gid nodejs nextjs

# Copy standalone server and required files
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
# Make sure we copy all static files from the build
COPY --from=builder --chown=nextjs:nodejs /app/.next/server ./.next/server
COPY --from=builder --chown=nextjs:nodejs /app/.next/required-server-files.json ./.next/
COPY --from=builder --chown=nextjs:nodejs /app/.next/build-manifest.json ./.next/
COPY --from=builder --chown=nextjs:nodejs /app/.next/prerender-manifest.json ./.next/
COPY --from=builder --chown=nextjs:nodejs /app/.next/routes-manifest.json ./.next/
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Copy environment files if needed
COPY --from=builder --chown=nextjs:nodejs /app/.env* ./

# Switch to non-root user
USER nextjs

# Expose the port
EXPOSE 8080

# Start the application using the server.js file
# This will use process.env.PORT which Cloud Run sets to 8080
CMD ["node", "server.js"]
