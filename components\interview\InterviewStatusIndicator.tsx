import React from 'react';
import { Mic, Volume2, Pause } from 'lucide-react';

// Define props interface for the component
interface InterviewStatusIndicatorProps {
  isSpeaking: boolean;
  isListening: boolean;
  isPaused: boolean;
  isProcessing?: boolean;
}

/**
 * Visual indicator of interview status that responds immediately to state changes
 */
const InterviewStatusIndicator: React.FC<InterviewStatusIndicatorProps> = ({
  isSpeaking,
  isListening,
  isPaused,
  isProcessing = false
}) => {
  // Explicitly log the current state for debugging
  React.useEffect(() => {
    console.log('Status indicator state:', {
      isSpeaking,
      isListening,
      isPaused,
      isProcessing
    });
  }, [isSpeaking, isListening, isPaused, isProcessing]);

  // Determine which status to show based on the current state
  let content;

  if (isPaused) {
    content = (
      <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-amber-100 border border-amber-300 text-amber-700">
        <Pause className="h-5 w-5" />
        <span className="font-medium">Paused</span>
      </div>
    );
  } else if (isSpeaking) {
    content = (
      <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-blue-100 border border-blue-300 text-blue-700 animate-pulse">
        <Volume2 className="h-5 w-5" />
        <span className="font-medium">System Speaking</span>
      </div>
    );
  } else if (isListening) {
    content = (
      <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-green-100 border border-green-300 text-green-700 animate-pulse">
        <Mic className="h-5 w-5" />
        <span className="font-medium">Listening to You</span>
      </div>
    );
  } else {
    // Default state (more visible than before)
    content = (
      <div className="flex items-center gap-2 px-5 py-2 rounded-full bg-[#F6A03C] border border-[#F8B968] text-[#111827]">
        <Mic className="h-5 w-5" />
        <span className="font-medium">Ready</span>
      </div>
    );
  }

  return (
    <div className="flex justify-center my-4 mx-auto max-w-max">{content}</div>
  );
};

export default InterviewStatusIndicator;
