import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { resume, job_description, focus_areas, tone } = await request.json();

    if (!resume || !job_description) {
      return NextResponse.json(
        { error: 'Resume and job description are required' },
        { status: 400 }
      );
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/enhance/resume`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ resume, job_description, focus_areas, tone })
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[resume-enhancement] Backend API error:', errorText);
      return NextResponse.json(
        { error: 'Failed to enhance resume', details: errorText },
        { status: response.status }
      );
    }

    const data = await response.json();

    console.log('[resume-enhancement] API response:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('[resume-enhancement] API Error:', error);
    return NextResponse.json(
      { error: 'Failed to enhance resume' },
      { status: 500 }
    );
  }
}
