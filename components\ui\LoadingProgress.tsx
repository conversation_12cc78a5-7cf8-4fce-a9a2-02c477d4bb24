import React from 'react';
import {
  <PERSON><PERSON>ex<PERSON>,
  <PERSON><PERSON><PERSON>cle,
  Loader2,
  <PERSON>,
  PenTool,
  TrendingUp,
  Upload,
  Wand2
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { PlanType } from '@/app/types/globalTypes';

type StepStatus = 'current' | 'complete' | 'pending';

interface Step {
  icon: React.ElementType;
  label: string;
  status: StepStatus;
  progress: number;
}

interface LoadingProgressProps {
  planType: PlanType;
  progress: number;
  completedSteps: string[];
  feature?: string;
  useSpinner?: boolean;
  useSingleSpinner?: boolean; // New prop for single spinner
  loadingMessage?: string;
}

export const LoadingProgress: React.FC<LoadingProgressProps> = ({
  planType,
  progress,
  completedSteps,
  feature,
  useSpinner = false,
  useSingleSpinner = false,
  loadingMessage
}) => {
  const getStepStatus = (
    isComplete?: boolean,
    isPrevComplete = true
  ): StepStatus => {
    if (isComplete) return 'complete';
    if (!isPrevComplete) return 'pending';
    return 'current';
  };

  const baseSteps: Step[] = [
    {
      icon: Brain,
      label: 'ATS Analysis',
      status: getStepStatus(completedSteps.includes('atsResult')),
      progress: completedSteps.includes('atsResult') ? 100 : progress
    },
    {
      icon: PenTool,
      label: 'Cover Letter Generation',
      status: getStepStatus(
        completedSteps.includes('coverLetter'),
        completedSteps.includes('atsResult')
      ),
      progress: completedSteps.includes('coverLetter') ? 100 : progress
    }
  ];

  const proSteps: Step[] = [
    {
      icon: FileText,
      label: 'Skills Gap Analysis',
      status: getStepStatus(
        completedSteps.includes('skillsGap'),
        completedSteps.includes('coverLetter')
      ),
      progress: completedSteps.includes('skillsGap') ? 100 : progress
    },
    {
      icon: Brain,
      label: 'Skill Improvement Recommendations',
      status: getStepStatus(
        completedSteps.includes('skillImprovements'),
        completedSteps.includes('skillsGap')
      ),
      progress: completedSteps.includes('skillImprovements') ? 100 : progress
    }
  ];

  const ultimateSteps: Step[] = [
    {
      icon: TrendingUp,
      label: 'Market Trends Analysis',
      status: getStepStatus(
        completedSteps.includes('marketTrends'),
        completedSteps.includes('skillImprovements')
      ),
      progress: completedSteps.includes('marketTrends') ? 100 : progress
    }
  ];

  // Define feature-specific steps
  const getFeatureSteps = (): Step[] => {
    switch (feature) {
      case 'CV Upload':
        return [
          {
            icon: Upload,
            label: 'Processing Document',
            status: getStepStatus(completedSteps.includes('fileUpload')),
            progress: completedSteps.includes('fileUpload') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Analyzing CV',
            status: getStepStatus(
              completedSteps.includes('resumeAnalysis'),
              completedSteps.includes('fileUpload')
            ),
            progress: completedSteps.includes('resumeAnalysis') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Finalizing',
            status: getStepStatus(
              completedSteps.includes('complete'),
              completedSteps.includes('resumeAnalysis')
            ),
            progress: completedSteps.includes('complete') ? 100 : progress
          }
        ];

      case 'ATS':
        return [
          {
            icon: FileText,
            label: 'Analyzing CV Format',
            status: getStepStatus(completedSteps.includes('formatAnalysis')),
            progress: completedSteps.includes('formatAnalysis') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Parsing Keywords',
            status: getStepStatus(
              completedSteps.includes('keywordParsing'),
              completedSteps.includes('formatAnalysis')
            ),
            progress: completedSteps.includes('keywordParsing') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Generating Recommendations',
            status: getStepStatus(
              completedSteps.includes('recommendations'),
              completedSteps.includes('keywordParsing')
            ),
            progress: completedSteps.includes('recommendations')
              ? 100
              : progress
          }
        ];

      case 'Skill Gap Analysis':
        return [
          {
            icon: FileText,
            label: 'Analyzing CV Skills',
            status: getStepStatus(completedSteps.includes('skillsAnalysis')),
            progress: completedSteps.includes('skillsAnalysis') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Comparing with Job Requirements',
            status: getStepStatus(
              completedSteps.includes('comparison'),
              completedSteps.includes('skillsAnalysis')
            ),
            progress: completedSteps.includes('comparison') ? 100 : progress
          },
          {
            icon: TrendingUp,
            label: 'Identifying Gaps',
            status: getStepStatus(
              completedSteps.includes('gapIdentification'),
              completedSteps.includes('comparison')
            ),
            progress: completedSteps.includes('gapIdentification')
              ? 100
              : progress
          }
        ];

      case 'Learning Resources':
        return [
          {
            icon: Brain,
            label: 'Analyzing Skills Gap',
            status: getStepStatus(completedSteps.includes('analyzing')),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: FileText,
            label: 'Finding Learning Resources',
            status: getStepStatus(
              completedSteps.includes('searching'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('searching') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Generating Recommendations',
            status: getStepStatus(
              completedSteps.includes('recommendations'),
              completedSteps.includes('searching')
            ),
            progress: completedSteps.includes('recommendations')
              ? 100
              : progress
          }
        ];

      case 'Career Matching':
        return [
          {
            icon: FileText,
            label: 'Analyzing CV and Skills',
            status: getStepStatus(completedSteps.includes('analyzing')),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Finding Career Matches',
            status: getStepStatus(
              completedSteps.includes('matching'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('matching') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Generating Recommendations',
            status: getStepStatus(
              completedSteps.includes('recommendations'),
              completedSteps.includes('matching')
            ),
            progress: completedSteps.includes('recommendations')
              ? 100
              : progress
          }
        ];

      case 'Cover Letter':
        return [
          {
            icon: FileText,
            label: 'Analyzing CV and Job Description',
            status: getStepStatus(completedSteps.includes('analyzing')),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Generating Content',
            status: getStepStatus(
              completedSteps.includes('generating'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('generating') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Finalizing Content',
            status: getStepStatus(
              completedSteps.includes('finalizing'),
              completedSteps.includes('generating')
            ),
            progress: completedSteps.includes('finalizing') ? 100 : progress
          }
        ];

      case 'AI CV Improvement':
        return [
          {
            icon: FileText,
            label: 'Analyzing CV',
            status: getStepStatus(completedSteps.includes('analyzing')),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Formatting Content',
            status: getStepStatus(
              completedSteps.includes('formatting'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('formatting') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Improving Content',
            status: getStepStatus(
              completedSteps.includes('improving'),
              completedSteps.includes('formatting')
            ),
            progress: completedSteps.includes('improving') ? 100 : progress
          }
        ];

      case 'Mock Interview':
        return [
          {
            icon: FileText,
            label: 'Analyzing Profile',
            status: getStepStatus(completedSteps.includes('analyzing')),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Generating Questions',
            status: getStepStatus(
              completedSteps.includes('generating'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('generating') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Preparing Feedback',
            status: getStepStatus(
              completedSteps.includes('preparing'),
              completedSteps.includes('generating')
            ),
            progress: completedSteps.includes('preparing') ? 100 : progress
          }
        ];

      case 'AI Career Coach':
        return [
          {
            icon: FileText,
            label: 'Processing Query',
            status: getStepStatus(completedSteps.includes('processing')),
            progress: completedSteps.includes('processing') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Analyzing Context',
            status: getStepStatus(
              completedSteps.includes('analyzing'),
              completedSteps.includes('processing')
            ),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Generating Response',
            status: getStepStatus(
              completedSteps.includes('responding'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('responding') ? 100 : progress
          }
        ];

      case 'Market Trends':
        return [
          {
            icon: FileText,
            label: 'Collecting Data',
            status: getStepStatus(completedSteps.includes('collecting')),
            progress: completedSteps.includes('collecting') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Analyzing Trends',
            status: getStepStatus(
              completedSteps.includes('analyzing'),
              completedSteps.includes('collecting')
            ),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Generating Insights',
            status: getStepStatus(
              completedSteps.includes('generating'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('generating') ? 100 : progress
          }
        ];

      case 'Salary Predictor':
        return [
          {
            icon: FileText,
            label: 'Collecting Market Data',
            status: getStepStatus(completedSteps.includes('collecting')),
            progress: completedSteps.includes('collecting') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Analyzing Factors',
            status: getStepStatus(
              completedSteps.includes('analyzing'),
              completedSteps.includes('collecting')
            ),
            progress: completedSteps.includes('analyzing') ? 100 : progress
          },
          {
            icon: TrendingUp,
            label: 'Calculating Prediction',
            status: getStepStatus(
              completedSteps.includes('calculating'),
              completedSteps.includes('analyzing')
            ),
            progress: completedSteps.includes('calculating') ? 100 : progress
          }
        ];

      case 'Recruitment Agencies':
        return [
          {
            icon: FileText,
            label: 'Searching Database',
            status: getStepStatus(completedSteps.includes('searching')),
            progress: completedSteps.includes('searching') ? 100 : progress
          },
          {
            icon: Brain,
            label: 'Filtering Results',
            status: getStepStatus(
              completedSteps.includes('filtering'),
              completedSteps.includes('searching')
            ),
            progress: completedSteps.includes('filtering') ? 100 : progress
          },
          {
            icon: CheckCircle,
            label: 'Preparing Recommendations',
            status: getStepStatus(
              completedSteps.includes('preparing'),
              completedSteps.includes('filtering')
            ),
            progress: completedSteps.includes('preparing') ? 100 : progress
          }
        ];

      case 'CV Improvement':
        return [
          {
            icon: FileText,
            label: 'Analyzing CV Structure',
            status: getStepStatus(completedSteps.includes('resume_analysis')),
            progress: completedSteps.includes('resume_analysis')
              ? 100
              : progress
          },
          {
            icon: Brain,
            label: 'Matching Job Requirements',
            status: getStepStatus(
              completedSteps.includes('job_comparison'),
              completedSteps.includes('resume_analysis')
            ),
            progress: completedSteps.includes('job_comparison') ? 100 : progress
          },
          {
            icon: Wand2,
            label: 'Optimizing Content',
            status: getStepStatus(
              completedSteps.includes('content_enhancement'),
              completedSteps.includes('job_comparison')
            ),
            progress: completedSteps.includes('content_enhancement')
              ? 100
              : progress
          },
          {
            icon: CheckCircle,
            label: 'Final Review',
            status: getStepStatus(
              completedSteps.includes('final_review'),
              completedSteps.includes('content_enhancement')
            ),
            progress: completedSteps.includes('final_review') ? 100 : progress
          }
        ];

      default:
        // If no feature is specified, use the plan-based steps
        return [
          ...baseSteps,
          ...(planType !== PlanType.BASIC ? proSteps : []),
          ...(planType === PlanType.ULTIMATE ? ultimateSteps : [])
        ];
    }
  };

  // Only use feature steps when feature is provided
  // Don't combine feature steps with plan-based steps
  const steps = feature
    ? getFeatureSteps()
    : [
        ...baseSteps,
        ...(planType !== PlanType.BASIC ? proSteps : []),
        ...(planType === PlanType.ULTIMATE ? ultimateSteps : [])
      ];

  if (useSingleSpinner) {
    return (
      <div className="flex flex-col items-center justify-center p-6">
        <Loader2 className="w-12 h-12 text-[#F6A03C] animate-spin mb-4" />
        <p className="text-center text-sm text-slate-300">
          {loadingMessage || 'Processing...'}
        </p>
      </div>
    );
  }

  return (
    <Card className="mt-6 backdrop-blur-sm bg-white/10 border-white/20 shadow-lg">
      <CardContent className="pt-6">
        {/* Standard step-by-step progress */}
        <div className="space-y-6">
          {steps.map((step, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center gap-4">
                <div className="relative">
                  {step.status === 'current' ? (
                    <Loader2 className="w-6 h-6 text-[#F6A03C] animate-spin" />
                  ) : step.status === 'complete' ? (
                    <CheckCircle className="w-6 h-6 text-[#F6A03C]" />
                  ) : (
                    <step.icon className="w-6 h-6 text-slate-400" />
                  )}
                </div>
                <div className="flex-1">
                  <p
                    className={`text-sm font-medium ${
                      step.status === 'current'
                        ? 'text-[#F6A03C]'
                        : step.status === 'complete'
                          ? 'text-[#F6A03C]'
                          : 'text-slate-400'
                    }`}
                  >
                    {step.label}
                  </p>
                  {/* Only show progress bar if useSpinner is false and step is current */}
                  {step.status === 'current' && !useSpinner && (
                    <div className="mt-2">
                      <Progress
                        value={step.progress}
                        className="h-2 bg-white/10"
                        indicatorClassName="bg-[#F6A03C] shadow-[0_0_8px_rgba(246,160,60,0.5)]"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
