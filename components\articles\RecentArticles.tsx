import { ThemedLink } from '../ui/Link';
import { articles } from '../../app/articles/articles-data';

export function RecentArticles({ qty = 3 }: { qty?: number }) {
  const sorted = [...articles].sort(
    (a, b) => +new Date(b.date) - +new Date(a.date)
  );
  return (
    <aside className="mt-16">
      <h3 className="text-lg font-semibold mb-4">Latest guides</h3>
      <ul className="space-y-2">
        {sorted.slice(0, qty).map((p) => (
          <li key={p.slug}>
            <ThemedLink href={`/articles/${p.slug}`} prefetch={false}>
              {p.title}
            </ThemedLink>
          </li>
        ))}
      </ul>
    </aside>
  );
}
