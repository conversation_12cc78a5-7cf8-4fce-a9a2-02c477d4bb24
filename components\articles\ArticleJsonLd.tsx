import { Article } from '@/app/types/globalTypes';
import JsonLd from '@/components/seo/JsonLd';

export default function ArticleJsonLd({ article }: { article: Article }) {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.metaDescription,
    author: {
      '@type': 'Person',
      name: article.author
    },
    datePublished: article.date,
    image: article.schema.image
  };

  return <JsonLd id="article-schema" data={jsonLd} />;
}
