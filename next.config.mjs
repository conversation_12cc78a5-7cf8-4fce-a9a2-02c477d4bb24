/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  compress: true,
  poweredByHeader: false,

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'ylsypkzjfqwwaheobapi.supabase.co',
        port: '',
        pathname: '/**'
      }
    ],
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 828, 1200],
    imageSizes: [32, 64, 96],
    minimumCacheTTL: 86400,
    unoptimized: false
  },

  output: 'standalone',

  experimental: {
    // ✅ CRITICAL: Optimize package imports more aggressively
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      'react-icons',
      '@supabase/supabase-js'
    ],

    // ✅ REMOVED: optimizeCss (requires critters package)
    // optimizeCss: true,

    serverActions: {
      bodySizeLimit: '2mb',
      allowedOrigins: ['jobspaceai.com', 'localhost:3000']
    }
  },

  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
    reactRemoveProperties: process.env.NODE_ENV === 'production'
  },

  // ✅ CRITICAL: Set modern browserslist to eliminate polyfills
  // This is the correct way to configure it in Next.js 15
  transpilePackages: [],

  webpack: (config, { dev, isServer }) => {
    config.output = {
      ...config.output,
      chunkLoadTimeout: 300000
    };

    if (!dev && !isServer) {
      // ✅ CRITICAL: Aggressive chunk optimization
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000, // Smaller minimum for better splitting
        maxSize: 200000, // Smaller max size for better caching
        maxInitialRequests: 6,
        maxAsyncRequests: 8,
        cacheGroups: {
          // ✅ Framework chunk
          framework: {
            test: /[\\/]node_modules[\\/](react|react-dom|next|scheduler)[\\/]/,
            priority: 50,
            enforce: true,
            reuseExistingChunk: true
          },

          // ✅ Large UI libraries
          radix: {
            test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
            priority: 40,
            enforce: true,
            reuseExistingChunk: true
          },

          // ✅ Supabase chunk
          supabase: {
            test: /[\\/]node_modules[\\/]@supabase[\\/]/,
            priority: 30,
            enforce: true,
            reuseExistingChunk: true
          },

          // ✅ Other vendor libraries
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            enforce: true,
            reuseExistingChunk: true,
            minChunks: 1
          },

          default: false,
          defaultVendors: false
        }
      };

      // ✅ Minimize chunks further
      config.optimization.minimize = true;
    }

    // SVG handling
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack']
    });

    return config;
  },

  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      {
        source: '/_next/image(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      }
    ];
  },

  async redirects() {
    return [
      {
        source: '/en-us/:path*',
        destination: '/:path*',
        permanent: true
      },
      {
        source: '/articles/cv-writing-article',
        destination: '/articles/effective-cv-writing-strategies-2025',
        permanent: true
      }
    ];
  }
};

export default nextConfig;
