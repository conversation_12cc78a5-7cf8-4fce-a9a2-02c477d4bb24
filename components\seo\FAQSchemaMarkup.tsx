import React from 'react';
import JsonLd from '@/components/seo/JsonLd';

interface FAQ {
  question: string;
  answer: string;
}

interface FAQSchemaMarkupProps {
  faqs: FAQ[];
  url: string;
}

const FAQSchemaMarkup: React.FC<FAQSchemaMarkupProps> = ({ faqs, url }) => {
  const faqSchema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntityOfPage: { '@type': 'WebPage', '@id': url },
    inLanguage: 'en-GB',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return <JsonLd id="faq-schema" data={faqSchema} />;
};

export default FAQSchemaMarkup;
