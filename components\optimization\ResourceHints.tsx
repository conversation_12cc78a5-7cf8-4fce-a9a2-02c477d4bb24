// components/optimization/ResourceHints.tsx
'use client';

import { useEffect } from 'react';

interface ResourceHintsProps {
  enableAnalytics?: boolean;
  enableClarity?: boolean;
}

export function ResourceHints({
  enableAnalytics,
  enableClarity
}: ResourceHintsProps) {
  useEffect(() => {
    // Only add resource hints after initial page load
    const addResourceHints = () => {
      const head = document.head;

      // Analytics resource hints - only if analytics enabled
      if (enableAnalytics) {
        // Preconnect to GTM/GA domains
        const gaPreconnect = document.createElement('link');
        gaPreconnect.rel = 'preconnect';
        gaPreconnect.href = 'https://www.google-analytics.com';
        gaPreconnect.crossOrigin = 'anonymous';
        head.appendChild(gaPreconnect);

        const gtmPreconnect = document.createElement('link');
        gtmPreconnect.rel = 'preconnect';
        gtmPreconnect.href = 'https://www.googletagmanager.com';
        head.appendChild(gtmPreconnect);

        // DNS prefetch for additional GA domains
        const domains = [
          'https://stats.g.doubleclick.net',
          'https://analytics.google.com'
        ];

        domains.forEach((domain) => {
          const link = document.createElement('link');
          link.rel = 'dns-prefetch';
          link.href = domain;
          head.appendChild(link);
        });
      }

      // Clarity resource hints
      if (enableClarity) {
        const clarityPreconnect = document.createElement('link');
        clarityPreconnect.rel = 'preconnect';
        clarityPreconnect.href = 'https://www.clarity.ms';
        clarityPreconnect.crossOrigin = 'anonymous';
        head.appendChild(clarityPreconnect);
      }
    };

    // Add hints after a delay to not block initial page load
    const timer = setTimeout(addResourceHints, 2000);

    return () => clearTimeout(timer);
  }, [enableAnalytics, enableClarity]);

  return null;
}

// Next.js specific optimizations
export function NextJSOptimizations() {
  useEffect(() => {
    // Optimize Next.js specific resources
    if (typeof window !== 'undefined') {
      // Preload critical Next.js chunks after initial load
      const preloadNextChunks = () => {
        // These are typical Next.js chunks that might be loaded later
        const criticalChunks = [
          '/_next/static/chunks/pages/_app.js',
          '/_next/static/chunks/main.js'
        ];

        criticalChunks.forEach((chunk) => {
          const link = document.createElement('link');
          link.rel = 'prefetch'; // Use prefetch for lower priority
          link.href = chunk;
          document.head.appendChild(link);
        });
      };

      // Preload after 3 seconds
      setTimeout(preloadNextChunks, 3000);

      // Optimize service worker registration if you have one
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js').catch(() => {
            // Service worker not found, ignore
          });
        });
      }
    }
  }, []);

  return null;
}
