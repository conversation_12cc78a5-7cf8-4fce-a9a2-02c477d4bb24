/* components/ScreenshotsGrid.tsx */
import Image from 'next/image';

export function ScreenshotsGrid() {
  const shots = [
    {
      src: '/images/ai-cv-builder/cv-improvement-2.webp',
      alt: 'Clean CV Builder Editor'
    },
    {
      src: '/images/ai-cover-letter-generator/ai-cover-letter-generator.webp',
      alt: 'Tailored Cover Letter Interface'
    },
    {
      src: '/images/ats-resume-checker/ats-analysis.webp',
      alt: 'Real-time ATS Feedback'
    },
    {
      src: '/images/ai-interview-coach/ai-career-coach.webp',
      alt: 'Interactive Interview Coach'
    }
  ];

  return (
    <div className="grid grid-cols-1 gap-6">
      {shots.map((shot) => (
        <div key={shot.src} className="flex flex-col items-center">
          {/* 1) parent has aspect-w-16 aspect-h-9 → forces a 16:9 box */}
          {/* 2) “relative overflow-hidden” allows the <Image fill> to absolutely position itself */}
          <div className="relative w-full aspect-w-16 aspect-h-9 overflow-hidden rounded-lg shadow-lg bg-gray-800">
            <Image
              src={shot.src}
              alt={shot.alt}
              fill
              className="object-cover"
              // No width/height props needed when using `fill`; the container defines the size
            />
          </div>
          <p className="text-slate-300 mt-2 text-sm sm:text-base text-center">
            {shot.alt}
          </p>
        </div>
      ))}
    </div>
  );
}
