// app/(marketing)/ai-cover-letter/page.tsx
export const dynamic = 'force-static';
export const revalidate = 86400; // 24 h

import type { Metadata } from 'next';
import JsonLd from '@/components/seo/JsonLd';

export const metadata: Metadata = {
  title: 'Free ATS CV Checker UK | Get Hired Faster 2025',
  description:
    'Generate a professional cover letter in seconds with our free AI cover letter generator. Tailored for UK job seekers, ATS-optimized, and used by thousands of successful applicants.',
  alternates: {
    canonical: '/ai-cover-letter'
  },
  keywords:
    'ai cover letter generator, free cover letter generator, cover letter builder uk, cover letter writer, ai cover letter writer, cover letter generator uk'
};

import { ThemedLink as Link } from 'components/ui/Link';
import Image from 'next/image';
import { faqLd, FAQItem } from 'lib/faq';
import { CallToAction } from 'components/CallToAction';
import { RecentArticles } from 'components/articles/RecentArticles';

const faqs: FAQItem[] = [
  {
    question: 'Is this cover letter generator really free?',
    answer:
      'Yes, our AI cover letter generator is completely free to use. You can create, customize, and download professional cover letters without any cost or hidden fees.'
  },
  {
    question: 'How does the free AI cover letter generator work?',
    answer:
      'Simply paste the job URL, answer three quick prompts about your experience and preferred tone, and our AI instantly generates a tailored cover letter that matches the job requirements and company culture.'
  },
  {
    question: 'Can I customize the cover letter for different UK jobs?',
    answer:
      'Absolutely. Our AI cover letter writer tailors each letter to match specific job descriptions and requirements. Perfect for applications through Reed, Indeed, NHS Jobs, or any UK employer.'
  },
  {
    question: 'Is the cover letter ATS-friendly for UK employers?',
    answer:
      'Yes, all cover letters are optimized to pass through Applicant Tracking Systems used by major UK employers including FTSE 100 companies, NHS trusts, and government departments.'
  },
  {
    question: 'What formats can I download my cover letter in?',
    answer:
      'You can download your cover letter as a PDF, Word document, or copy the text directly. All formats are optimized for UK job applications and ATS compatibility.'
  },
  {
    question: 'How does the AI know my writing voice?',
    answer:
      'Our AI analyzes your input and adapts to your preferred tone (formal, friendly, or bold). It creates authentic-sounding cover letters that match your personality while maintaining professionalism.'
  },
  {
    question: 'Can I edit the generated cover letter?',
    answer:
      'Yes, you have full control to edit and personalize the content. Our inline editor preserves ATS keywords while allowing you to make the letter perfectly match your needs.'
  },
  {
    question: 'Do UK employers still expect cover letters in 2025?',
    answer:
      "Yes, 68% of UK hiring managers still expect customized cover letters, and many say it's the deciding factor between similar candidates. A well-written cover letter significantly increases your interview chances."
  }
];

const sections = [
  {
    id: 'free-cover-letter-generator',
    h2: 'Free Cover Letter Generator for UK Jobs'
  },
  { id: 'how-it-works', h2: 'How our AI cover letter generator works' },
  { id: 'key-features', h2: 'Key features of our cover letter builder' },
  { id: 'benefits', h2: 'Why choose our AI cover letter writer' },
  { id: 'faqs', h2: 'Frequently asked questions' }
];

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from 'components/ui/accordion';

export default function AICoverLetter() {
  return (
    <div className="relative z-10 container px-4 pb-16  rounded-3xl bg-transparent backdrop-blur-sm text-center flex flex-col items-center">
      <div className="relative isolate min-h-screen overflow-hidden">
        <div className="relative z-10  px-4 py-12 rounded-3xl text-center">
          <CallToAction label="Write my cover letter" href="/signin/signup" />
          <h1 className="font-roboto-condensed mb-4 font-bold leading-[0.85] tracking-[-0.02em] text-white drop-shadow-2xl text-4xl md:text-8xl lg:text-8xl">
            Free AI{' '}
            <span className="text-yellow-400 text-shadow-yellow">
              Cover Letter
            </span>{' '}
            Generator UK
          </h1>
          <p className="text-slate-200 max-w-4xl mx-auto text-[14px] sm:text-[16px] md:text-[18px] mb-8 leading-relaxed">
            Create professional, tailored cover letters in seconds with our free
            AI cover letter generator. A recent UK survey found that{' '}
            <strong>68% of hiring managers</strong> still expect a customized
            cover letter—and many say it&apos;s the tie-breaker between similar
            candidates <sup>[2]</sup>. Our AI cover letter writer creates
            persuasive, job-specific letters in your own voice, so you ace that
            first impression without spending hours writing from scratch.
          </p>
          <div className="relative mx-auto mt-6 w-full h-[300px] md:h-screen max-h-screen">
            <Image
              src="/images/ai-cover-letter-generator/ai-cover-letter-generator.webp"
              alt="Free AI Cover Letter Generator UK - Create professional cover letters in seconds"
              fill
              sizes="(min-width: 1200px) 1200px, 100vw"
              className="object-contain rounded-xl shadow-md"
            />
          </div>

          {sections.map(({ id, h2 }) => {
            if (id === 'free-cover-letter-generator') {
              return (
                <section id={id} key={id} className="mt-12">
                  <h2 className="mb-4 text-4xl font-semibold">{h2}</h2>
                  <p className="text-slate-200 max-w-4xl mx-auto text-[14px] sm:text-[16px] md:text-[18px] mb-6 leading-relaxed">
                    Stop spending hours writing cover letters from scratch. Our
                    AI cover letter builder creates professional, personalized
                    letters for any UK job in seconds. Whether you&apos;re
                    applying through Reed, Indeed, NHS Jobs, or directly to
                    employers, our free cover letter generator ensures your
                    application stands out to recruiters and passes ATS
                    screening.
                  </p>
                  <div className="grid max-w-4xl mx-auto md:grid-cols-3 gap-4 text-left">
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-400 mb-2">
                        🎯 Tailored for UK Market
                      </h3>
                      <p className="text-sm text-slate-200">
                        Optimized for UK employers, job boards, and ATS systems
                        used across London, Manchester, Birmingham and beyond.
                      </p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-400 mb-2">
                        ⚡ Generate in Seconds
                      </h3>
                      <p className="text-sm text-slate-200">
                        Our AI cover letter writer creates professional letters
                        instantly - no more staring at blank documents.
                      </p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-400 mb-2">
                        💼 ATS-Optimized
                      </h3>
                      <p className="text-sm text-slate-200">
                        Passes through applicant tracking systems used by major
                        UK employers and recruitment agencies.
                      </p>
                    </div>
                  </div>
                </section>
              );
            }
            if (id === 'how-it-works') {
              return (
                <section id={id} key={id} className="mt-12">
                  <h2 className="mb-4 text-2xl font-semibold">{h2}</h2>
                  <ol className="space-y-4 list-decimal list-inside mx-auto text-left max-w-4xl">
                    <li className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <strong>Paste the job URL or description.</strong> Our AI
                      extracts the company mission, culture, and must-have
                      skills from any UK job posting.
                    </li>
                    <li className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <strong>Answer three quick prompts.</strong> Choose your
                      tone (formal, friendly, bold), add a key achievement, and
                      select UK English spelling preferences.
                    </li>
                    <li className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <strong>Generate and download instantly.</strong> Get your
                      cover letter as PDF, Word, or text. Edit lines inline if
                      needed—our edits preserve ATS keywords.
                    </li>
                  </ol>
                </section>
              );
            }
            if (id === 'key-features') {
              return (
                <section id={id} key={id} className="mt-12">
                  <h2 className="mb-4 text-2xl font-semibold">{h2}</h2>
                  <div className="grid md:grid-cols-2 gap-4 text-left max-w-4xl mx-auto">
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-400 mb-2">
                        Company-Specific Content
                      </h3>
                      <p className="text-sm text-slate-200">
                        Personalized opener and hook that references the
                        specific company and role
                      </p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-400 mb-2">
                        STAR-Based Achievements
                      </h3>
                      <p className="text-sm text-slate-200">
                        Professional achievement paragraphs using the STAR
                        method
                      </p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-400 mb-2">
                        Custom Call-to-Action
                      </h3>
                      <p className="text-sm text-slate-200">
                        Compelling closing that encourages the employer to take
                        action
                      </p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-400 mb-2">
                        Quality Assurance
                      </h3>
                      <p className="text-sm text-slate-200">
                        Built-in plagiarism checker and tone analyzer ensures
                        professional quality
                      </p>
                    </div>
                  </div>
                </section>
              );
            }
            if (id === 'benefits') {
              return (
                <section id={id} key={id} className="mt-12">
                  <h2 className="mb-4 text-2xl font-semibold">{h2}</h2>
                  <div className="space-y-4 max-w-4xl mx-auto">
                    <p className="text-slate-200 text-left">
                      <strong>Match voice to brand.</strong> Our AI cover letter
                      generator mirrors the company&apos;s language and culture
                      for instant resonance with UK employers. Save hours of
                      writing time while creating letters that sound
                      authentically like you.
                    </p>
                    <p className="text-slate-200 text-left">
                      <strong>Boost your application success.</strong> Cover
                      letters generated with our AI tool help candidates stand
                      out in competitive UK job markets. Pair with our{' '}
                      <Link
                        href="/ai-cv-builder"
                        prefetch={false}
                        className="text-yellow-400 underline"
                      >
                        AI CV Builder
                      </Link>{' '}
                      for a complete application package, then practice with our{' '}
                      <Link
                        href="/ai-interview-coach"
                        prefetch={false}
                        className="text-yellow-400 underline"
                      >
                        AI interview practice tool
                      </Link>
                      .
                    </p>
                  </div>
                  <RecentArticles />
                </section>
              );
            }
            if (id === 'faqs') {
              return (
                <section id={id} key={id} className="mt-12">
                  <h2 className="mb-4 max-w-4xl mx-auto text-2xl font-semibold">
                    {h2}
                  </h2>
                  <Accordion
                    type="single"
                    collapsible
                    className="mb-8 backdrop-blur-sm max-w-4xl mx-auto bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
                  >
                    {faqs.map(({ question, answer }, index) => (
                      <AccordionItem
                        key={index}
                        value={`item-${index}`}
                        className="border-white/10"
                      >
                        <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-sm sm:text-lg font-medium">
                          {question}
                        </AccordionTrigger>
                        <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
                          {answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                  <JsonLd
                    id="faq-schema"
                    data={faqLd('/ai-cover-letter', faqs)}
                  />
                  {/* HowTo schema for the "How it works" list */}
                  <JsonLd
                    id="howto-ai-cover-letter"
                    data={{
                      '@context': 'https://schema.org',
                      '@type': 'HowTo',
                      name: 'How to generate a professional cover letter with AI in 3 steps',
                      description:
                        'Learn how to create a tailored cover letter using our free AI cover letter generator for UK jobs.',
                      step: [
                        {
                          '@type': 'HowToStep',
                          position: 1,
                          name: 'Paste the job URL or description',
                          text: 'Our AI extracts company mission, culture and must-have skills from any UK job posting',
                          url: 'https://jobspaceai.com/ai-cover-letter#how-it-works'
                        },
                        {
                          '@type': 'HowToStep',
                          position: 2,
                          name: 'Answer three quick prompts',
                          text: 'Choose your tone, add achievements, and select UK English preferences',
                          url: 'https://jobspaceai.com/ai-cover-letter#how-it-works'
                        },
                        {
                          '@type': 'HowToStep',
                          position: 3,
                          name: 'Generate and download your cover letter',
                          text: 'Get your professional cover letter as PDF, Word, or text format instantly',
                          url: 'https://jobspaceai.com/ai-cover-letter#how-it-works'
                        }
                      ]
                    }}
                  />

                  {/* Enhanced SoftwareApplication schema */}
                  <JsonLd
                    id="app-ai-cover-letter"
                    data={{
                      '@context': 'https://schema.org',
                      '@type': 'SoftwareApplication',
                      name: 'JobSpaceAI Free AI Cover Letter Generator',
                      alternateName: 'AI Cover Letter Builder UK',
                      description:
                        'Free AI-powered cover letter generator for UK job seekers. Create professional, ATS-optimized cover letters in seconds.',
                      url: 'https://jobspaceai.com/ai-cover-letter',
                      applicationCategory: 'BusinessApplication',
                      operatingSystem: 'Web',
                      offers: {
                        '@type': 'Offer',
                        price: '0',
                        priceCurrency: 'GBP',
                        description:
                          'Free AI cover letter generator with no hidden costs'
                      },
                      featureList: [
                        'AI-powered cover letter generation',
                        'ATS optimization',
                        'UK job market focus',
                        'Multiple download formats',
                        'Company-specific customization'
                      ],
                      applicationSubCategory: 'Cover Letter Generator'
                    }}
                  />

                  {/* Organization schema for local SEO */}
                  <JsonLd
                    id="organization-schema"
                    data={{
                      '@context': 'https://schema.org',
                      '@type': 'Organization',
                      name: 'JobSpaceAI',
                      url: 'https://jobspaceai.com',
                      description:
                        'AI-powered job application tools for UK job seekers',
                      areaServed: {
                        '@type': 'Country',
                        name: 'United Kingdom'
                      },
                      serviceType: 'AI Cover Letter Generator'
                    }}
                  />
                </section>
              );
            }
            return null;
          })}

          {/* Call to action section */}
          <section className="mt-16 max-w-4xl mx-auto bg-gradient-to-r from-yellow-400/20 to-yellow-600/20 border border-yellow-400/30 rounded-xl p-8">
            <h2 className="text-2xl font-semibold mb-4 text-yellow-400">
              Ready to Create Your Professional Cover Letter?
            </h2>
            <p className="text-slate-200 mb-6 max-w-4xl mx-auto">
              Join thousands of UK job seekers who&apos;ve successfully landed
              interviews using our free AI cover letter generator. Create your
              tailored cover letter in under 60 seconds.
            </p>
            <CallToAction
              label="Generate My Free Cover Letter"
              href="/signin/signup"
            />
            <p className="text-xs text-slate-300 mt-2">
              No credit card required • Instant download • ATS-optimized
            </p>
          </section>
        </div>
      </div>
    </div>
  );
}
