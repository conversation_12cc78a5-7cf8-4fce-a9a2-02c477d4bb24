import JsonLd from '@/components/seo/JsonLd';

export default function OrganizationJsonLd() {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Job Space AI',
    url: 'https://jobspaceai.com',
    logo: 'https://jobspaceai.com/logo.png',

    parentOrganization: {
      '@type': 'Organization',
      name: 'NRB CONSULTING LTD',
      identifier: '16375535',
      foundingLocation: {
        '@type': 'Place',
        address: {
          '@type': 'PostalAddress',
          addressCountry: 'GB'
        }
      }
    },
    sameAs: [
      'https://twitter.com/jobspaceai',
      'https://linkedin.com/company/jobspaceai'
    ]
  };

  return <JsonLd id="organization-schema" data={jsonLd} />;
}
