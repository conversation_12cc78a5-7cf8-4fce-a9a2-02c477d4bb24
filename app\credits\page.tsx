// app/credits/page.tsx
import { createClient } from '../utils/supabase/server';
import { creditPackages } from '../../lib/credit-packages';
import CreditsClient from 'components/credits/CreditsClient';
import { Metadata } from 'next';
import { constructMetadata } from '@/lib/seo-config';

export const metadata: Metadata = constructMetadata({
  title: 'Purchase Credits | Job Space AI',
  description:
    'Purchase credits to unlock premium features and enhance your job search experience.',
  path: '/credits'
});

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default async function CreditsPage() {
  let user = null;
  let creditsData = null;
  let creditsError = null;

  try {
    const supabase = await createClient();
    const {
      data: { user: authUser },
      error: userError
    } = await supabase.auth.getUser();

    if (userError) {
      user = null;
    } else {
      user = authUser;

      if (user) {
        const { data, error } = await supabase
          .from('credits')
          .select('amount')
          .eq('user_id', user.id)
          .maybeSingle();

        if (!data && !error) {
          const { data: newData, error: insertError } = await supabase
            .from('credits')
            .insert([{ user_id: user.id, amount: 0 }])
            .select('amount')
            .single();

          if (insertError) {
            creditsError = insertError;
          } else {
            creditsData = newData;
          }
        } else {
          creditsData = data;
          creditsError = error;
        }
      }
    }
  } catch (error) {
    console.error('Error fetching user data:', error);
    user = null;
  }

  return (
    <div className="w-full min-h-screen">
      <div className="max-w-7xl mx-auto">
        <CreditsClient
          user={user ?? null}
          initialCredits={user ? (creditsData?.amount ?? 0) : null}
          creditPackages={creditPackages}
          initialError={creditsError?.message}
        />
      </div>
    </div>
  );
}
