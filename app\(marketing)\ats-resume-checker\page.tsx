export const dynamic = 'force-static';
export const revalidate = 86400; // 24h cache

import type { Metadata } from 'next';
import { faqLd, FAQItem } from 'lib/faq';
import ScanForm from './components/ScanForm';
import { CallToAction } from 'components/CallToAction';
import {
  Upload,
  FileText,
  Sparkles,
  BadgeCheck,
  Users,
  HelpCircle,
  MapPin,
  Building2,
  CheckCircle2
} from 'lucide-react';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent
} from 'components/ui/accordion';
import JsonLd from '@/components/seo/JsonLd';

const faqs: FAQItem[] = [
  {
    question: 'What is an ATS and how does it affect my CV in the UK?',
    answer:
      'ATS stands for Applicant Tracking System—the software recruiters use to filter CVs before a human ever sees them. In the UK, major employers like Reed, NHS Jobs, Amazon, and Barclays use ATS systems to scan for specific keywords and formatting before your CV reaches hiring managers.'
  },
  {
    question: 'Is my CV data stored when I use this ATS scanner?',
    answer:
      'No. Files are processed in-memory only, and only anonymised aggregate stats are kept. Your CV content is never stored or shared with third parties.'
  },
  {
    question: 'Do I have to pay to test my CV against UK ATS systems?',
    answer:
      'Your first three CV scans per day are completely free. You only pay (£5) if you choose the optional AI auto-fix upgrade for advanced optimisation suggestions.'
  },
  {
    question: 'How is the ATS compatibility score calculated for UK jobs?',
    answer:
      "We compare required keywords from the UK job description against your CV and check for ATS-unfriendly formatting like tables, images, or non-standard fonts that UK employers' systems might struggle to parse."
  },
  {
    question:
      'Does this CV checker work for London, Manchester, and Birmingham jobs?',
    answer:
      'Yes, our ATS CV checker is specifically designed for UK job seekers and tests compatibility with ATS systems used by major UK employers across London, Manchester, Birmingham, Leeds, Glasgow, and Edinburgh including Reed, Indeed UK, NHS Jobs, and Totaljobs.'
  },
  {
    question: 'Which UK employers and cities use ATS systems most?',
    answer:
      'ATS systems are widely used across London (Barclays, Amazon UK), Manchester (Co-op, AO.com), Birmingham (JLR, NHS), Leeds (Sky Betting), Glasgow (Weir Group), and Edinburgh (RBS, Scottish Government) by employers of all sizes, from SMEs to FTSE 100 companies.'
  },
  {
    question:
      'How can I improve my CV for UK ATS systems and applicant tracking?',
    answer:
      'Use our scanner to identify missing keywords from UK job descriptions, fix formatting issues, ensure proper UK date formats, and tailor your CV to UK job market standards including proper spelling conventions.'
  },
  {
    question:
      'Is this ATS CV checker suitable for all UK industries and regions?',
    answer:
      'Yes, it covers a wide range of industries including finance (London), tech (Manchester), manufacturing (Birmingham), healthcare (NHS nationwide), and works for applications across England, Scotland, Wales, and Northern Ireland.'
  }
];

// Updated metadata with enhanced UK and city-specific SEO
export const metadata: Metadata = {
  title: 'CV Checker UK | Free ATS Scanner for UK Jobs',
  description:
    'Test your CV online and see exactly how it performs against Applicant Tracking Systems used by top UK employers in London, Manchester, Birmingham, Leeds, Glasgow, and Edinburgh – 2025 ready. Scan your CV for roles on Reed, NHS Jobs, Amazon and Barclays.',
  keywords: [
    'CV checker UK',
    'ATS scan',
    'test CV for London jobs',
    'applicant tracking system UK',
    'resume scanner Manchester',
    'CV scanner Birmingham',
    'ATS resume checker Leeds',
    'Glasgow CV checker',
    'Edinburgh resume scanner',
    'UK ATS compliance',
    'Reed CV scanner',
    'NHS Jobs resume checker',
    'Amazon UK CV test',
    'Barclays ATS checker',
    'free CV scan UK',
    'UK resume scanner',
    'CV checker 2025',
    'UK job application scanner'
  ].join(', '),
  openGraph: {
    title: 'CV Checker UK | Free ATS Scanner for UK Jobs',
    description:
      'Test your CV against UK employer ATS systems used by Reed, NHS, Amazon & Barclays. Get instant feedback for London, Manchester, Birmingham jobs.',
    type: 'website',
    locale: 'en_GB',
    siteName: 'JobSpace AI'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CV Checker UK | Free ATS Scanner for UK Jobs',
    description:
      'Test your CV against UK employer ATS systems. Free scan for London, Manchester, Birmingham jobs.'
  },
  alternates: {
    canonical: 'https://jobspaceai.com/ats-resume-checker'
  }
};

// UK Cities data for local SEO sections
const ukCities = [
  {
    name: 'London',
    icon: Building2,
    stats: '2.3M+ jobs',
    description:
      'Test your CV for London roles at major employers including Barclays, Amazon UK, NHS London, and financial services firms in the City.',
    majorEmployers: ['Barclays', 'Amazon UK', 'NHS London', 'HSBC', 'Deloitte'],
    jobTypes: 'Finance, Tech, Healthcare, Government'
  },
  {
    name: 'Manchester',
    icon: Users,
    stats: '850K+ jobs',
    description:
      'Scan your CV for Manchester opportunities with major employers like Co-op, AO.com, NHS Greater Manchester, and MediaCity companies.',
    majorEmployers: [
      'Co-op Group',
      'AO.com',
      'NHS Greater Manchester',
      'Bruntwood',
      'Auto Trader'
    ],
    jobTypes: 'Tech, Media, Retail, Healthcare'
  },
  {
    name: 'Birmingham',
    icon: MapPin,
    stats: '1.1M+ jobs',
    description:
      'Optimize your CV for Birmingham roles with employers like JLR, NHS Birmingham, and emerging tech companies in the West Midlands.',
    majorEmployers: [
      'Jaguar Land Rover',
      'NHS Birmingham',
      'Mondelez',
      'BT',
      'KPMG'
    ],
    jobTypes: 'Manufacturing, Healthcare, Professional Services'
  },
  {
    name: 'Leeds',
    icon: Building2,
    stats: '780K+ jobs',
    description:
      'Check your CV for Leeds opportunities including NHS Leeds, Sky Betting, and the growing fintech sector in Yorkshire.',
    majorEmployers: [
      'NHS Leeds',
      'Sky Betting',
      'Asda',
      'DLA Piper',
      'Yorkshire Bank'
    ],
    jobTypes: 'Finance, Legal, Retail, Healthcare'
  },
  {
    name: 'Glasgow',
    icon: Users,
    stats: '650K+ jobs',
    description:
      'Test your CV for Glasgow roles with major Scottish employers including NHS Scotland, Weir Group, and financial services.',
    majorEmployers: [
      'NHS Scotland',
      'Weir Group',
      'Scottish Power',
      'Clydesdale Bank',
      'Arnold Clark'
    ],
    jobTypes: 'Energy, Engineering, Finance, Public Sector'
  },
  {
    name: 'Edinburgh',
    icon: Building2,
    stats: '520K+ jobs',
    description:
      'Scan your CV for Edinburgh opportunities with employers like RBS, Scottish Government, and the thriving fintech sector.',
    majorEmployers: [
      'NatWest (RBS)',
      'Scottish Government',
      'Standard Life Aberdeen',
      'Baillie Gifford',
      'Skyscanner'
    ],
    jobTypes: 'Finance, Government, Tech, Investment'
  }
];

// Major UK employers that use ATS
const majorUKEmployers = [
  { name: 'Reed', sector: 'Recruitment', atsType: 'Taleo' },
  { name: 'NHS Jobs', sector: 'Healthcare', atsType: 'Trac' },
  { name: 'Amazon UK', sector: 'E-commerce/Tech', atsType: 'Custom ATS' },
  { name: 'Barclays', sector: 'Banking', atsType: 'SuccessFactors' },
  { name: 'Tesco', sector: 'Retail', atsType: 'Workday' },
  {
    name: 'BT Group',
    sector: 'Telecommunications',
    atsType: 'SmartRecruiters'
  },
  { name: 'Rolls-Royce', sector: 'Aerospace', atsType: 'Oracle HCM' },
  { name: 'Unilever UK', sector: 'FMCG', atsType: 'Workday' }
];

export default function AtsCvCheckerPage() {
  return (
    <>
      {/* Enhanced JSON-LD Schema for better SEO */}
      <JsonLd
        id="main-schema"
        data={{
          '@context': 'https://schema.org',
          '@type': 'WebApplication',
          name: 'ATS CV Checker UK - JobSpace AI',
          description:
            'Free online CV scanner that tests resumes against UK employer Applicant Tracking Systems including Reed, NHS Jobs, Amazon and Barclays',
          url: 'https://jobspaceai.com/ats-resume-checker',
          applicationCategory: 'BusinessApplication',
          operatingSystem: 'Any',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'GBP',
            description: 'Free ATS CV scanning with 3 daily scans included'
          },
          areaServed: [
            { '@type': 'City', name: 'London', addressCountry: 'GB' },
            { '@type': 'City', name: 'Manchester', addressCountry: 'GB' },
            { '@type': 'City', name: 'Birmingham', addressCountry: 'GB' },
            { '@type': 'City', name: 'Leeds', addressCountry: 'GB' },
            { '@type': 'City', name: 'Glasgow', addressCountry: 'GB' },
            { '@type': 'City', name: 'Edinburgh', addressCountry: 'GB' }
          ],
          creator: {
            '@type': 'Organization',
            name: 'JobSpace AI',
            url: 'https://jobspaceai.com'
          },
          featureList: [
            'Free ATS CV scanning',
            'UK employer compatibility testing',
            'Keyword optimization suggestions',
            'Formatting issue detection',
            'Real-time CV scoring'
          ]
        }}
      />

      {/* FAQ JSON-LD for FAQPage */}
      <JsonLd id="faq-schema" data={faqLd('/ats-resume-checker', faqs)} />

      {/* HowTo JSON-LD for the "How it works" section */}
      <JsonLd
        id="howto-schema"
        data={{
          '@context': 'https://schema.org',
          '@type': 'HowTo',
          name: 'How to Use the Free ATS CV Checker for UK Job Applications',
          description:
            'Follow these simple steps to scan and optimise your CV for Applicant Tracking Systems used by UK employers like Reed, NHS Jobs, Amazon and Barclays.',
          step: [
            {
              '@type': 'HowToStep',
              position: 1,
              name: 'Upload or paste your CV',
              text: 'Upload your CV file (PDF/Word) or paste the text directly into our UK-optimized ATS scanner',
              item: 'https://jobspaceai.com/ats-resume-checker#how-it-works'
            },
            {
              '@type': 'HowToStep',
              position: 2,
              name: 'Add UK job description',
              text: 'Paste the job description from Reed, NHS Jobs, or any UK employer to test CV compatibility',
              item: 'https://jobspaceai.com/ats-resume-checker#how-it-works'
            },
            {
              '@type': 'HowToStep',
              position: 3,
              name: 'Get instant ATS score and UK-specific optimisation tips',
              text: 'Receive your ATS compatibility score and personalized suggestions for UK job applications',
              item: 'https://jobspaceai.com/ats-resume-checker#how-it-works'
            }
          ]
        }}
      />

      <div className="relative z-10 container px-4 pb-16 max-w-[1200px] rounded-3xl bg-transparent backdrop-blur-sm text-center flex flex-col items-center overflow-visible h-auto">
        <main className="relative isolate w-full text-center">
          {/* Enhanced Hero Section with city-specific targeting */}
          <section className="max-w-5xl mx-auto py-16">
            <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] text-white text-[clamp(2rem,6vw,3.5rem)] sm:text-[clamp(2.5rem,8vw,4.5rem)]">
              Free{' '}
              <span className="text-yellow-400 text-shadow-yellow">
                ATS CV Checker UK
              </span>{' '}
              for 2025 Jobs
            </h1>
            <p className="mt-4 text-slate-200 max-w-4xl mx-auto text-[16px] mb-6 leading-relaxed">
              Test your CV online and see exactly how it performs against
              Applicant Tracking Systems used by top UK employers in{' '}
              <strong className="text-yellow-400">
                London, Manchester, Birmingham, Leeds, Glasgow, and Edinburgh
              </strong>{' '}
              – 2025 ready. Scan your CV for roles on{' '}
              <strong className="text-white">
                Reed, NHS Jobs, Amazon and Barclays
              </strong>{' '}
              — no sign-up needed.
            </p>
            <CallToAction
              label="Test my CV for UK jobs now"
              href="#scan-form"
            />
          </section>

          {/* Enhanced intro copy with more UK keywords */}
          <section
            id="intro"
            className="max-w-4xl mx-auto text-slate-200 mb-12 text-base leading-relaxed"
          >
            <p>
              Our free <strong>ATS CV checker and resume scanner</strong> is
              specifically designed for <strong>UK job seekers</strong>,
              ensuring your CV passes the stringent filters of{' '}
              <strong>Applicant Tracking Systems</strong> used by employers
              across{' '}
              <strong>
                London, Manchester, Birmingham, Leeds, Glasgow, and Edinburgh
              </strong>
              . Test your CV against job adverts to discover missing keywords,
              formatting issues, and get expert AI-driven suggestions that work
              for <strong>2025 UK job applications</strong>.
            </p>
            <p className="mt-4">
              Whether you&apos;re applying to leading UK firms on{' '}
              <strong>Reed, NHS Jobs, Indeed UK, or Totaljobs</strong>,
              targeting{' '}
              <strong>
                FTSE 100 companies like Amazon UK, Barclays, or Tesco
              </strong>
              , or focusing on local SMEs, this <strong>CV scanner</strong>{' '}
              helps you beat the ATS black hole and secure the interview you
              deserve. <strong>No sign-up required</strong> for your first three
              daily scans.
            </p>
          </section>

          {/* Interactive form */}
          <section id="scan-form" className="w-full max-w-6xl mx-auto">
            <ScanForm />
          </section>

          {/* How it works */}
          <section id="how-it-works" className="mt-16 w-full">
            <h2 className="text-2xl font-semibold mb-4 text-white">
              How our UK ATS CV scanner works
            </h2>
            <ol className="list-decimal list-inside mx-auto text-center space-y-6 max-w-2xl">
              <li className="flex flex-col items-center">
                <Upload
                  className="h-8 w-8 text-yellow-400 mb-2"
                  aria-hidden="true"
                />
                <span className="font-medium">Upload or Paste Your CV</span>
                <p className="text-sm text-slate-200 mt-1">
                  Upload your CV file (PDF/Word) or paste the text directly into
                  our UK-optimized ATS scanner
                </p>
              </li>
              <li className="flex flex-col items-center">
                <FileText
                  className="h-8 w-8 text-yellow-400 mb-2"
                  aria-hidden="true"
                />
                <span className="font-medium">Add UK Job Description</span>
                <p className="text-sm text-slate-200 mt-1">
                  Paste the job description from Reed, NHS Jobs, or any UK
                  employer to test CV compatibility
                </p>
              </li>
              <li className="flex flex-col items-center">
                <Sparkles
                  className="h-8 w-8 text-yellow-400 mb-2"
                  aria-hidden="true"
                />
                <span className="font-medium">
                  Get Instant ATS Test Results
                </span>
                <p className="text-sm text-slate-200 mt-1">
                  See your ATS compatibility score and UK-specific optimization
                  recommendations
                </p>
              </li>
            </ol>
          </section>

          {/* New: Major UK Employers ATS Section */}
          <section id="uk-employers" className="mt-16 w-full">
            <h2 className="text-2xl font-semibold mb-6 text-white">
              Test your CV against major UK employer ATS systems
            </h2>
            <p className="text-slate-200 mb-6 max-w-3xl mx-auto">
              Our scanner tests compatibility with the same ATS systems used by
              leading UK employers across all sectors:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-5xl mx-auto">
              {majorUKEmployers.map((employer, index) => (
                <div
                  key={index}
                  className="p-4 bg-white/5 rounded-lg border border-white/10"
                >
                  <div className="flex items-center mb-2">
                    <CheckCircle2 className="h-4 w-4 text-green-400 mr-2" />
                    <span className="font-medium text-white">
                      {employer.name}
                    </span>
                  </div>
                  <p className="text-xs text-slate-300 mb-1">
                    {employer.sector}
                  </p>
                  <p className="text-xs text-yellow-400">
                    ATS: {employer.atsType}
                  </p>
                </div>
              ))}
            </div>
          </section>

          {/* Enhanced Local Landing Sections for UK Cities */}
          <section id="uk-cities" className="mt-16 w-full">
            <h2 className="text-2xl font-semibold mb-6 text-white">
              ATS CV checker for major UK employment hubs
            </h2>
            <p className="text-slate-200 mb-8 max-w-3xl mx-auto">
              Our CV scanner is trusted by job seekers across the UK&apos;s
              largest employment centres, helping candidates secure interviews
              with local and national employers.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {ukCities.map((city, index) => {
                const IconComponent = city.icon;
                return (
                  <div
                    key={index}
                    className="p-6 bg-white/5 rounded-xl border border-white/10 hover:border-yellow-400/30 transition-colors"
                  >
                    <div className="flex items-center mb-3">
                      <IconComponent className="h-6 w-6 text-yellow-400 mr-3" />
                      <div>
                        <h3 className="font-semibold text-white">
                          {city.name} CV Scanner
                        </h3>
                        <p className="text-sm text-yellow-400">{city.stats}</p>
                      </div>
                    </div>
                    <p className="text-sm text-slate-200 mb-3 leading-relaxed">
                      {city.description}
                    </p>
                    <div className="mb-3">
                      <p className="text-xs text-slate-300 mb-1">
                        Key Sectors:
                      </p>
                      <p className="text-xs text-yellow-400">{city.jobTypes}</p>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {city.majorEmployers
                        .slice(0, 3)
                        .map((employer, empIndex) => (
                          <span
                            key={empIndex}
                            className="text-xs bg-white/10 px-2 py-1 rounded text-slate-200"
                          >
                            {employer}
                          </span>
                        ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </section>

          {/* Enhanced UK-specific benefits */}
          <section id="benefits" className="mt-16 w-full">
            <h2 className="text-2xl font-semibold mb-6 text-white">
              Why UK job seekers choose our ATS CV checker
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="p-6 flex flex-col items-start bg-white/5 rounded-lg border border-white/10">
                <BadgeCheck className="h-6 w-6 text-yellow-400 mb-3" />
                <span className="font-medium text-white mb-2">
                  Beat UK ATS systems used by Reed, NHS & FTSE 100
                </span>
                <p className="text-sm text-slate-200 leading-relaxed">
                  Test your CV against the same ATS systems used by major UK
                  employers including Reed, NHS Jobs, Amazon UK, and Barclays.
                  Our scanner identifies missing keywords so your CV gets
                  noticed by recruiters across London, Manchester, Birmingham
                  and beyond.
                </p>
              </div>
              <div className="p-6 flex flex-col items-start bg-white/5 rounded-lg border border-white/10">
                <Users className="h-6 w-6 text-yellow-400 mb-3" />
                <span className="font-medium text-white mb-2">
                  Optimized for UK job market standards & 2025 requirements
                </span>
                <p className="text-sm text-slate-200 leading-relaxed">
                  UK employers expect specific date formats (DD/MM/YYYY),
                  section headings, and British spelling conventions. Our CV
                  checker ensures your CV meets local standards for applications
                  in Leeds, Glasgow, Edinburgh and across the UK.
                </p>
              </div>
              <div className="p-6 flex flex-col items-start bg-white/5 rounded-lg border border-white/10">
                <HelpCircle className="h-6 w-6 text-yellow-400 mb-3" />
                <span className="font-medium text-white mb-2">
                  Free CV testing - 3 scans daily, no payment required
                </span>
                <p className="text-sm text-slate-200 leading-relaxed">
                  Test your CV risk-free with our scanner. No payment details or
                  sign-up required for your first three daily CV compatibility
                  checks. Perfect for testing multiple job applications across
                  different UK cities.
                </p>
              </div>
              <div className="p-6 flex flex-col items-start bg-white/5 rounded-lg border border-white/10">
                <Sparkles className="h-6 w-6 text-yellow-400 mb-3" />
                <span className="font-medium text-white mb-2">
                  Instant CV scan results with AI recommendations
                </span>
                <p className="text-sm text-slate-200 leading-relaxed">
                  Get personalized AI-powered suggestions to optimize your
                  CV&apos;s keyword matching, formatting, and content in seconds
                  after each scan. Tailored specifically for UK job market
                  requirements.
                </p>
              </div>
            </div>
          </section>

          {/* Enhanced FAQs with UK focus */}
          <section id="faqs" className="mt-16">
            <CallToAction
              label="Generate your AI‑optimised CV for UK jobs now"
              href="/signin/signup"
            />
            <h2 className="mb-6 text-2xl font-semibold">
              Frequently asked questions about ATS CV checking in the UK
            </h2>
            <Accordion
              type="single"
              collapsible
              className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
            >
              {faqs.map(({ question, answer }, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border-white/10"
                >
                  <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-sm sm:text-lg font-medium">
                    {question}
                  </AccordionTrigger>
                  <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
                    {answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </section>
        </main>
      </div>
    </>
  );
}
