'use client';

import dynamic from 'next/dynamic';

// Use dynamic import with ssr: false in this client component
export const PDFViewer = dynamic(() => import('../resume/PDFViewer'), {
  ssr: false,
  loading: () => (
    <div className="w-full min-h-[500px] bg-white shadow-md flex items-center justify-center">
      <div className="animate-pulse text-gray-400">Loading PDF...</div>
    </div>
  )
});

export const DocxViewer = dynamic(() => import('../resume/DocxViewer'), {
  ssr: false,
  loading: () => (
    <div className="w-full min-h-[500px] bg-white shadow-md flex items-center justify-center">
      <div className="animate-pulse text-gray-400">Loading DOCX...</div>
    </div>
  )
});
