import { Product } from '@/components/application-funnel/ProductSelection';
import { User } from '@supabase/supabase-js';

export interface Resume {
  id: string; // UUID
  user_id: string; // UUID
  resume: string;
  file_name: string | null;
  file_type: string | null;
  created_at: string | null;
  updated_at: string | null;
  thumbnail_url: string | null;
  analysis_result: ResumeAnalysisResult;
  content?: string;
  status?: 'active' | 'archived';
  last_used?: string;
  version?: number;
  title?: string;
}

export interface LocalResume extends Resume {
  isLocal: boolean;
}

export type FeatureResult = {
  ats: ComprehensiveAnalysisResult;
  skillGap: SkillsGapResult;
  skillsGap: SkillsGapResult;
  careerMatching: CareerMatchingResponse;
  mockInterview: InterviewFeedbackResult;
  interview: InterviewFeedbackResult;
  learningResources: SkillImprovementResponse;
  marketTrends: MarketTrendsResult;
  resumeImprovement: ResumeEnhancementResponse;
  coverLetter: DashboardCoverLetterResult;
  careerCoach: CareerCoachingResponse;
  recruitmentAgencies: Record<string, unknown>;
};

export interface ResumeSection {
  title: string;
  content: string[];
}

export interface ResumeAnalysisSection {
  title: string;
  content: string[] | string | Record<string, string>[];
}

export interface ResumeAnalysisResult {
  sections: ResumeSection[] | ResumeAnalysisSection[];
}

export interface Job {
  id: string | number; // Can be either string (UUID) or number (DB auto-increment)
  user_id: string; // This remains string as it's a UUID
  title: string | null;
  company: string | null;
  description: string;
  job_link: string;
  salary_range: string | null;
  location: string | null;
  analysis_result: JobAnalysisResult;
  status: string;
  created_at: string;
  updated_at: string;
  last_interaction?: string;
  notes?: string;
}

export interface JobSection {
  title: string;
  content: string[];
}

export interface JobAnalysisResult {
  job_title: string | null;
  company_name: string | null;
  location: string | null;
  job_type: string | null;
  salary_range: string | null;
  sections: JobSection[];
  job_link: string;
}

export interface FormattingConsistency {
  heading_style: number;
  spacing_consistency: number;
  bullet_points: number;
  indentation: number;
  line_lengths: number;
  whitespace_usage: number;
  overall_consistency: number;
}

export interface SectionAnalysis {
  score: number;
  feedback: string[];
  suggestions?: string[];
  keywords?: string[];
  improvement_areas?: string[];
}

export interface KeywordAnalysis {
  essential: string[];
  preferred: string[];
}

export interface SkillAnalysis {
  score: number;
  relevance: number;
  experience_level?: string;
  confidence: number;
  feedback: string[];
  matches: string[];
  missing: string[];
}

export interface FormattingMetric {
  score: number;
  feedback: string[];
  suggestions?: string[];
  details?: {
    spacing: boolean;
    alignment: boolean;
    consistency: boolean;
    margins: boolean;
  };
}

export interface FormatAnalysis {
  formatting_consistency: FormattingConsistency;
  formatting_metrics: {
    score: number;
    feedback: string[];
    suggestions: string[];
  };
  structure: {
    score: number;
    feedback: string[];
  };
  readability: {
    score: number;
    feedback: string[];
  };
  styling: {
    score: number;
    feedback: string[];
  };
  overall_score: number;
}

export interface ATSAnalysisResponse {
  overall_match: number;
  overall_match_percentage: number;
  section_analysis: Record<string, SectionAnalysis>;
  keyword_analysis: Record<string, KeywordMatch[]>;
  skills_analysis: Record<string, SkillMatch>;
  format_analysis: Record<string, FormattingMetric>;
  keyword_match_percentage: number;
  skill_match_percentage: number;
  keyword_matches: KeywordMatch[];
  skill_matches: SkillMatch[];
  missing_keywords: string[];
  missing_skills: string[];
  readability_score: number;
  recommendations: string[];
}
export interface KeywordMatch {
  keyword: string;
  count: number;
  context: string;
  relevance_score: number;
}

export interface SkillMatch {
  skill: string;
  in_resume: boolean;
  in_job_description: boolean;
  experience_level: ExperienceLevel;
  last_used: string;
  importance?: string;
}

export interface APIResumeData {
  id: string;
  user_id: string;
  file_name: string;
  file_type: string;
  resume: string;
  created_at: string;
  updated_at: string;
  analysis_result: string;
}

export interface InterviewQuestionsResult {
  estimated_level: number;
  questions: string[];
  difficulty_levels: number[];
}

export interface InterviewQuestion {
  question: string;
  difficulty: number;
}

export interface QuestionAnswer {
  question: string;
  answer: string;
  difficulty: number;
}

export interface AnswerFeedback {
  question: string;
  answer: string;
  feedback: string;
  score: number;
}

export interface InterviewFeedbackResult {
  questions: {
    question: string;
    difficulty: number;
    category?: string;
    answer?: string;
    feedback?: string;
    score?: number;
  }[];
  estimatedDuration: number;
  skillsAssessed: string[];
  difficulty: {
    overall: number;
    byCategory?: Record<string, number>;
  };
  overall_score?: number;
  overall_feedback?: string;
  recommendations?: string[];
  metadata?: {
    generatedAt: string;
    version: string;
  };
}

export interface InterviewFeedback {
  score: number;
  summary: string;
  strengths: string[];
  weaknesses: string[];
}

export interface LearningTopic {
  name: string;
  description: string;
}

export interface SkillGap {
  skill: string;
  learning_topics: LearningTopic[];
}

export interface SkillsGapResponse {
  skills_gaps: SkillGap[];
  match_percentage: number;
}

export interface SkillsGapResult {
  skills_gaps: SkillGap[];
  match_percentage?: number;
  raw_response?: {
    skills_analysis?: {
      identified_skills?: string[];
      missing_skills?: string[];
      skill_levels?: Record<string, string>;
    };
    recommendations?: string[];
    metadata?: {
      analysis_timestamp?: string;
      version?: string;
    };
  };
}

export interface SkillsGapError {
  error: string;
  status: number;
}

export interface FeatureProps {
  title: string;
  description: string;
  icon: string;
  disabled?: boolean;
  hasStoredResults?: boolean;
  requiresJob?: boolean;
  requiresResume?: boolean;
}

export interface MediaContent {
  storageKey: string;
  duration: string;
  mimeType: string;
}

export interface Article {
  title: string;
  slug: string;
  metaDescription: string;
  content: string;
  author: string;
  date: string;
  readingTime: string;
  tags: string[];
  previewImage?: string;
  audio?: MediaContent;
  video?: MediaContent;
  schema: {
    '@context': string;
    '@type': string;
    headline: string;
    description: string;
    image: string;
    author: {
      '@type': string;
      name: string;
    };
    publisher: {
      '@type': string;
      name: string;
      logo: {
        '@type': string;
        url: string;
      };
    };
    datePublished: string;
    dateModified: string;
  };
}

export interface RecruitmentAgency {
  name: string;
  number: string;
  address: string | null;
  website?: string;
  sic_codes?: string[];
}

export interface GooglePlacesRecruitmentAgency {
  name: string;
  address: string;
  phone?: string;
  website?: string;
  rating?: number;
  opening_hours?: string[];
  reviews?: GooglePlacesReview[];
}

export interface GooglePlacesReview {
  rating: number;
  text: string;
}

export interface TailoredResume {
  id?: string;
  originalResumeId: string;
  jobId: string;
  sections: ResumeSection[];
  createdAt?: string;
  updatedAt?: string;
  score?: number;
}

export interface RawAgencyData {
  name?: string;
  number?: string;
  address?: string | null;
  website?: string;
  sic_codes?: string[];
}

export interface RawGooglePlacesData {
  name?: string;
  address?: string;
  phone?: string;
  website?: string;
  rating?: number;
  opening_hours?: string[];
  reviews?: GooglePlacesReview[];
}

// Career matching feature
export interface AlternativeCareer {
  title: string;
  match_percentage: number;
  skills_match: string[];
  additional_skills_needed: string[];
  description: string;
}

export interface CareerMatchingResponse {
  alternative_careers: AlternativeCareer[];
}

export interface SalaryTrend {
  month: string;
  tech: number;
  finance: number;
  healthcare: number;
}

export interface JobGrowth {
  quarter: string;
  growth: number;
}

export interface SkillDemand {
  skill: string;
  count: number;
}

export interface IndustryBreakdown {
  name: string;
  value: number;
}

export interface RegionalComparison {
  region: string;
  averageSalary: number;
  jobCount: number;
  growthRate: number;
  topSkills: string[];
}

export interface ExperienceLevel {
  level: string;
  percentage: number;
  averageSalary: number;
  mostDemandedSkills: string[];
}

export interface ContractType {
  type: string;
  percentage: number;
  averageSalary: number;
  trend: number;
}

export interface RemoteWorkTrend {
  type: string;
  percentage: number;
  averageSalary: number;
  industryBreakdown: { industry: string; percentage: number }[];
}

export interface TopEmployer {
  name: string;
  jobCount: number;
  averageSalary: number;
  mainLocations: string[];
  topRoles: string[];
}

export interface SkillCluster {
  name: string;
  skills: string[];
  demandScore: number;
  salaryRange: { min: number; max: number };
  relatedRoles: string[];
}

export interface CompensationBenefit {
  type: string;
  percentage: number;
  byIndustry: { industry: string; percentage: number }[];
}

export interface EducationRequirement {
  level: string;
  percentage: number;
  averageSalary: number;
  topRoles: string[];
}

export interface MarketData {
  salaryTrends: SalaryTrend[];
  jobGrowth: JobGrowth[];
  skillDemand: SkillDemand[];
  industryBreakdown: IndustryBreakdown[];
  remoteWorkTrends: RemoteWorkTrend[];
  regionalComparison: RegionalComparison[];
  experienceLevels: ExperienceLevel[];
  contractTypes: ContractType[];
  topEmployers: TopEmployer[];
  skillsClusters: SkillCluster[];
  compensationBenefits: CompensationBenefit[];
  educationRequirements: EducationRequirement[];
  lastUpdated: string;
}

export interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  savePercent?: number;
}

export type ServiceType =
  | 'ATS_ANALYSIS'
  | 'MOCK_INTERVIEW'
  | 'SKILL_GAP_ANALYSIS'
  | 'RECRUITMENT_AGENCIES'
  | 'RESUME_IMPROVEMENT'
  | 'CAREER_MATCHING'
  | 'COVER_LETTER'
  | 'CAREER_COACHING'
  | 'SUCCESS_PREDICTION'
  | 'LEARNING_RESOURCES'
  | 'SALARY_PREDICTOR'
  | 'MARKET_TRENDS'
  | 'GAMIFIED_SEARCH';

export type CreditCosts = Record<ServiceType, number> & {
  [key: string]: number;
};

export interface AdzunaResponse {
  results: JobListing[];
  count: number;
  __CLASS__: string;
}

export interface JobListing {
  id: string;
  created: string;
  title: string;
  description: string;
  salary_min: number;
  salary_max: number;
  company: Company;
  location: Location;
  category: Category;
  contract_type?: string;
  salary_is_predicted: string;
  adref: string;
  redirect_url: string;
  __CLASS__: string;
  latitude?: number;
  longitude?: number;
}

export interface Company {
  display_name: string;
  __CLASS__: string;
}

export interface Location {
  area: string[];
  display_name: string;
  __CLASS__: string;
}

export interface Category {
  label: string;
  tag: string;
  __CLASS__: string;
}

export interface HistoricalResponse {
  month: string;
  count: number;
}

export interface LocationArea {
  country: string;
  region: string;
  county: string;
  city: string;
}

export interface AdzunaSearchParams {
  app_id: string;
  app_key: string;
  results_per_page: number;
  what?: string;
  where?: string;
  category?: string;
  content_type?: string;
  salary_min?: number;
  salary_max?: number;
  full_time?: boolean;
  part_time?: boolean;
  contract?: boolean;
  permanent?: boolean;
}

export interface HistoricalTrend {
  current: number;
  previous: number;
  trend: number;
}

export interface MarketTrendsSkillAnalysis {
  name: string;
  count: number;
  salaries: number[];
  titles: Set<string>;
  companies: Set<string>;
  locations: Set<string>;
  requiredYears: number[];
  educationLevels: Set<string>;
}

export interface EnhancedMarketData {
  skillsAnalysis: MarketTrendsSkillAnalysis[];
  educationLevels: string[];
  experienceLevels: [string, number][];
  benefits: [string, number][];
}

export interface EnrichedMarketData {
  skills: CombinedSkillData[];
  salaries: CombinedSalaryData[];
  employment: CombinedEmploymentData[];
}

export interface PublicDatasetData {
  metadata?: {
    title?: string;
    description?: string;
    lastUpdated?: string;
  };
  results?: Array<Record<string, string | number | boolean | null>>;
}

// ONS API Types
export interface ONSMetadata {
  title: string;
  description: string;
  releaseDate: string;
  nextRelease?: string;
  source: string;
  methodology?: string;
}

export interface ONSDataPoint {
  dimensions: Record<string, string>;
  label: string;
  value: number;
}

export interface ONSDataset {
  observations: ONSDataPoint[];
  dimensions: {
    [key: string]: {
      label: string;
      options: Array<{
        id: string;
        label: string;
      }>;
    };
  };
}

export interface ESCOSkill {
  id: string;
  name: string;
  description?: string;
  type: string;
  reuseLevel: string;
}

export interface ESCOResponse {
  skills?: {
    id: string;
    name: string;
    description: string;
    type: string;
    reuseLevel: string;
  }[];
  occupations?: {
    id: string;
    name: string;
    description: string;
    skills: string[];
  }[];
  // Add the _embedded property for the API response format
  _embedded?: {
    results: {
      id: string;
      name: string;
      type: string;
      reuseLevel: string;
    }[];
  };
  // Add the page property for pagination
  page?: {
    size: number;
    totalElements: number;
    totalPages: number;
    number: number;
  };
}

export interface ESCOQueryParams {
  language?: string;
  limit?: number;
  offset?: number;
  selectedVersion?: string;
  viewObsolete?: boolean;
}

export type DatasetContent = ONSDataset | ESCOResponse;

export interface PublicDataset {
  url: string;
  lastUpdated: Date;
  data: DatasetContent | null;
  metadata?: ONSMetadata | null;
  error: string;
}

export type DatasetCategory =
  | 'EMPLOYMENT_DATASETS'
  | 'SALARY_DATASETS'
  | 'SKILLS_DATASETS';

export interface ONSDatasetMap {
  EMPLOYMENT_DATASETS: string[];
  SALARY_DATASETS: string[];
  SKILLS_DATASETS: string[];
}

export interface CombinedSkillData {
  name: string;
  count: number;
  demandScore: number;
  averageSalary: number;
  growthRate: number;
  relatedSkills: string[];
  topEmployers: string[];
  description?: string;
  alternateNames?: string[];
  skillLevel?: string;
  commonRoles: string[];
}

export interface CombinedSalaryData {
  jobTitle: string;
  averageSalary: number;
  salaryRange: {
    min: number;
    max: number;
    percentile25: number;
    percentile75: number;
  };
  regionalVariation: {
    region: string;
    adjustment: number;
  }[];
  experienceImpact: {
    level: string;
    multiplier: number;
  }[];
  industryComparison: {
    industry: string;
    averageSalary: number;
  }[];
}

export interface CombinedEmploymentData {
  industry: string;
  currentEmployment: number;
  growthRate: number;
  projectedGrowth: number;
  keyMetrics: {
    turnoverRate: number;
    jobCreation: number;
    retirementRate: number;
  };
  trends: {
    date: string;
    value: number;
  }[];
}

export interface CombinedEducationData {
  level: string;
  percentage: number;
  averageSalary: number;
  demandTrend: number;
  requiredByIndustry: {
    industry: string;
    percentage: number;
  }[];
  commonRoles: string[];
}

export interface ESCOTopConcept {
  title: string;
  uri: string;
  href: string;
  code?: string;
}

export interface ESCOAPIResponse {
  hasTopConcept?: ESCOTopConcept[];
  _links?: {
    self: {
      href: string;
      uri: string;
      title: string;
    };
  };
}

export interface JobCategory {
  name: string;
  pattern: RegExp;
}

export interface SkillsMap {
  programmingLanguages: string[];
  frontend: string[];
  backend: string[];
  databases: string[];
  cloudAndDevOps: string[];
  toolsAndPractices: string[];
  mobile: string[];
  dataAndAI: string[];
  security: string[];
  softSkills: string[];
  business: string[];
}

export interface CoverLetterRequest {
  resume:
    | string
    | {
        sections: ResumeSection[];
      };
  job_description: string;
  company_name: string;
  hiring_manager?: string;
}

export interface CoverLetterResponse {
  cover_letter: string;
  content?: string; // Add content property for compatibility
  customization_score: number;
  key_points_highlighted: string[];
}

export interface FunnelState {
  step: number;
  job: Job | null;
  selectedResume: Resume | null;
  jobSaved: boolean;
  selectedProduct: Product | null;
  credits: number;
  processingResults: boolean;
  resultError: string | null;
  results: BasicPlanResult | ProPlanResult | UltimatePlanResult | null;
  generatedResults: BasicPlanResult | ProPlanResult | UltimatePlanResult | null;
  showResults: boolean;
  analysisHistory: {
    jobId: string | number; // Can be either string (UUID) or number (DB auto-increment)
    timestamp: number;
  }[];
  lastAnalysisTimestamp: number | null;
  forceReanalysis: boolean;
}

export interface StepProps {
  state: FunnelState;
  setState: React.Dispatch<React.SetStateAction<FunnelState>>;
  isLoading: boolean;
  user: User;
}

export enum PlanType {
  BASIC = 'BASIC',
  PRO = 'PRO',
  ULTIMATE = 'ULTIMATE'
}

export interface MarketTrendsInsights {
  overview: string;
  keySkills: string[];
  salaryInfo: string;
  growthOpportunities: string;
  recommendations: string[];
  timeRange: string;
}

export interface SkillImprovementResource {
  title: string;
  url?: string;
  link?: string;
  description: string;
  type: string;
  resource_type: string;
  difficulty: string;
}

export interface SkillResource {
  title: string;
  url?: string;
  link?: string;
  type: 'video' | 'article' | 'course' | 'documentation';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  description: string;
}

export interface SkillResourceGroup {
  skill: string;
  resources: SkillResource[];
}

export interface SkillImprovementResponse {
  skill_resources: SkillResourceGroup[];
}

export type LearningResourcesResult = SkillImprovementResponse;

export interface SkillImprovementRequest {
  resume?: string; // Must be a string, either raw text or stringified JSON
  job_description?: string;
  job?: string;
  skills_gap?: SkillGap[];
  skills?: string[]; // Added to support the skills property used in generate-ultimate-results
}

export interface ChatMessage {
  role: string;
  content: string;
  timestamp?: string;
  metadata?: {
    confidence?: number;
    sources?: string[];
    processingTime?: number;
  };
}

export interface CareerCoachingRequest {
  resumes: (string | object)[];
  job_description: string;
  chat_history?: ChatMessage[];
  question?: string;
  preferences?: {
    focus_areas?: string[];
    detail_level?: 'brief' | 'detailed' | 'comprehensive';
    include_resources?: boolean;
    language?: string;
  };
  context?: {
    previous_interactions?: number;
    user_experience_level?: string;
    specific_concerns?: string[];
  };
}

export interface RelevantSection {
  title: string;
  content: string;
  score: number;
  keywords?: string[];
  context?: string;
  source_type?: 'resume' | 'job_description' | 'market_data';
  importance: 'high' | 'medium' | 'low';
}

export interface CareerCoachingResponse {
  answer: string;
  relevant_sections: RelevantSection[];
  confidence_score: number;
  follow_up_questions: string[];
  analysis: CareerAdvice;
  recommendations: SkillResourceGroup[];
  metadata: {
    processing_time: number;
    model_version: string;
    timestamp: string;
    confidence: number;
    sources: string[];
  };
}

export interface CareerAdvice {
  strengths: string[];
  areas_for_improvement: string[];
  action_items: string[];
  recommendations: {
    short_term: string[];
    long_term: string[];
  };
  skill_development: {
    skill: string;
    importance: 'high' | 'medium' | 'low';
    resources: string[];
  }[];
}

export interface DashboardCoverLetterResult {
  content: string;
  key_points_highlighted?: string[];
}

export interface ProPlanResult {
  planType: PlanType;
  atsResult: ATSAnalysisResponse;
  coverLetter: CoverLetterResponse;
  skillsGap: SkillGap[];
  skillImprovements: SkillImprovementResponse;
  mockInterview: InterviewQuestionsResult;
}

export interface UltimatePlanResult {
  planType: PlanType;
  atsResult: ATSAnalysisResponse;
  coverLetter: CoverLetterResponse;
  skillsGap: SkillGap[];
  skillImprovements: SkillImprovementResponse[];
  mockInterview: {
    estimated_level: number;
    questions: string[];
    difficulty_levels: number[];
  };
  careerMatches: CareerMatchingResponse;
  careerCoaching: CareerCoachingResponse;
  marketTrends?: {
    jobGrowth?: Array<{ quarter: string; growth: number }>;
    salaryTrends?: Array<{
      month: string;
      tech: number;
      finance: number;
      healthcare: number;
    }>;
    skillDemand?: Array<{ skill: string; count: number }>;
    remoteWorkTrends?: Array<{
      type: string;
      percentage: number;
      averageSalary: number;
      industryBreakdown: Array<{ industry: string; percentage: number }>;
    }>;
    industryBreakdown?: Array<{ industry: string; percentage: number }>;
  };
}

export interface BasicPlanResult {
  planType: PlanType;
  atsResult: ATSAnalysisResponse;
  coverLetter: CoverLetterResponse;
}

export interface AnalysisHistoryEntry {
  jobDescription: string;
  jobLink: string;
  result: JobAnalysisResult;
  timestamp: number;
}

export interface UploadJobFormProps {
  onJobDataChange: (description: string, link: string) => void;
  onSubmit: (description: string, link: string) => Promise<Job>;
  initialJob: Job | null;
  submitButtonText: string;
  isLoading: boolean;
  autoAdvance?: boolean;
  onComplete: (savedJob: Job) => void;
  isOpen?: boolean;
  onOpenChange?: React.Dispatch<React.SetStateAction<boolean>>;
  showHeader?: boolean; // Show "Add a job posting..." text and button
  showPreview?: boolean; // Show the job preview card
  externalDialogOpen?: boolean; // External dialog control
  onExternalDialogClose?: () => void;
  autoOpenDialog?: boolean;
}

export interface UploadJobDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onJobUploaded: (job: Job) => void; // Changed from () => void to (job: Job) => void
  userId: string;
}

export interface JobPosting {
  id: string;
  title: string;
  company: string;
  description: string;
  requirements?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface DashboardContextValue {
  selectedResume: Resume | null;
  selectedJob: JobPosting | null;
  setSelectedResume: (resume: Resume | null) => void;
  setSelectedJob: (job: JobPosting | null) => void;
}

export interface VoiceSettingsType {
  rate: number;
  pitch: number;
  voiceName: string;
}

export interface InterviewSetupConfig {
  numQuestions: number;
  isVoiceMode: boolean;
  voiceSettings?: VoiceSettingsType;
}

export interface InterviewSessionState {
  isInProgress: boolean;
  currentQuestionIndex: number;
  startTime?: Date;
  endTime?: Date;
  answers: QuestionAnswer[];
}

export interface InterviewConfig {
  focusArea: string;
  difficulty: number;
  timeLimit?: number;
  includeTechnical: boolean;
  includeBehavioral: boolean;
}

export interface InterviewGenerationRequest {
  resume: string;
  jobDescription: string;
  config: InterviewConfig;
}

export interface InterviewProgress {
  totalQuestions: number;
  questionsAnswered: number;
  timeElapsed: number;
  estimatedTimeRemaining?: number;
  currentScore?: number;
}

export interface SpeechServiceState {
  isListening: boolean;
  transcript: string;
  availableVoices: SpeechSynthesisVoice[];
  selectedVoice: SpeechSynthesisVoice | null;
  rate: number;
  pitch: number;
  isSpeechRecognitionSupported: boolean;
  isSpeechSynthesisSupported: boolean;
  error: string | null;
}

export interface SpeechServiceActions {
  startListening: () => void;
  stopListening: () => void;
  toggleListening: () => void;
  speakText: (text: string) => Promise<void>;
  stopSpeaking: () => void;
  setTranscript: (transcript: string) => void;
  clearTranscript: () => void;
  setSelectedVoice: (voice: SpeechSynthesisVoice | null) => void;
  setRate: (rate: number) => void;
  setPitch: (pitch: number) => void;
}

export interface SpeechService
  extends SpeechServiceState,
    SpeechServiceActions {}

export interface VoiceSettingsProps {
  speechService: SpeechService;
  initialSettings?: VoiceSettingsType;
  onSettingsChange: (settings: VoiceSettingsType) => void;
}

export interface CareerCoachingResult {
  advice: string;
  strengths: string[];
  areas_for_improvement: string[];
  action_items: string[];
  career_path_recommendations: {
    title: string;
    description: string;
    match_percentage: number;
  }[];
}

export interface MarketTrendsResult {
  jobGrowth?: {
    quarter: string;
    growth: number;
  }[];
  salaryTrends?: {
    month: string;
    tech: number;
    finance: number;
    healthcare: number;
  }[];
  skillDemand?: {
    skill: string;
    count: number;
  }[];
  industryBreakdown?: {
    industry: string;
    percentage: number;
  }[];
  remoteWorkTrends?: {
    type: string;
    percentage: number;
    averageSalary: number;
    industryBreakdown: {
      industry: string;
      percentage: number;
    }[];
  }[];
  contractTypes?: {
    type: string;
    percentage: number;
    averageSalary: number;
    trend: number;
  }[];
  regionalComparison?: {
    region: string;
    averageSalary: number;
    jobCount: number;
    growthRate: number;
    topSkills: string[];
  }[];
}

export interface InterviewResult {
  questions: {
    question: string;
    difficulty: number;
    category?: string;
    expectedAnswer?: string;
    scoringCriteria?: string[];
  }[];
  estimatedDuration: number;
  skillsAssessed: string[];
  difficulty: {
    overall: number;
    byCategory?: Record<string, number>;
  };
  recommendations?: string[];
  metadata?: {
    generatedAt: string;
    version: string;
  };
}

export interface ComprehensiveAnalysisResult {
  match_score: Record<string, number | string>;
  skills_analysis: Record<
    string,
    {
      identified: string[];
      missing: string[];
      proficiency: Record<string, string>;
      relevance: Record<string, number>;
    }
  >;
  section_recommendations: Record<string, string[]>; // Changed from recommendations
  keyword_analysis: Record<
    string,
    {
      found: string[];
      missing: string[];
      frequency: Record<string, number>;
    }
  >;
  format_analysis: Record<
    string,
    {
      score: number;
      feedback: string[];
    }
  >;
  skill_matches: Array<{
    skill: string;
    match_level: number;
    context: string;
    importance: number;
  }>;
  overall_match: number;
  overall_match_percentage: number;
  keyword_match_percentage: number;
  skill_match_percentage: number;
  missing_keywords: string[];
  missing_skills: string[];
  readability_score: number;
  recommendations: string[]; // General recommendations
}

export interface EnhancedResumeSection {
  section: string;
  original: string;
  enhanced: string;
  explanation: string;
}

export interface BeforeAfterComparison {
  original: {
    content: string;
    metrics: {
      wordCount: number;
      readabilityScore: number;
      keywordDensity: number;
    };
  };
  enhanced: {
    content: string;
    metrics: {
      wordCount: number;
      readabilityScore: number;
      keywordDensity: number;
    };
    improvements: string[];
  };
}

export interface ResumeEnhancementResponse {
  enhanced_sections: EnhancedResumeSection[];
  overall_improvements: string[];
  keyword_additions: string[];
  format_improvements: string[];
  before_after_comparison: BeforeAfterComparison;
  full_enhanced_resume: string; // Added field for full enhanced resume text
}

export interface CareerProgression {
  level: string;
  title: string;
  timeframe: string;
  skills_required: string[];
  salary_range: {
    min: number;
    max: number;
    currency: string;
  };
  certification_requirements?: string[];
}

export interface CareerPathOption {
  path: string;
  suitability_score: number;
  progression: CareerProgression[];
  industry_outlook: string;
  required_development: string[];
}

export interface CurrentAssessment {
  skills: {
    name: string;
    level: string;
    relevance: number;
  }[];
  experience: {
    years: number;
    relevant_roles: string[];
  };
  education: {
    level: string;
    relevance: number;
  };
}

export interface DevelopmentMilestone {
  timeframe: string;
  goals: string[];
  skills_to_acquire: string[];
  suggested_resources: {
    type: string;
    links: string[];
  }[];
}

export interface CareerPathResponse {
  current_assessment: CurrentAssessment;
  career_paths: CareerPathOption[];
  development_roadmap: {
    short_term: DevelopmentMilestone;
    medium_term: DevelopmentMilestone;
    long_term: DevelopmentMilestone;
  };
}

export interface ResumeData {
  content: string;
  metadata?: {
    format: string;
    version?: number;
    lastModified?: string;
  };
  sections?: {
    title: string;
    content: string;
  }[];
}

export interface JobDescriptionData {
  content: string;
  metadata?: {
    source: string;
    postDate?: string;
    company?: string;
  };
  requirements?: string[];
}

export interface EnhancedATSAnalysisRequest {
  resume: string | ResumeData;
  job_description: string | JobDescriptionData;
  analysis_level: 'basic' | 'comprehensive' | 'expert';
  include_examples: boolean;
}

export interface EnhancedSkillsGapRequest {
  resume: string | ResumeData;
  job_description: string | JobDescriptionData;
  include_learning_resources: boolean;
}

export interface ResumeEnhancementRequest {
  resume: string;
  job_description: string;
  focus_areas: string[];
  tone: 'professional' | 'confident' | 'technical' | string;
}

export interface CareerPathRequest {
  resume: string;
  desired_field?: string;
  experience_level: 'entry' | 'mid' | 'senior' | 'executive';
  location?: string;
}
