// hooks/useTimeOnPage.tsx
'use client';

import { useEffect, useRef } from 'react';
import { trackTimeOnPage } from '@/lib/ga-events';

interface UseTimeOnPageOptions {
  pageName: string;
  enabled?: boolean;
  trackOnUnmount?: boolean;
}

export function useTimeOnPage({
  pageName,
  enabled = true,
  trackOnUnmount = true
}: UseTimeOnPageOptions) {
  const startTimeRef = useRef<number>(Date.now());

  useEffect(() => {
    if (!enabled) return;

    // Reset start time when page changes
    startTimeRef.current = Date.now();

    const trackTime = () => {
      const timeSpent = Math.round((Date.now() - startTimeRef.current) / 1000);
      if (timeSpent > 5) {
        // Only track if more than 5 seconds
        trackTimeOnPage(pageName, timeSpent);
      }
    };

    // Track on page visibility change (user switching tabs)
    const handleVisibilityChange = () => {
      if (document.hidden) {
        trackTime();
      } else {
        // Reset timer when user returns
        startTimeRef.current = Date.now();
      }
    };

    // Track on beforeunload
    const handleBeforeUnload = () => {
      trackTime();
    };

    if (trackOnUnmount) {
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    // Cleanup function
    return () => {
      if (trackOnUnmount) {
        trackTime();
        document.removeEventListener(
          'visibilitychange',
          handleVisibilityChange
        );
        window.removeEventListener('beforeunload', handleBeforeUnload);
      }
    };
  }, [pageName, enabled, trackOnUnmount]);
}
