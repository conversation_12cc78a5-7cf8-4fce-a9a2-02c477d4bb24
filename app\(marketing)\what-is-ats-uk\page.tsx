export const dynamic = 'force-static';
export const revalidate = 86400;

import type { Metadata } from 'next';
import { ThemedLink as Link } from 'components/ui/Link';
import JsonLd from '@/components/seo/JsonLd';
import { CallToAction } from '@/components/CallToAction';

export const metadata: Metadata = {
  title: 'What is ATS? UK Applicant Tracking Systems Guide 2025',
  description:
    'Complete guide to ATS systems used by UK employers. Learn how applicant tracking systems work and how to optimise your CV for UK job applications.',
  alternates: { canonical: '/what-is-ats-uk' }
};

export default function WhatIsAtsUk() {
  const howToSchema = {
    '@context': 'https://schema.org',
    '@type': 'HowTo',
    name: 'How to Optimise Your CV for UK ATS Systems',
    description:
      'Step-by-step guide to making your CV ATS-friendly for UK job applications',
    step: [
      {
        '@type': 'HowToStep',
        name: 'Format your CV correctly',
        text: 'Use standard fonts, single-column layout, and clear section headings'
      },
      {
        '@type': 'HowToStep',
        name: 'Include UK-specific keywords',
        text: 'Use British spelling and include relevant qualifications and professional registrations'
      },
      {
        '@type': 'HowToStep',
        name: 'Test your CV',
        text: 'Use an ATS checker to ensure your CV is compatible with UK employer systems'
      }
    ]
  };

  const faqSchema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: 'Do all UK employers use ATS systems?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: '75% of UK employers with 50+ employees use ATS. This includes virtually all FTSE companies, NHS Trusts, and major recruitment agencies.'
        }
      },
      {
        '@type': 'Question',
        name: 'How long do CVs stay in ATS systems?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Most UK employers keep CVs for 6-12 months. Some, like NHS Trusts, may retain them for up to 2 years for future opportunities.'
        }
      },
      {
        '@type': 'Question',
        name: 'Can I bypass ATS by applying directly to hiring managers?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Rarely in large UK organisations. Most systems automatically route all applications through ATS, regardless of how they are submitted.'
        }
      },
      {
        '@type': 'Question',
        name: 'Do recruitment agencies use different ATS systems?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, major UK agencies like Reed, Hays, and Michael Page each use different systems, but optimisation principles remain similar.'
        }
      },
      {
        '@type': 'Question',
        name: 'How often should I update my CV for ATS?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Review quarterly and update for each application. ATS algorithms and job requirements evolve constantly.'
        }
      }
    ]
  };

  return (
    <div className="relative isolate min-h-screen overflow-hidden ">
      <div className="relative z-10 container mx-auto px-4 py-16 max-w-[1200px] text-slate-200">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-blue-500/20 border border-blue-400/30 rounded-full px-4 py-2 mb-6">
            <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
            <span className="text-blue-300 text-sm font-medium">
              UK ATS Guide 2025
            </span>
          </div>

          <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mb-6 text-white text-[clamp(2rem,6vw,3.5rem)] sm:text-[clamp(2.5rem,8vw,4.5rem)]">
            What is <span className="text-yellow-400">ATS</span>? The Complete
            UK Guide to Applicant Tracking Systems
          </h1>

          <p className="text-lg text-slate-300 max-w-3xl mx-auto mb-8 leading-relaxed">
            If you&apos;ve ever wondered why your perfectly qualified CV seems
            to disappear into a &quot;black hole&quot; after applying for UK
            jobs, you&apos;ve likely encountered an ATS.
            <span className="text-yellow-400 font-semibold">
              {' '}
              75% of UK employers
            </span>{' '}
            use these systems to filter CVs automatically.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <CallToAction
              label="Test My CV for UK Jobs Now"
              sublabel="Free ATS compatibility check"
              href="/ats-resume-checker"
            />
          </div>
        </div>

        {/* Key Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-2">75%</div>
            <div className="text-slate-300">UK employers use ATS</div>
          </div>
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-2">95%</div>
            <div className="text-slate-300">FTSE 100 companies</div>
          </div>
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-2">85%</div>
            <div className="text-slate-300">Public sector usage</div>
          </div>
        </div>

        {/* What is ATS Section */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-yellow-400 text-2xl">?</span>
            <h2 className="text-3xl font-bold text-white">
              What Does ATS Stand For?
            </h2>
          </div>

          <div className="bg-gradient-to-r from-yellow-400/10 to-yellow-600/10 border border-yellow-400/20 rounded-xl p-8 mb-8">
            <p className="text-lg mb-6">
              ATS stands for{' '}
              <strong className="text-yellow-400">
                Applicant Tracking System
              </strong>
              —software that automatically scans, parses, and ranks CVs based on
              how well they match job requirements. Think of it as a digital
              gatekeeper that determines whether your application reaches human
              recruiters.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Automated CV scanning and parsing</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Keyword matching against job descriptions</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Ranking candidates by relevance score</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Integration with UK job boards</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Major UK Employers Section */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-yellow-400 text-2xl">🏢</span>
            <h2 className="text-3xl font-bold text-white">
              Which UK Employers Use ATS?
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Major Companies */}
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-xl font-semibold mb-4 text-yellow-400">
                Major UK Companies
              </h3>
              <div className="space-y-3">
                {[
                  {
                    company: 'NHS',
                    system: 'Trac JobTrain',
                    detail: 'Most NHS Trust positions'
                  },
                  {
                    company: 'Amazon UK',
                    system: 'Custom ATS',
                    detail: 'All UK warehouse & corporate roles'
                  },
                  {
                    company: 'Barclays',
                    system: 'SuccessFactors',
                    detail: 'Banking and finance positions'
                  },
                  {
                    company: 'Tesco',
                    system: 'Workday',
                    detail: 'Retail and corporate opportunities'
                  },
                  {
                    company: 'British Airways',
                    system: 'Oracle HCM',
                    detail: 'Aviation careers'
                  }
                ].map((item, index) => (
                  <div
                    key={index}
                    className="border-l-2 border-yellow-400 pl-4"
                  >
                    <div className="font-semibold text-white">
                      {item.company}
                    </div>
                    <div className="text-sm text-yellow-300">{item.system}</div>
                    <div className="text-sm text-slate-400">{item.detail}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* By Sector */}
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-xl font-semibold mb-4 text-yellow-400">
                Usage by Sector
              </h3>
              <div className="space-y-4">
                {[
                  {
                    sector: 'Public Sector',
                    percentage: 85,
                    description: 'NHS, councils, government'
                  },
                  {
                    sector: 'FTSE 100',
                    percentage: 95,
                    description: 'Virtually all major corporations'
                  },
                  {
                    sector: 'SMEs (50+ employees)',
                    percentage: 60,
                    description: 'Growing adoption rate'
                  },
                  {
                    sector: 'Recruitment Agencies',
                    percentage: 90,
                    description: 'Reed, Hays, Michael Page'
                  }
                ].map((item, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-white">
                        {item.sector}
                      </span>
                      <span className="text-yellow-400 font-bold">
                        {item.percentage}%
                      </span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2 mb-1">
                      <div
                        className="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-sm text-slate-400">
                      {item.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* How ATS Works - Interactive Timeline */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-green-500 text-2xl">⚡</span>
            <h2 className="text-3xl font-bold text-white">
              How UK ATS Systems Work
            </h2>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <div className="space-y-6">
              {[
                {
                  step: 1,
                  title: 'CV Upload/Submission',
                  description:
                    'Candidate applies via company website or job board; CV automatically uploaded to ATS database',
                  icon: '📄'
                },
                {
                  step: 2,
                  title: 'Parsing and Extraction',
                  description:
                    'ATS converts CV into structured data, extracts details and identifies skills',
                  icon: '🔍'
                },
                {
                  step: 3,
                  title: 'Keyword Matching',
                  description:
                    'Compares CV content to job description keywords and UK-specific terms',
                  icon: '🎯'
                },
                {
                  step: 4,
                  title: 'Scoring and Ranking',
                  description:
                    'Assigns compatibility score and filters out low-ranking CVs',
                  icon: '📊'
                },
                {
                  step: 5,
                  title: 'Human Review',
                  description: 'Top-ranked CVs forwarded to recruiters',
                  icon: '👥'
                }
              ].map((item, index) => (
                <div
                  key={index}
                  className="flex items-start gap-4 p-4 rounded-lg hover:bg-white/5 transition-colors"
                >
                  <div className="flex-shrink-0 w-12 h-12 border-2 border-blue-400 rounded-full flex items-center justify-center font-bold text-blue-400">
                    {item.step}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl">{item.icon}</span>
                      <h3 className="text-lg font-semibold text-white">
                        {item.title}
                      </h3>
                    </div>
                    <p className="text-slate-300">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-8 p-6 bg-yellow-500/10 border border-yellow-400/20 rounded-lg">
              <h4 className="font-semibold text-yellow-400 mb-3">
                UK-Specific Considerations:
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className="text-green-400">📅</span>
                  <span>
                    <strong>Date Formats:</strong> DD/MM/YYYY preferred
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-400">🎓</span>
                  <span>
                    <strong>Qualifications:</strong> UK degrees, GCSEs, A-levels
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-400">👥</span>
                  <span>
                    <strong>Professional Bodies:</strong> CQC, GMC, SRA, RICS
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-400">🇬🇧</span>
                  <span>
                    <strong>British Spelling:</strong> &quot;realise&quot;,
                    &quot;colour&quot;
                  </span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Common Failure Reasons */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8">
            Why CVs Fail UK ATS Systems
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                title: 'Missing UK Keywords',
                percentage: 40,
                color: 'red',
                examples: [
                  'Using &quot;resume&quot; instead of &quot;CV&quot;',
                  'American spelling variants',
                  'Missing sector-specific terms'
                ]
              },
              {
                title: 'Formatting Problems',
                percentage: 30,
                color: 'orange',
                examples: [
                  'Complex tables and graphics',
                  'Headers/footers with info',
                  'Non-standard fonts'
                ]
              },
              {
                title: 'Qualification Gaps',
                percentage: 20,
                color: 'yellow',
                examples: [
                  'UK qualifications unclear',
                  'Missing professional registrations',
                  'Degree classifications missing'
                ]
              },
              {
                title: 'Contact Info Issues',
                percentage: 10,
                color: 'blue',
                examples: [
                  'No +44 prefix on mobile',
                  'Missing UK postcode',
                  'Non-UK email domains'
                ]
              }
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">
                    {item.title}
                  </h3>
                  <span className={`text-${item.color}-400 font-bold text-xl`}>
                    {item.percentage}%
                  </span>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2 mb-4">
                  <div
                    className={`bg-${item.color}-400 h-2 rounded-full transition-all duration-1000`}
                    style={{ width: `${item.percentage}%` }}
                  ></div>
                </div>
                <div className="space-y-2">
                  {item.examples.map((example, i) => (
                    <div
                      key={i}
                      className="flex items-start gap-2 text-sm text-slate-300"
                    >
                      <span className="text-red-400 mt-1">•</span>
                      <span>{example}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* ATS Platforms Section */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-8">
            <span className="text-yellow-400 text-2xl">🛠️</span>
            <h2 className="text-3xl font-bold text-white">
              Popular ATS Platforms in the UK
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                name: 'Workday',
                companies: 'Tesco, Rolls-Royce, British Gas',
                tip: 'Use standard section headings',
                color: 'blue'
              },
              {
                name: 'SuccessFactors (SAP)',
                companies: 'Barclays, Shell, BT',
                tip: 'Include exact keywords from job description',
                color: 'green'
              },
              {
                name: 'SmartRecruiters',
                companies: 'John Lewis, Vodafone UK',
                tip: 'Clean, simple formatting crucial',
                color: 'purple'
              },
              {
                name: 'Taleo (Oracle)',
                companies: 'Many recruitment agencies',
                tip: 'Avoid fancy formatting',
                color: 'slate'
              },
              {
                name: 'Custom Systems',
                companies: 'Amazon UK, Google UK',
                tip: 'Focus on exact keyword matching',
                color: 'red'
              }
            ].map((platform, index) => (
              <div
                key={index}
                className="bg-slate-800/50 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6 hover:bg-slate-700/50 transition-all duration-300"
              >
                <h3
                  className={`text-lg font-semibold mb-2 ${
                    platform.color === 'blue'
                      ? 'text-blue-400'
                      : platform.color === 'green'
                        ? 'text-green-400'
                        : platform.color === 'purple'
                          ? 'text-purple-400'
                          : platform.color === 'slate'
                            ? 'text-slate-300'
                            : 'text-red-400'
                  }`}
                >
                  {platform.name}
                </h3>
                <p className="text-sm text-slate-400 mb-4">
                  {platform.companies}
                </p>
                <div className="bg-slate-700/50 rounded-lg p-3 border border-slate-600/30">
                  <p className="text-sm text-slate-300">
                    <span className="font-medium text-white">Tip:</span>{' '}
                    {platform.tip}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="mb-16">
          <div className="backdrop-blur-sm border border-slate-600/50 rounded-2xl p-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Beat UK ATS Systems?
            </h2>
            <p className="text-lg text-slate-300 mb-8 max-w-2xl mx-auto">
              Don&apos;t let your perfect CV get lost in the ATS black hole. Our
              free UK ATS checker simulates the exact systems used by NHS, FTSE
              100 companies, and major UK employers.
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div className="text-left">
                <h3 className="font-semibold text-yellow-400 mb-4">
                  What You&apos;ll Get:
                </h3>
                <div className="space-y-3">
                  {[
                    'Instant ATS compatibility score',
                    'UK-specific keyword analysis',
                    'Formatting recommendations',
                    'Industry-tailored suggestions',
                    'Free for first 3 daily scans'
                  ].map((item, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-slate-300">{item}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-slate-700/30 rounded-xl p-6 border border-slate-600/30">
                <div className="text-center mb-4">
                  <div className="flex justify-center mb-2">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className="text-yellow-400 text-xl">
                        ⭐
                      </span>
                    ))}
                  </div>
                  <div className="text-sm text-slate-400 mb-3">
                    Trusted by 10,000+ UK Job Seekers
                  </div>
                </div>
                <blockquote className="text-sm italic text-slate-300 mb-3">
                  &ldquo;Finally understood why my applications weren&apos;t
                  getting through. The UK-specific tips were
                  game-changing!&ldquo;
                </blockquote>
                <cite className="text-xs text-slate-400">
                  - Sarah M., Manchester
                </cite>
              </div>
            </div>

            <CallToAction
              label="Test My CV for UK Jobs Now"
              sublabel="Free ATS compatibility check"
              href="/ats-resume-checker"
            />
          </div>
        </section>

        {/* FAQ Section - Collapsible */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-cyan-500 text-2xl">❓</span>
            <h2 className="text-3xl font-bold text-white">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="space-y-4">
            {[
              {
                q: 'Do all UK employers use ATS systems?',
                a: 'No, but 75% of UK employers with 50+ employees use ATS. This includes virtually all FTSE companies, NHS Trusts, and major recruitment agencies.'
              },
              {
                q: 'How long do CVs stay in ATS systems?',
                a: 'Most UK employers keep CVs for 6-12 months. Some, like NHS Trusts, may retain them for up to 2 years for future opportunities.'
              },
              {
                q: 'Can I bypass ATS by applying directly to hiring managers?',
                a: 'Rarely in large UK organisations. Most systems automatically route all applications through ATS, regardless of how they&apos;re submitted.'
              },
              {
                q: 'Do recruitment agencies use different ATS systems?',
                a: 'Yes, major UK agencies like Reed, Hays, and Michael Page each use different systems, but optimisation principles remain similar.'
              },
              {
                q: 'How often should I update my CV for ATS?',
                a: 'Review quarterly and update for each application. ATS algorithms and job requirements evolve constantly.'
              }
            ].map((faq, index) => (
              <details
                key={index}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
              >
                <summary className="p-6 cursor-pointer hover:bg-white/5 transition-colors">
                  <span className="font-semibold text-white">{faq.q}</span>
                </summary>
                <div className="px-6 pb-6 text-slate-300">{faq.a}</div>
              </details>
            ))}
          </div>
        </section>

        {/* Additional Resources */}
        <section className="mb-10">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-emerald-500 text-2xl">📚</span>
            <h2 className="text-3xl font-bold text-white">
              Additional UK Job Search Resources
            </h2>
          </div>

          <div className="">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-blue-400 mb-4">
                Free UK Job Search Tools
              </h3>
              <div className="space-y-3">
                {[
                  {
                    title: 'Free UK ATS Resume Checker',
                    href: '/ats-resume-checker',
                    description:
                      'Instant ATS compatibility check - no signup required'
                  },
                  {
                    title: 'Complete JobSpaceAI Platform',
                    href: '/platform',
                    description:
                      'Access our full suite of AI-powered job search tools'
                  }
                ].map((tool, i) => (
                  <Link
                    key={i}
                    href={tool.href}
                    className="block p-3 rounded-lg hover:bg-white/5 transition-colors border-l-2 border-blue-400"
                  >
                    <div className="flex flex-col">
                      <span className="text-white hover:text-blue-400 transition-colors font-semibold">
                        {tool.title}
                      </span>
                      <span className="text-slate-300 text-sm mt-1">
                        {tool.description}
                      </span>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Industry-Specific Optimization Section */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-rose-500 text-2xl">🎯</span>
            <h2 className="text-3xl font-bold text-white">
              ATS Optimisation by UK Industry
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[
              {
                industry: 'NHS and Healthcare',
                color: 'red',
                icon: '🏥',
                keywords: 'NMC registered, CQC compliant, patient-centred care',
                include: 'GMC, NMC, or HCPC registration numbers',
                mention: 'NHS experience, ward types, patient volumes'
              },
              {
                industry: 'Financial Services',
                color: 'green',
                icon: '💰',
                keywords: 'FCA regulated, risk management, AML compliance',
                include: 'Professional qualifications (ACA, ACCA, CFA)',
                mention: 'Specific regulations (MiFID II, Basel III)'
              },
              {
                industry: 'Technology Sector',
                color: 'blue',
                icon: '💻',
                keywords: 'Programming languages, frameworks, methodologies',
                include: 'Certifications (AWS, Microsoft, Google Cloud)',
                mention: 'Agile, DevOps, specific tech stacks'
              },
              {
                industry: 'Legal Profession',
                color: 'purple',
                icon: '⚖️',
                keywords: 'SRA qualified, solicitor, specific practice areas',
                include: 'Law Society membership, chambers',
                mention: 'Court experience, case types'
              },
              {
                industry: 'Engineering',
                color: 'orange',
                icon: '🔧',
                keywords: 'CEng, IET member, RICS qualified',
                include: 'Professional institution memberships',
                mention: 'Project values, safety records, standards compliance'
              },
              {
                industry: 'Education',
                color: 'indigo',
                icon: '🎓',
                keywords: 'QTS, PGCE, curriculum development',
                include: 'Teaching qualifications and specializations',
                mention: 'Ofsted ratings, student outcomes, pastoral care'
              }
            ].map((sector, index) => {
              const colorClasses: Record<
                string,
                {
                  border: string;
                  text: string;
                  bg: string;
                  innerBorder: string;
                }
              > = {
                red: {
                  border: 'border-red-400/20',
                  text: 'text-red-400',
                  bg: 'bg-red-500/10',
                  innerBorder: 'border-red-400/20'
                },
                green: {
                  border: 'border-green-400/20',
                  text: 'text-green-400',
                  bg: 'bg-green-500/10',
                  innerBorder: 'border-green-400/20'
                },
                blue: {
                  border: 'border-blue-400/20',
                  text: 'text-blue-400',
                  bg: 'bg-blue-500/10',
                  innerBorder: 'border-blue-400/20'
                },
                purple: {
                  border: 'border-purple-400/20',
                  text: 'text-purple-400',
                  bg: 'bg-purple-500/10',
                  innerBorder: 'border-purple-400/20'
                },
                orange: {
                  border: 'border-orange-400/20',
                  text: 'text-orange-400',
                  bg: 'bg-orange-500/10',
                  innerBorder: 'border-orange-400/20'
                },
                indigo: {
                  border: 'border-indigo-400/20',
                  text: 'text-indigo-400',
                  bg: 'bg-indigo-500/10',
                  innerBorder: 'border-indigo-400/20'
                }
              };

              const colors =
                colorClasses[sector.color as keyof typeof colorClasses];

              return (
                <div
                  key={index}
                  className={`bg-white/5 backdrop-blur-sm border ${colors.border} rounded-xl p-6 hover:bg-white/10 transition-all duration-300`}
                >
                  <div className="flex items-center gap-3 mb-4">
                    <span className="text-2xl">{sector.icon}</span>
                    <h3 className={`text-xl font-semibold ${colors.text}`}>
                      {sector.industry}
                    </h3>
                  </div>

                  <div className="space-y-3 text-sm">
                    <div>
                      <span className="font-medium text-white">
                        Essential keywords:
                      </span>
                      <div
                        className={`mt-1 p-2 ${colors.bg} rounded border ${colors.innerBorder}`}
                      >
                        {sector.keywords}
                      </div>
                    </div>

                    <div>
                      <span className="font-medium text-white">Include:</span>
                      <p className="text-slate-300 mt-1">{sector.include}</p>
                    </div>

                    <div>
                      <span className="font-medium text-white">Mention:</span>
                      <p className="text-slate-300 mt-1">{sector.mention}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </section>

        {/* How to Format CV Section with Examples */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-yellow-500 text-2xl">📝</span>
            <h2 className="text-3xl font-bold text-white">
              How to Make Your CV ATS-Friendly
            </h2>
          </div>

          <div className="space-y-8">
            {/* Formatting Rules */}
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h3 className="text-xl font-semibold text-yellow-400 mb-6">
                Essential Formatting Rules
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-white">File Format</div>
                      <div className="text-sm text-slate-300">
                        Use .docx or .pdf (both widely accepted in UK)
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-white">Layout</div>
                      <div className="text-sm text-slate-300">
                        Single column layout, standard fonts (Arial, Calibri,
                        Times New Roman)
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-white">Font Size</div>
                      <div className="text-sm text-slate-300">
                        10-12pt for body text with clear section headings
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white text-xs">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-white">File Size</div>
                      <div className="text-sm text-slate-300">
                        Keep under 2MB
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Examples */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Contact Information Example */}
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <h4 className="font-semibold text-blue-400 mb-4">
                  ✅ Contact Information Example
                </h4>
                <div className="bg-slate-800/50 rounded-lg p-4 font-mono text-sm">
                  <div className="text-green-400">John Smith</div>
                  <div className="text-slate-300">
                    Birmingham, West Midlands, B1 1AA
                  </div>
                  <div className="text-slate-300">+44 7123 456789</div>
                  <div className="text-slate-300"><EMAIL></div>
                </div>
              </div>

              {/* Education Example */}
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <h4 className="font-semibold text-purple-400 mb-4">
                  ✅ Education Section Example
                </h4>
                <div className="bg-slate-800/50 rounded-lg p-4 font-mono text-sm">
                  <div className="text-purple-400">
                    University of Manchester
                  </div>
                  <div className="text-slate-300">
                    BSc Computer Science, First Class Honours (1:1)
                  </div>
                  <div className="text-slate-300">
                    September 2019 - June 2022
                  </div>
                </div>
              </div>
            </div>

            {/* Keywords Strategy */}
            <div className="bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-400/20 rounded-xl p-8">
              <h4 className="font-semibold text-cyan-400 mb-6">
                Keywords Strategy for UK Jobs
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h5 className="font-medium text-white mb-3">
                    Use Both Terms
                  </h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-300">CV</span>
                      <span className="text-cyan-400">/</span>
                      <span className="text-slate-300">resume</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">mobile</span>
                      <span className="text-cyan-400">/</span>
                      <span className="text-slate-300">cell phone</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="font-medium text-white mb-3">
                    Location Keywords
                  </h5>
                  <div className="space-y-2 text-sm text-slate-300">
                    <div>&quot;Greater London&quot;</div>
                    <div>&quot;West Midlands&quot;</div>
                    <div>&quot;North West England&quot;</div>
                  </div>
                </div>

                <div>
                  <h5 className="font-medium text-white mb-3">
                    Sector-Specific
                  </h5>
                  <div className="space-y-2 text-sm text-slate-300">
                    <div>&quot;CQC compliant&quot;</div>
                    <div>&quot;FCA regulated&quot;</div>
                    <div>&quot;Ofsted rated&quot;</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Myths Section */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-pink-500 text-2xl">🚫</span>
            <h2 className="text-3xl font-bold text-white">
              Debunking UK ATS Myths
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                myth: 'PDFs Don&apos;t Work with ATS',
                truth:
                  'Modern UK ATS systems read PDFs excellently. Many UK employers prefer PDFs as they preserve formatting.',
                icon: '📄'
              },
              {
                myth: 'You Must Use Specific Keywords Exactly',
                truth:
                  'UK ATS systems recognise synonyms and variations. Use natural language while including key terms.',
                icon: '🎯'
              },
              {
                myth: 'ATS Can&apos;t Read Creative CVs',
                truth:
                  'While simple formatting is safer, many UK ATS systems handle moderate design elements well.',
                icon: '🎨'
              },
              {
                myth: 'Two-Page CVs Are Always Better',
                truth:
                  'UK standard is 2 pages, but senior roles often require 3+ pages. ATS can handle longer CVs.',
                icon: '📋'
              },
              {
                myth: 'You Need Different CVs for Each ATS',
                truth:
                  'A well-optimised CV works across most UK ATS systems. Focus on content over system-specific tweaks.',
                icon: '🔄'
              },
              {
                myth: 'ATS Automatically Rejects Most CVs',
                truth:
                  "ATS ranks CVs; rejection depends on the employer's threshold and competition level.",
                icon: '⚡'
              }
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-colors"
              >
                <div className="flex items-start gap-3 mb-4">
                  <span className="text-2xl">{item.icon}</span>
                  <div>
                    <div className="font-semibold text-red-400 mb-2">
                      ❌ Myth: &quot;{item.myth}&quot;
                    </div>
                    <div className="text-green-400 font-medium mb-2">
                      ✅ Truth:
                    </div>
                    <div className="text-slate-300 text-sm">{item.truth}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Future Trends */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-6">
            <span className="text-violet-500 text-2xl">🔮</span>
            <h2 className="text-3xl font-bold text-white">
              The Future of ATS in UK Recruitment
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-violet-400 mb-4">
                Emerging Trends
              </h3>
              <div className="space-y-3 text-sm">
                {[
                  {
                    icon: '🤖',
                    text: 'AI-Powered Matching: More sophisticated candidate-job matching'
                  },
                  {
                    icon: '⚡',
                    text: 'Skills-Based Recruitment: Focus on competencies over qualifications'
                  },
                  {
                    icon: '🌈',
                    text: 'Diversity Screening: ATS systems promoting inclusive recruitment'
                  },
                  {
                    icon: '🎥',
                    text: 'Video CV Integration: Some UK employers testing video parsing'
                  }
                ].map((trend, i) => (
                  <div key={i} className="flex items-start gap-3">
                    <span className="text-lg">{trend.icon}</span>
                    <span className="text-slate-300">{trend.text}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-blue-400 mb-4">
                2025 Predictions
              </h3>
              <div className="space-y-3 text-sm">
                {[
                  'Increased SME adoption (expected 70% by end of 2025)',
                  'Better integration with LinkedIn and UK job boards',
                  'More sophisticated keyword context understanding',
                  'Enhanced mobile application experiences'
                ].map((prediction, i) => (
                  <div key={i} className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300">{prediction}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-green-400 mb-4">
                For Job Seekers
              </h3>
              <div className="space-y-3 text-sm">
                {[
                  'Skills-based CVs becoming more important',
                  'Need for continuous CV optimisation',
                  'Importance of staying current with ATS technology',
                  'Greater emphasis on quantifiable achievements'
                ].map((tip, i) => (
                  <div key={i} className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300">{tip}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Schema Scripts */}
      <JsonLd id="howto-schema" data={JSON.stringify(howToSchema, null, 2)} />
      <JsonLd id="faq-schema" data={JSON.stringify(faqSchema, null, 2)} />
    </div>
  );
}
