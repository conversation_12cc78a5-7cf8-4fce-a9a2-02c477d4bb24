'use client';

import React, { useState, useEffect } from 'react';
import {
  ArrowRight,
  Check,
  X,
  Target,
  Zap,
  TrendingUp,
  FileText
} from 'lucide-react';

interface ResumeComparisonCardProps {
  className?: string;
}

const ResumeComparisonCard: React.FC<ResumeComparisonCardProps> = ({
  className = ''
}) => {
  const [activeExample, setActiveExample] = useState(0);

  // Different professional examples that rotate
  const examples = [
    {
      profession: 'Marketing Manager',
      company: 'Digital Agency',
      beforeBullet: 'Managed social media campaigns for clients',
      afterBullet:
        'Led 15+ multi-channel marketing campaigns for B2B clients, generating £2.3M in revenue and increasing lead conversion rates by 45%',
      atsScore: { before: 32, after: 96 },
      keywords: { before: 2, after: 18 },
      coverLetterPreview:
        "With a proven track record of driving growth through data-driven marketing strategies, I'm excited to bring my expertise in digital marketing, campaign optimization, and client relationship management to your dynamic team..."
    },
    {
      profession: 'Software Developer',
      company: 'TechCorp London',
      beforeBullet: 'Built websites using JavaScript and React',
      afterBullet:
        'Developed 12+ responsive web applications using React.js, Node.js, and TypeScript, reducing page load times by 40% and improving user engagement metrics by 25%',
      atsScore: { before: 28, after: 94 },
      keywords: { before: 3, after: 16 },
      coverLetterPreview:
        "As a full-stack developer with expertise in modern JavaScript frameworks, cloud technologies, and agile development methodologies, I'm eager to contribute to TechCorp's innovative projects and help scale your platform..."
    },
    {
      profession: 'Registered Nurse',
      company: 'NHS Trust',
      beforeBullet: 'Administered medications and cared for patients on ward',
      afterBullet:
        'Delivered evidence-based care to 15+ patients per shift on busy medical ward, achieving 98% medication accuracy rate and reducing patient incidents by 20% through proactive monitoring',
      atsScore: { before: 35, after: 98 },
      keywords: { before: 1, after: 14 },
      coverLetterPreview:
        "I'm committed to compassionate, patient-centred care and eager to join your NHS Trust as a Band 5 Staff Nurse, bringing five years of acute-ward experience, advanced clinical skills, and current NMC registration..."
    },
    {
      profession: 'Financial Analyst',
      company: 'Investment Firm',
      beforeBullet: 'Analyzed financial data and created reports',
      afterBullet:
        'Conducted comprehensive financial modeling and risk analysis for £50M+ investment portfolio, delivering actionable insights that improved portfolio performance by 12% YoY',
      atsScore: { before: 31, after: 93 },
      keywords: { before: 2, after: 15 },
      coverLetterPreview:
        "With expertise in quantitative analysis, financial modeling, and risk assessment, I'm excited to contribute to your investment team's success through data-driven insights and strategic portfolio optimization..."
    }
  ];

  // Auto-rotate examples every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveExample((prev) => (prev + 1) % examples.length);
    }, 8000);
    return () => clearInterval(interval);
  }, [examples.length]);

  const currentExample = examples[activeExample];

  return (
    <div
      className={`bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl overflow-hidden ${className}`}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-red-400 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
          </div>
          <div className="flex items-center gap-2 text-xs text-slate-400">
            <FileText className="w-4 h-4" />
            <span>CV Analysis</span>
          </div>
        </div>
      </div>

      {/* Professional Header */}
      <div className="px-6 py-4 bg-gradient-to-r from-hero-yellow/10 to-orange-400/10">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-white font-bold text-lg">
            📋 {currentExample.profession}
          </h3>
          <div className="flex items-center gap-1 text-hero-yellow text-sm">
            <Target className="w-4 h-4" />
            <span>{currentExample.company}</span>
          </div>
        </div>

        {/* ATS Score Improvement */}
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-red-400 rounded-full"></div>
            <span className="text-slate-300">
              ATS Score: {currentExample.atsScore.before}%
            </span>
          </div>
          <ArrowRight className="w-4 h-4 text-hero-yellow" />
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span className="text-green-400 font-semibold">
              ATS Score: {currentExample.atsScore.after}%
            </span>
          </div>
        </div>
      </div>

      {/* Before/After Comparison */}
      <div className="px-6 py-6">
        <div className="space-y-4">
          {/* Before */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <X className="w-4 h-4 text-red-400" />
              <span className="text-red-400 font-semibold text-sm">
                BEFORE (Generic)
              </span>
            </div>
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
              <p className="text-slate-300 text-sm leading-relaxed">
                {currentExample.beforeBullet}
              </p>
            </div>
          </div>

          {/* AI Processing Animation */}
          <div className="flex items-center justify-center py-2">
            <div className="flex items-center gap-2 text-hero-yellow">
              <Zap className="w-4 h-4 animate-pulse" />
              <span className="text-xs font-medium">AI Enhancement</span>
              <div className="flex gap-1">
                <div className="w-1 h-1 bg-hero-yellow rounded-full animate-pulse"></div>
                <div className="w-1 h-1 bg-hero-yellow rounded-full animate-pulse animation-delay-200"></div>
                <div className="w-1 h-1 bg-hero-yellow rounded-full animate-pulse animation-delay-400"></div>
              </div>
            </div>
          </div>

          {/* After */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Check className="w-4 h-4 text-green-400" />
              <span className="text-green-400 font-semibold text-sm">
                AFTER (ATS-Optimized)
              </span>
            </div>
            <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
              <p className="text-slate-300 text-sm leading-relaxed">
                {currentExample.afterBullet}
              </p>
            </div>
          </div>
        </div>

        {/* Improvement Metrics */}
        <div className="grid grid-cols-3 gap-3 mt-6 p-4 bg-white/5 rounded-xl border border-white/10">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Target className="w-4 h-4 text-hero-yellow" />
            </div>
            <div className="text-lg font-bold text-white">
              {currentExample.atsScore.after}%
            </div>
            <div className="text-xs text-slate-400">ATS Score</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <FileText className="w-4 h-4 text-green-400" />
            </div>
            <div className="text-lg font-bold text-white">
              {currentExample.keywords.after}
            </div>
            <div className="text-xs text-slate-400">Keywords</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <TrendingUp className="w-4 h-4 text-purple-400" />
            </div>
            <div className="text-lg font-bold text-white">
              +{currentExample.atsScore.after - currentExample.atsScore.before}%
            </div>
            <div className="text-xs text-slate-400">Improvement</div>
          </div>
        </div>

        {/* Cover Letter Preview */}
        <div className="mt-6">
          <div className="flex items-center gap-2 mb-3">
            <FileText className="w-4 h-4 text-hero-yellow" />
            <span className="text-white font-semibold text-sm">
              AI Cover Letter:
            </span>
          </div>
          <div className="p-4 bg-gradient-to-r from-white/5 to-white/10 rounded-xl border border-white/10">
            <p className="text-slate-300 text-sm leading-relaxed">
              {currentExample.coverLetterPreview}
            </p>
          </div>
        </div>

        {/* Live Indicator */}
        <div className="flex items-center justify-center gap-2 mt-6 pt-4 border-t border-white/10">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-slate-400">
            Live demo - Try your CV free
          </span>
        </div>
      </div>

      {/* Progress Dots */}
      <div className="flex justify-center gap-2 pb-6">
        {examples.map((_, index) => (
          <button
            key={index}
            onClick={() => setActiveExample(index)}
            className={`transition-all duration-300 ${
              index === activeExample
                ? 'w-6 h-2 bg-hero-yellow rounded-full'
                : 'w-2 h-2 bg-white/30 rounded-full hover:bg-white/50'
            }`}
            aria-label={`View ${examples[index].profession} example`}
          />
        ))}
      </div>
    </div>
  );
};

export default ResumeComparisonCard;
