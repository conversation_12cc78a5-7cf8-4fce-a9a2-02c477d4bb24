'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import Cookies from 'js-cookie';

export interface ConsentState {
  analytics: boolean;
  advertising: boolean;
  functional: boolean;
  hasConsented: boolean;
}

interface ConsentContextType {
  consentState: ConsentState;
  isConsentBannerVisible: boolean;
  acceptAllConsent: () => void;
  rejectAllConsent: () => void;
  saveConsent: (consent: Partial<ConsentState>) => void;
  resetConsent: () => void;
}

// Create the context
const ConsentContext = createContext<ConsentContextType | undefined>(undefined);

const CONSENT_COOKIE_NAME = 'user_consent_preferences';
const CONSENT_EXPIRY_DAYS = 365;

export function ConsentProvider({ children }: { children: ReactNode }) {
  const [consentState, setConsentState] = useState<ConsentState>({
    analytics: false,
    advertising: false,
    functional: true,
    hasConsented: false,
  });

  const [isConsentBannerVisible, setIsConsentBannerVisible] = useState(false);

  useEffect(() => {
    // Check for existing consent
    const savedConsent = Cookies.get(CONSENT_COOKIE_NAME);
    
    if (savedConsent) {
      try {
        const parsedConsent: ConsentState = JSON.parse(savedConsent);
        setConsentState(parsedConsent);
        updateGoogleConsent(parsedConsent);
      } catch (error) {
        console.error('Error parsing consent cookie:', error);
        setIsConsentBannerVisible(true);
      }
    } else {
      // Show banner for new users
      setIsConsentBannerVisible(true);
    }
  }, []);

  const updateGoogleConsent = (consent: ConsentState) => {
    if (typeof window !== 'undefined') {
      // Initialize dataLayer if it doesn't exist
      if (!window.dataLayer) {
        window.dataLayer = [];
      }

      // Update consent via gtag if available (using your existing type signature)
      if (window.gtag) {
        window.gtag('consent', 'update', {
          analytics_storage: consent.analytics ? 'granted' : 'denied',
          ad_storage: consent.advertising ? 'granted' : 'denied',
          ad_user_data: consent.advertising ? 'granted' : 'denied',
          ad_personalization: consent.advertising ? 'granted' : 'denied',
          functionality_storage: consent.functional ? 'granted' : 'denied',
          personalization_storage: consent.analytics ? 'granted' : 'denied',
          security_storage: 'granted'
        });
      }

      // Push to dataLayer for GTM compatibility
      window.dataLayer.push({
        event: 'consent_update',
        analytics_consent: consent.analytics ? 'granted' : 'denied',
        ads_consent: consent.advertising ? 'granted' : 'denied',
        timestamp: new Date().toISOString()
      });

      console.log('🍪 Consent updated:', consent);
    }
  };

  const saveConsent = (newConsent: Partial<ConsentState>) => {
    const updatedConsent: ConsentState = {
      ...consentState,
      ...newConsent,
      hasConsented: true,
    };

    setConsentState(updatedConsent);
    setIsConsentBannerVisible(false);
    
    // Save to cookie
    Cookies.set(CONSENT_COOKIE_NAME, JSON.stringify(updatedConsent), {
      expires: CONSENT_EXPIRY_DAYS,
      secure: true,
      sameSite: 'strict',
    });

    updateGoogleConsent(updatedConsent);
  };

  const acceptAllConsent = () => {
    saveConsent({
      analytics: true,
      advertising: true,
      functional: true,
    });
  };

  const rejectAllConsent = () => {
    saveConsent({
      analytics: false,
      advertising: false,
      functional: true,
    });
  };

  const resetConsent = () => {
    Cookies.remove(CONSENT_COOKIE_NAME);
    setConsentState({
      analytics: false,
      advertising: false,
      functional: true,
      hasConsented: false,
    });
    setIsConsentBannerVisible(true);
  };

  const contextValue: ConsentContextType = {
    consentState,
    isConsentBannerVisible,
    acceptAllConsent,
    rejectAllConsent,
    saveConsent,
    resetConsent,
  };

  return (
    <ConsentContext.Provider value={contextValue}>
      {children}
    </ConsentContext.Provider>
  );
}

export function useConsent(): ConsentContextType {
  const context = useContext(ConsentContext);
  if (context === undefined) {
    throw new Error('useConsent must be used within a ConsentProvider');
  }
  return context;
}
