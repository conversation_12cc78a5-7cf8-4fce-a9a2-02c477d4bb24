import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import UploadResumeForm from '@/components/resume/uploadResumeForm';
import { Resume } from '@/app/types/globalTypes';
import { Loader2 } from 'lucide-react';
import {
  trackUserInteraction,
  trackFileUploadSuccess,
  trackFileUploadAbandon
} from '@/lib/ga-events';

interface UploadResumeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (resume: Resume) => Promise<void>;
  userId: string;
}

export const UploadResumeDialog: React.FC<UploadResumeDialogProps> = ({
  isOpen,
  onClose,
  onComplete,
  userId
}) => {
  // Add a local loading state to control when the dialog actually closes
  const [isProcessing, setIsProcessing] = useState(false);
  const [dialogOpenTime, setDialogOpenTime] = useState<number | null>(null);

  // Track dialog open/close events
  useEffect(() => {
    if (isOpen) {
      const openTime = Date.now();
      setDialogOpenTime(openTime);

      // Track dialog opened
      trackUserInteraction({
        interaction_type: 'dialog_open',
        element_type: 'upload_resume_dialog',
        user_journey: 'dashboard'
      });
    } else if (dialogOpenTime) {
      // Track dialog closed and time spent
      const timeSpent = Date.now() - dialogOpenTime;
      trackUserInteraction({
        interaction_type: 'dialog_close',
        element_type: 'upload_resume_dialog',
        user_journey: 'dashboard',
        time_spent_ms: timeSpent
      });
      setDialogOpenTime(null);
    }
  }, [isOpen, dialogOpenTime]);

  // Track abandonment on unmount if dialog was open
  useEffect(() => {
    return () => {
      if (isOpen && dialogOpenTime && !isProcessing) {
        const timeSpent = Date.now() - dialogOpenTime;
        trackFileUploadAbandon({
          file_type: 'cv_resume',
          abandon_stage: 'dialog_open',
          time_spent_ms: timeSpent,
          user_journey: 'dashboard'
        });
      }
    };
  }, [isOpen, dialogOpenTime, isProcessing]);

  const handleUploadComplete = async (resume: Resume) => {
    try {
      setIsProcessing(true);

      // Track successful upload in dashboard context
      const uploadTime = dialogOpenTime
        ? Date.now() - dialogOpenTime
        : undefined;
      trackFileUploadSuccess({
        file_type: 'cv_resume',
        upload_method: 'dashboard_dialog',
        file_source: 'dashboard_upload',
        upload_duration_ms: uploadTime,
        file_size_chars: resume.resume?.length || 0,
        user_journey: 'dashboard'
      });

      // Call onComplete and await it to ensure the resume is fully processed
      await onComplete(resume);

      // Track successful completion
      trackUserInteraction({
        interaction_type: 'upload_complete',
        element_type: 'upload_resume_dialog',
        user_journey: 'dashboard',
        time_spent_ms: uploadTime
      });

      // Only close the dialog after onComplete finishes
      onClose();
    } catch (error) {
      console.error('Error processing uploaded CV:', error);

      // Track processing error
      trackUserInteraction({
        interaction_type: 'upload_error',
        element_type: 'upload_resume_dialog',
        user_journey: 'dashboard',
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });

      // Show error to user (could add a toast notification here)
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        // Prevent closing the dialog while processing
        if (!open && isProcessing) {
          return;
        }
        onClose();
      }}
    >
      <DialogContent className="sm:max-w-2xl w-[90vw] max-h-[90vh] overflow-y-auto backdrop-blur-sm bg-hero-bg/90 border border-white/20 text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-[hsl(var(--hero-yellow))] text-xl">
            Upload CV
          </DialogTitle>
          <DialogDescription className="text-slate-300">
            Upload your CV in PDF or DOCX format to get started.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          {isProcessing ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-12 w-12 animate-spin text-[hsl(var(--hero-yellow))] mb-4" />
              <p className="text-white">Processing your CV...</p>
              <p className="text-slate-300 text-sm mt-2">
                This may take a moment
              </p>
            </div>
          ) : (
            <UploadResumeForm
              userId={userId}
              onComplete={handleUploadComplete}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
