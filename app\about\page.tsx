'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, <PERSON>rk<PERSON>, <PERSON><PERSON>, Shield, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import Link from 'next/link';

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

const FeatureCard = ({
  icon,
  title,
  description,
  delay
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay: number;
}) => (
  <motion.div
    variants={item}
    className="relative group"
    initial="hidden"
    animate="show"
    transition={{ delay }}
  >
    <Card className="backdrop-blur-sm bg-white/10 border border-white/20 text-white overflow-hidden relative hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-lg md:text-xl font-semibold gap-3">
          <div className="p-2 rounded-lg bg-hero-yellow/20 text-hero-yellow shrink-0">
            {icon}
          </div>
          <span className="leading-tight">{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm md:text-base text-slate-200 leading-relaxed">
          {description}
        </p>
      </CardContent>
    </Card>
  </motion.div>
);

const AboutPage = () => {
  return (
    <div className="relative w-full overflow-hidden min-h-screen">
      <div className="relative z-10 mx-auto w-full max-w-[1200px] px-4 py-16">
        <motion.div
          className="text-center mb-16"
          variants={fadeIn}
          initial="hidden"
          animate="visible"
          transition={{ duration: 0.6 }}
        >
          <span className="text-yellow-300 font-semibold">ABOUT US</span>
          <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
            AI CV Builder for UK Job Seekers – London, Manchester, Birmingham &
            More
          </h1>
          <motion.p
            className="text-lg md:text-xl text-slate-200 max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Job Space AI is purpose-built for UK job hunters in 2025, offering
            advanced CV optimisation, ATS checks and instant cover letters
            tailored for the UK market. Optimise your applications for London,
            Manchester, Birmingham, Edinburgh & Leeds jobs today.
          </motion.p>
        </motion.div>

        <div className="space-y-12">
          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <p className="text-lg mb-6 text-white">
              Each job application is unique.
            </p>
            <p className="text-lg mb-4 text-slate-200">
              Your CV needs to be perfectly tailored to stand out from the
              competition.
            </p>
            <div className="my-8 p-6 backdrop-blur-sm bg-white/5 border border-white/10 rounded-xl">
              <p className="text-lg font-medium mb-4 text-yellow-300">
                How it works:
              </p>
              <ul className="space-y-4 list-none pl-0">
                <li className="flex items-start gap-3">
                  <span className="text-hero-yellow mt-1 font-bold">1.</span>
                  <span className="text-white">Upload your CV</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-hero-yellow mt-1 font-bold">2.</span>
                  <span className="text-white">Add the job description</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-hero-yellow mt-1 font-bold">3.</span>
                  <span className="text-white">
                    Get AI-powered optimization suggestions
                  </span>
                </li>
              </ul>
            </div>
            <p className="text-lg mb-4 text-slate-200">
              Built by AI specialists and recruitment experts, we combine
              cutting-edge technology with industry expertise to give you the
              competitive edge.
            </p>
          </motion.div>

          <motion.div
            variants={container}
            initial="hidden"
            animate="show"
            className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12"
          >
            <FeatureCard
              icon={<FileText className="w-6 h-6" />}
              title="Smart CV Analysis"
              description="Get instant feedback on how well your CV matches specific job descriptions, with detailed suggestions for improvement."
              delay={0.4}
            />
            <FeatureCard
              icon={<Sparkles className="w-6 h-6" />}
              title="AI-Powered Optimization"
              description="Our AI engine provides tailored recommendations to optimize your CV for each application, increasing your match rate."
              delay={0.5}
            />
            <FeatureCard
              icon={<Bot className="w-6 h-6" />}
              title="Intelligent Insights"
              description="Receive data-driven insights about key skills and qualifications that matter most for your target roles."
              delay={0.6}
            />
            <FeatureCard
              icon={<Shield className="w-6 h-6" />}
              title="Privacy First"
              description="Your data is encrypted and secure. We never share your personal information or CV details with third parties."
              delay={0.7}
            />
          </motion.div>

          <motion.div
            className="backdrop-blur-sm bg-white/5 border border-white/10 p-8 rounded-xl mt-16"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <h2 className="text-2xl font-roboto-condensed font-bold mb-4 text-yellow-300">
              Our Commitment
            </h2>
            <p className="text-slate-200">
              We&apos;re dedicated to continuously improving our AI technology
              to provide you with the most accurate and helpful CV optimization
              tools. Our goal is to simplify the job application process and
              help you present your best self to potential employers.
            </p>
          </motion.div>

          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.9 }}
          >
            <div className="max-w-xl mx-auto">
              <h2 className="text-2xl font-roboto-condensed font-bold mb-4 text-white">
                Ready to Get Started?
              </h2>
              <p className="text-slate-200 mb-8">
                Join thousands of job seekers who are using AI to optimize their
                job applications and increase their interview success rate.
              </p>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link href="/onboarding">
                  <Button
                    size="lg"
                    className="group px-8 py-4 md:py-5 bg-hero-yellow text-[#111827] font-semibold rounded-full shadow-lg inline-flex items-center justify-center gap-3 hover:bg-hero-yellow-light active:scale-95 text-[16px] md:text-[18px] whitespace-nowrap"
                  >
                    <span>Try AI CV Analysis</span>
                    <ArrowRight className="w-5 h-5 md:w-6 md:h-6 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
