// lib/analytics-manager.ts
'use client';

// ✅ Import only the types we actually use
import type {
  PurchaseEventParams,
  ViewItemListEventParams,
  CustomEventParams,
  TrackEventFunction
} from '@/types/events';

// ✅ Local interfaces to avoid global conflicts
interface GtagConfig {
  debug_mode?: boolean;
  send_page_view?: boolean;
  page_path?: string;
  page_title?: string;
  custom_map?: Record<string, string>;
  groups?: string;
  linker?: {
    domains: string[];
  };
}

interface GtagConsentParams {
  analytics_storage?: 'granted' | 'denied';
  ad_storage?: 'granted' | 'denied';
  ad_user_data?: 'granted' | 'denied';
  ad_personalization?: 'granted' | 'denied';
  functionality_storage?: 'granted' | 'denied';
  security_storage?: 'granted' | 'denied';
  wait_for_update?: number;
}

// ✅ Enhanced Clarity API interface with proper method signatures
interface ClarityAPI {
  event: (name: string) => void;
  set: (key: string, value: string) => void;
  identify: (
    userId: string,
    sessionId?: string,
    pageId?: string,
    userHint?: string
  ) => void;
  consent: () => void;
  start: (config: { projectId: string }) => void;
  upgrade: (upgradeReason: string) => void;
}

// ✅ Define types for global analytics objects without extending Window
interface GlobalGtag {
  (command: 'config', targetId: string, config?: GtagConfig): void;
  (
    command: 'event',
    eventName: string,
    eventParams?: Record<string, unknown>
  ): void;
  (
    command: 'consent',
    consentArg: 'default' | 'update',
    consentParams: GtagConsentParams
  ): void;
  (command: 'js', date: Date): void;
  (command: string, ...args: unknown[]): void;
}

// ✅ Safe global object access - fixed type assertions
function getGlobalGtag(): GlobalGtag | undefined {
  if (typeof window === 'undefined') return undefined;
  return (window as unknown as { gtag?: GlobalGtag }).gtag;
}

function getGlobalClarity(): ClarityAPI | undefined {
  if (typeof window === 'undefined') return undefined;
  return (window as unknown as { clarity?: ClarityAPI }).clarity;
}

function getDataLayer(): unknown[] {
  if (typeof window === 'undefined') return [];
  const win = window as unknown as { dataLayer?: unknown[] };
  return win.dataLayer || [];
}

function setDataLayer(dataLayer: unknown[]): void {
  if (typeof window === 'undefined') return;
  (window as unknown as { dataLayer: unknown[] }).dataLayer = dataLayer;
}

function setGlobalGtag(gtag: GlobalGtag): void {
  if (typeof window === 'undefined') return;
  (window as unknown as { gtag: GlobalGtag }).gtag = gtag;
}

function setGlobalClarity(
  clarity: ClarityAPI | ((...args: unknown[]) => void)
): void {
  if (typeof window === 'undefined') return;
  (window as unknown as { clarity: typeof clarity }).clarity = clarity;
}

class AnalyticsManager {
  private static instance: AnalyticsManager;
  private gaLoaded = false;
  private clarityLoaded = false;
  private clarityReady = false;
  private gtmLoaded = false;
  private loadingPromises: Map<string, Promise<void>> = new Map();
  private clarityRetryCount = 0;
  private maxClarityRetries = 10;
  private clarityRetryDelay = 500;

  private constructor() {}

  static getInstance(): AnalyticsManager {
    if (!AnalyticsManager.instance) {
      AnalyticsManager.instance = new AnalyticsManager();
    }
    return AnalyticsManager.instance;
  }

  // ✅ Check if we're in a browser environment
  private isBrowser(): boolean {
    return typeof window !== 'undefined';
  }

  // ✅ Wait for Clarity API to be fully ready with retry mechanism
  private async waitForClarityReady(): Promise<boolean> {
    return new Promise((resolve) => {
      const checkClarity = () => {
        const clarity = getGlobalClarity();

        // Check if Clarity API is fully loaded and methods are available
        if (
          clarity &&
          typeof clarity.event === 'function' &&
          typeof clarity.set === 'function'
        ) {
          this.clarityReady = true;
          console.log('🔍 Clarity API is fully ready');
          resolve(true);
          return;
        }

        if (this.clarityRetryCount < this.maxClarityRetries) {
          this.clarityRetryCount++;
          const delay = this.clarityRetryDelay * this.clarityRetryCount;
          console.log(
            `🔍 Waiting for Clarity API... Attempt ${this.clarityRetryCount}/${this.maxClarityRetries} (delay: ${delay}ms)`
          );

          setTimeout(checkClarity, delay);
        } else {
          console.warn(
            '🔍 Clarity API failed to become ready after maximum retries'
          );
          resolve(false);
        }
      };

      checkClarity();
    });
  }

  // ✅ Load Google Analytics with deduplication
  async loadGA(gaId: string): Promise<void> {
    if (!this.isBrowser() || !gaId) return;

    const cacheKey = `ga-${gaId}`;

    // Return existing promise if already loading
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // Return immediately if already loaded
    if (this.gaLoaded && getGlobalGtag()) {
      return Promise.resolve();
    }

    const loadPromise = new Promise<void>((resolve, reject) => {
      try {
        // Check if script already exists
        const existingScript = document.querySelector(
          `script[src*="gtag/js?id=${gaId}"]`
        );
        if (existingScript && getGlobalGtag()) {
          this.gaLoaded = true;
          resolve();
          return;
        }

        // Initialize dataLayer if it doesn't exist
        const dataLayer = getDataLayer();
        setDataLayer(dataLayer.length > 0 ? dataLayer : []);

        const gtag =
          getGlobalGtag() ||
          function (...gtagArgs: unknown[]) {
            const currentDataLayer = getDataLayer();
            currentDataLayer.push(gtagArgs);
            setDataLayer(currentDataLayer);
          };

        setGlobalGtag(gtag as GlobalGtag);
        gtag('js', new Date());

        // Create and load script
        const script = document.createElement('script');
        script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
        script.async = true;

        script.onload = () => {
          // Initialize GA
          const loadedGtag = getGlobalGtag();
          if (loadedGtag) {
            loadedGtag('config', gaId, {
              debug_mode: process.env.NODE_ENV === 'development',
              send_page_view: false // We'll handle this manually
            });
            this.gaLoaded = true;
            console.log('📊 Google Analytics loaded successfully');
            resolve();
          } else {
            reject(new Error('gtag not available after script load'));
          }
        };

        script.onerror = (error) => {
          console.warn('Failed to load Google Analytics:', error);
          reject(error);
        };

        document.head.appendChild(script);
      } catch (error) {
        console.warn('Error loading Google Analytics:', error);
        reject(error);
      }
    });

    this.loadingPromises.set(cacheKey, loadPromise);
    return loadPromise;
  }

  // ✅ Enhanced Clarity loading with proper API readiness check
  async loadClarity(projectId: string): Promise<void> {
    if (!this.isBrowser() || !projectId) return;

    const cacheKey = `clarity-${projectId}`;

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    if (this.clarityLoaded && this.clarityReady && getGlobalClarity()) {
      return Promise.resolve();
    }

    const loadPromise = new Promise<void>((resolve, reject) => {
      try {
        // Check if Clarity is already loaded and ready
        if (getGlobalClarity() && this.clarityReady) {
          this.clarityLoaded = true;
          resolve();
          return;
        }

        // Set up Clarity function with queue
        interface ClarityQueueFunction {
          (...args: unknown[]): void;
          q?: unknown[];
        }

        const clarityQueue: ClarityQueueFunction = function (
          ...clarityArgs: unknown[]
        ) {
          if (!clarityQueue.q) {
            clarityQueue.q = [];
          }
          clarityQueue.q.push(clarityArgs);
        };

        setGlobalClarity(clarityQueue);

        // Load the Clarity script
        const script = document.createElement('script');
        script.async = true;
        script.src = `https://www.clarity.ms/tag/${projectId}`;

        script.onload = async () => {
          this.clarityLoaded = true;
          console.log(
            '🔍 Clarity script loaded, waiting for API to be ready...'
          );

          // Wait for Clarity API to be fully ready
          const isReady = await this.waitForClarityReady();
          if (isReady) {
            console.log('🔍 Microsoft Clarity loaded and ready successfully');
            resolve();
          } else {
            console.warn('🔍 Clarity loaded but API not ready');
            // Still resolve to prevent blocking, but mark as not ready
            this.clarityReady = false;
            resolve();
          }
        };

        script.onerror = (error) => {
          console.warn('Failed to load Microsoft Clarity:', error);
          reject(error);
        };

        // Insert script
        const firstScript = document.getElementsByTagName('script')[0];
        if (firstScript && firstScript.parentNode) {
          firstScript.parentNode.insertBefore(script, firstScript);
        } else {
          document.head.appendChild(script);
        }
      } catch (error) {
        console.warn('Error loading Microsoft Clarity:', error);
        reject(error);
      }
    });

    this.loadingPromises.set(cacheKey, loadPromise);
    return loadPromise;
  }

  // ✅ Safe Clarity event tracking
  private trackClarityEvent(eventName: string): boolean {
    if (!this.isBrowser() || !this.clarityReady) {
      return false;
    }

    try {
      const clarity = getGlobalClarity();
      if (clarity && typeof clarity.event === 'function') {
        clarity.event(eventName);
        console.log(`🔍 Clarity event tracked: ${eventName}`);
        return true;
      }
    } catch (error) {
      console.warn(`🔍 Failed to track Clarity event "${eventName}":`, error);
    }
    return false;
  }

  // ✅ Safe Clarity tag setting with proper error handling
  setClarityTags(tags: Record<string, string>): void {
    if (!this.isBrowser() || !this.clarityReady) {
      console.log('🔍 Clarity not ready for tag setting');
      return;
    }

    try {
      const clarity = getGlobalClarity();
      if (!clarity || typeof clarity.set !== 'function') {
        console.warn('🔍 Clarity set method not available');
        return;
      }

      let successCount = 0;
      const totalTags = Object.keys(tags).length;

      for (const [key, value] of Object.entries(tags)) {
        try {
          clarity.set(key, String(value));
          successCount++;
          console.log(`🔍 Clarity tag set: ${key} = ${value}`);
        } catch (error) {
          console.warn(`🔍 Failed to set Clarity tag "${key}":`, error);
        }
      }

      if (successCount === totalTags) {
        console.log(`🔍 All ${totalTags} Clarity tags set successfully`);
      } else {
        console.warn(
          `🔍 Only ${successCount}/${totalTags} Clarity tags set successfully`
        );
      }
    } catch (error) {
      console.error('🔍 Error setting Clarity tags:', error);
    }
  }

  // ✅ Type-safe event tracking with enhanced Clarity support
  trackEvent: TrackEventFunction = (eventName, parameters) => {
    if (!this.isBrowser()) return;

    try {
      // Track with Google Analytics
      const gtag = getGlobalGtag();
      if (gtag && this.gaLoaded) {
        gtag('event', String(eventName), parameters);
        console.log(`📊 GA event tracked: ${String(eventName)}`);
      }

      // Track with Clarity
      this.trackClarityEvent(String(eventName));
    } catch (error) {
      console.warn('Error tracking event:', error);
    }
  };

  // ✅ Enhanced trackCustomEvent with Clarity tags support
  trackCustomEvent(
    eventName: string,
    parameters?: CustomEventParams,
    clarityTags?: Record<string, string>
  ): void {
    // Track the event
    this.trackEvent(
      eventName as keyof import('@/types/events').EventTypeMap,
      parameters
    );

    // Set Clarity tags if provided
    if (clarityTags && Object.keys(clarityTags).length > 0) {
      this.setClarityTags(clarityTags);
    }
  }

  // ✅ Specific method for purchase tracking
  trackPurchase(purchaseData: PurchaseEventParams): void {
    this.trackEvent('purchase', purchaseData);
  }

  // ✅ Specific method for item list viewing
  trackViewItemList(listData: ViewItemListEventParams): void {
    this.trackEvent('view_item_list', listData);
  }

  // ✅ Safe page view tracking
  trackPageView(path: string, title?: string): void {
    if (!this.isBrowser()) return;

    try {
      const gtag = getGlobalGtag();
      if (gtag && this.gaLoaded) {
        gtag('event', 'page_view', {
          page_path: path,
          page_title: title || document.title
        });
        console.log(`📊 Page view tracked: ${path}`);
      }
    } catch (error) {
      console.warn('Error tracking page view:', error);
    }
  }

  // ✅ Safe consent management
  updateConsent(consentParams: GtagConsentParams): void {
    if (!this.isBrowser()) return;

    try {
      const gtag = getGlobalGtag();
      if (gtag && this.gaLoaded) {
        gtag('consent', 'update', consentParams);
        console.log('📊 Consent updated:', consentParams);
      }
    } catch (error) {
      console.warn('Error updating consent:', error);
    }
  }

  // ✅ Safe user identification for Clarity
  identifyUser(userId: string, sessionId?: string): void {
    if (!this.isBrowser() || !this.clarityReady) return;

    try {
      const clarity = getGlobalClarity();
      if (clarity && typeof clarity.identify === 'function') {
        clarity.identify(userId, sessionId);
        console.log(`🔍 User identified: ${userId}`);
      }
    } catch (error) {
      console.warn('Error identifying user:', error);
    }
  }

  // ✅ Check loading status
  isGAReady(): boolean {
    return this.gaLoaded && this.isBrowser() && !!getGlobalGtag();
  }

  isClarityReady(): boolean {
    return (
      this.clarityLoaded &&
      this.clarityReady &&
      this.isBrowser() &&
      !!getGlobalClarity()
    );
  }

  // ✅ Force check Clarity readiness (useful for debugging)
  async recheckClarityReadiness(): Promise<boolean> {
    if (!this.isBrowser()) return false;

    this.clarityRetryCount = 0;
    const isReady = await this.waitForClarityReady();
    return isReady;
  }
}

// ✅ Export singleton instance
export const analyticsManager = AnalyticsManager.getInstance();

// ✅ Enhanced hooks interface with new methods
export interface UseAnalyticsReturn {
  trackEvent: TrackEventFunction;
  trackCustomEvent: (
    eventName: string,
    parameters?: CustomEventParams,
    clarityTags?: Record<string, string>
  ) => void;
  trackPageView: (path: string, title?: string) => void;
  trackPurchase: (purchaseData: PurchaseEventParams) => void;
  trackViewItemList: (listData: ViewItemListEventParams) => void;
  updateConsent: (consentParams: GtagConsentParams) => void;
  identifyUser: (userId: string, sessionId?: string) => void;
  setClarityTags: (tags: Record<string, string>) => void;
  isGAReady: () => boolean;
  isClarityReady: () => boolean;
  recheckClarityReadiness: () => Promise<boolean>;
}

export function useAnalytics(): UseAnalyticsReturn {
  return {
    trackEvent: analyticsManager.trackEvent.bind(analyticsManager),
    trackCustomEvent: analyticsManager.trackCustomEvent.bind(analyticsManager),
    trackPageView: analyticsManager.trackPageView.bind(analyticsManager),
    trackPurchase: analyticsManager.trackPurchase.bind(analyticsManager),
    trackViewItemList:
      analyticsManager.trackViewItemList.bind(analyticsManager),
    updateConsent: analyticsManager.updateConsent.bind(analyticsManager),
    identifyUser: analyticsManager.identifyUser.bind(analyticsManager),
    setClarityTags: analyticsManager.setClarityTags.bind(analyticsManager),
    isGAReady: analyticsManager.isGAReady.bind(analyticsManager),
    isClarityReady: analyticsManager.isClarityReady.bind(analyticsManager),
    recheckClarityReadiness:
      analyticsManager.recheckClarityReadiness.bind(analyticsManager)
  };
}

// ✅ Export types for use in other components
export type { GtagConfig, GtagConsentParams };
export type {
  PurchaseEventParams,
  ViewItemListEventParams,
  CustomEventParams
} from '@/types/events';
