// tailwind.config.ts - OPTIMIZED VERSION WITH BLOB ANIMATIONS
import type { Config } from 'tailwindcss';
import tailwindcssAnimate from 'tailwindcss-animate';
import typography from '@tailwindcss/typography';
import aspectRatio from '@tailwindcss/aspect-ratio';
import plugin from 'tailwindcss/plugin';

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    // Exclude node_modules explicitly for better purging
    '!./node_modules/**/*'
  ],

  // ✅ CRITICAL: Remove the massive safelist - let Tailwind purge unused CSS
  // Only keep truly dynamic classes that can't be detected
  safelist: [
    // Only classes that are dynamically generated and can't be detected
    'animate-pulse', // Used in loading states
    'text-shadow-yellow', // Custom utility
    'animate-blob', // Add blob animation to safelist since it's used with inline styles
    'animate-blob-roam', // New roaming animation
    'animate-blob-wander', // New wandering animation
    'animate-blob-pulse' // New pulsing animation
    // Remove all the other classes - they should be detected automatically
  ],

  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: '1rem',
        sm: '2rem',
        lg: '4rem',
        xl: '5rem',
        '2xl': '6rem'
      },
      screens: { '2xl': '1400px' }
    },

    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          foreground: 'hsl(var(--success-foreground))'
        },
        failure: {
          DEFAULT: 'hsl(var(--failure))',
          foreground: 'hsl(var(--failure-foreground))'
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',

        // Simplified hero colors
        hero: {
          bg: '#0b1624',
          yellow: '#F6A03C',
          'yellow-light': '#f7ab4f'
        },

        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))'
        }
      },

      fontFamily: {
        sans: ['var(--font-sans)', 'system-ui', 'sans-serif'],
        // ✅ Remove unused font families to reduce CSS
        headline: [
          'var(--font-montserrat)',
          'Impact',
          'Arial Black',
          'sans-serif'
        ]
      },

      keyframes: {
        // ✅ LARGE MOVEMENTS & DRAMATIC SIZE CHANGES - But very slow
        blob: {
          '0%': {
            transform: 'translate(0vw, 0vh) scale(1)'
          },
          '20%': {
            transform: 'translate(25vw, -20vh) scale(1.6)'
          },
          '40%': {
            transform: 'translate(-15vw, 30vh) scale(0.4)'
          },
          '60%': {
            transform: 'translate(35vw, 10vh) scale(1.4)'
          },
          '80%': {
            transform: 'translate(-20vw, -15vh) scale(0.6)'
          },
          '100%': {
            transform: 'translate(0vw, 0vh) scale(1)'
          }
        },
        'blob-roam': {
          '0%': {
            transform: 'translate(0vw, 0vh) scale(1) rotate(0deg)'
          },
          '16.66%': {
            transform: 'translate(30vw, -25vh) scale(1.8) rotate(60deg)'
          },
          '33.33%': {
            transform: 'translate(-25vw, 35vh) scale(0.3) rotate(120deg)'
          },
          '50%': {
            transform: 'translate(40vw, 15vh) scale(1.5) rotate(180deg)'
          },
          '66.66%': {
            transform: 'translate(-30vw, -20vh) scale(0.5) rotate(240deg)'
          },
          '83.33%': {
            transform: 'translate(15vw, 25vh) scale(1.3) rotate(300deg)'
          },
          '100%': {
            transform: 'translate(0vw, 0vh) scale(1) rotate(360deg)'
          }
        },
        'blob-wander': {
          '0%': {
            transform: 'translate(0vw, 0vh) scale(1)'
          },
          '14.28%': {
            transform: 'translate(35vw, -30vh) scale(1.7)'
          },
          '28.56%': {
            transform: 'translate(-25vw, 40vh) scale(0.4)'
          },
          '42.84%': {
            transform: 'translate(30vw, 20vh) scale(1.5)'
          },
          '57.12%': {
            transform: 'translate(-35vw, -25vh) scale(0.5)'
          },
          '71.4%': {
            transform: 'translate(20vw, 35vh) scale(1.6)'
          },
          '85.68%': {
            transform: 'translate(-15vw, -20vh) scale(0.7)'
          },
          '100%': {
            transform: 'translate(0vw, 0vh) scale(1)'
          }
        },
        'blob-pulse': {
          '0%': {
            transform: 'translate(0vw, 0vh) scale(1) rotate(0deg)'
          },
          '12.5%': {
            transform: 'translate(25vw, -35vh) scale(1.9) rotate(45deg)'
          },
          '25%': {
            transform: 'translate(-30vw, 25vh) scale(0.3) rotate(90deg)'
          },
          '37.5%': {
            transform: 'translate(40vw, 30vh) scale(1.6) rotate(135deg)'
          },
          '50%': {
            transform: 'translate(-20vw, -30vh) scale(0.4) rotate(180deg)'
          },
          '62.5%': {
            transform: 'translate(30vw, 20vh) scale(1.7) rotate(225deg)'
          },
          '75%': {
            transform: 'translate(-35vw, 15vh) scale(0.5) rotate(270deg)'
          },
          '87.5%': {
            transform: 'translate(15vw, -25vh) scale(1.4) rotate(315deg)'
          },
          '100%': {
            transform: 'translate(0vw, 0vh) scale(1) rotate(360deg)'
          }
        },
        // ✅ Add missing keyframes that are used
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' }
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' }
        },
        'fade-in': {
          from: { opacity: '0' },
          to: { opacity: '1' }
        },
        'fade-out': {
          from: { opacity: '1' },
          to: { opacity: '0' }
        }
      },

      animation: {
        'accordion-down': 'accordion-down 2s ease-out',
        'accordion-up': 'accordion-up 2s ease-out',
        'fade-in': 'fade-in 2s ease-out',
        'fade-out': 'fade-out 2s ease-in',
        // ✅ MUCH FASTER BLOB ANIMATIONS - Easy to see movement
        blob: 'blob 18s infinite ease-in-out',
        'blob-roam': 'blob-roam 100s infinite ease-in-out',
        'blob-wander': 'blob-wander 120s infinite ease-in-out',
        'blob-pulse': 'blob-pulse 150s infinite ease-in-out'
      },

      textShadow: {
        yellow: '0 0 8px rgba(246,160,60,0.6)',
        sm: '0 1px 2px rgba(0,0,0,0.2)'
      }
    }
  },

  plugins: [
    tailwindcssAnimate,
    typography,
    aspectRatio,

    plugin(({ matchUtilities, theme }) => {
      matchUtilities(
        {
          'text-shadow': (value) => ({ textShadow: value })
        },
        { values: theme('textShadow') }
      );
    })
  ]
};

export default config;
