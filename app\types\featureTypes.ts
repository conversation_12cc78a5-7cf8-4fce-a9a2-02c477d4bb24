// Feature type definitions
export type FeatureType =
  | 'ats'
  | 'skillGap'
  | 'skillsGap'
  | 'careerMatching'
  | 'mockInterview'
  | 'learningResources'
  | 'marketTrends'
  | 'resumeImprovement'
  | 'coverLetter'
  | 'careerCoach'
  | 'recruitmentAgencies'; // Make sure this matches exactly

// Alias for FeatureType for backward compatibility
export type FeatureId =
  | 'ats'
  | 'skillGap'
  | 'careerMatching'
  | 'mockInterview'
  | 'learningResources'
  | 'marketTrends'
  | 'resumeImprovement'
  | 'coverLetter'
  | 'careerCoach';

// Feature configuration interface
export interface FeatureConfig {
  type: FeatureType;
  title: string;
  description: string;
  icon: string;
  creditCost: number;
  requiresJob: boolean;
  requiresResume: boolean;
  availableInPlans: ('BASIC' | 'PRO' | 'ULTIMATE')[];
  gradient: string;
}

// Feature details for marketing/display purposes
export interface FeatureDetails {
  howItWorks: string;
  benefits: string[];
  keyPoints: string[];
}

// Feature card props for UI components
export interface FeatureCardProps {
  title: string;
  description: string;
  icon: string;
  details: FeatureDetails;
  hidden?: boolean;

  /**
   * slug — relative URL used by the marketing site, e.g. "ai-cv-builder".
   * public — true = show in public nav/footer & sitemap; false = internal-only.
   */
  slug?: string;
  public?: boolean;
}

// Feature mappings for different contexts
export const FEATURE_TO_SERVICE_TYPE = {
  ATS: 'ATS_ANALYSIS',
  'Skill Gap Analysis': 'SKILL_GAP_ANALYSIS',
  'AI Career Matching': 'CAREER_MATCHING',
  'Mock Interview': 'MOCK_INTERVIEW',
  'Learning Resources': 'LEARNING_RESOURCES',
  'Market Trends': 'MARKET_TRENDS',
  'AI CV Improvement': 'RESUME_IMPROVEMENT',
  'Cover Letter Generator': 'COVER_LETTER',
  'AI Career Coach': 'CAREER_COACHING',
  'Recruitment Agencies': 'RECRUITMENT_AGENCIES'
} as const;

export const FEATURE_TO_STORAGE_KEY = {
  ATS: 'SimpleATS',
  'Skill Gap Analysis': 'SkillsGap',
  'AI Career Matching': 'CareerMatching',
  'Mock Interview': 'MockInterview',
  'Learning Resources': 'LearningResources',
  'Market Trends': 'MarketTrends',
  'AI CV Improvement': 'CVImprovement',
  'Cover Letter Generator': 'CoverLetter',
  'AI Career Coach': 'CareerCoach',
  'Recruitment Agencies': 'RecruitmentAgencies'
} as const;

export const FEATURE_TO_PROGRESS_KEY = {
  ATS: 'ats-analysis',
  'Skill Gap Analysis': 'skills-gap',
  'AI Career Matching': 'career-matching',
  'Mock Interview': 'mock-interview',
  'Learning Resources': 'learning-resources',
  'Market Trends': 'market-trends',
  'AI CV Improvement': 'CV-improvement',
  'Cover Letter Generator': 'cover-letter',
  'AI Career Coach': 'career-coach',
  'Recruitment Agencies': 'recruitment-agencies'
} as const;

// Feature configurations
export const FEATURE_CONFIGS: Record<FeatureType, FeatureConfig> = {
  ats: {
    type: 'ats',
    title: 'ATS Analysis',
    description: 'Analyze your CV against ATS systems',
    icon: 'search',
    creditCost: 10,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['BASIC', 'PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-blue-50 to-transparent'
  },
  skillGap: {
    type: 'skillGap',
    title: 'Skills Gap Analysis',
    description: 'Identify missing skills for your target role',
    icon: 'bar-chart',
    creditCost: 15,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-green-50 to-transparent'
  },
  skillsGap: {
    type: 'skillsGap',
    title: 'Skills Gap Analysis',
    description: 'Identify missing skills for your target role',
    icon: 'bar-chart',
    creditCost: 15,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-green-50 to-transparent'
  },
  careerMatching: {
    type: 'careerMatching',
    title: 'Career Matching',
    description: 'Find matching career opportunities',
    icon: 'briefcase',
    creditCost: 20,
    requiresJob: false,
    requiresResume: true,
    availableInPlans: ['ULTIMATE'],
    gradient: 'bg-gradient-to-r from-purple-50 to-transparent'
  },
  mockInterview: {
    type: 'mockInterview',
    title: 'Mock Interview',
    description: 'Practice interview questions',
    icon: 'message-square',
    creditCost: 15,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-yellow-50 to-transparent'
  },
  learningResources: {
    type: 'learningResources',
    title: 'Learning Resources',
    description: 'Resources to improve your skills',
    icon: 'book',
    creditCost: 15,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-indigo-50 to-transparent'
  },
  marketTrends: {
    type: 'marketTrends',
    title: 'Market Trends',
    description: 'Latest trends in your industry',
    icon: 'trending-up',
    creditCost: 20,
    requiresJob: true,
    requiresResume: false,
    availableInPlans: ['ULTIMATE'],
    gradient: 'bg-gradient-to-r from-red-50 to-transparent'
  },
  resumeImprovement: {
    type: 'resumeImprovement',
    title: 'CV Improvement',
    description: 'Get suggestions to improve your CV',
    icon: 'file-text',
    creditCost: 15,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-teal-50 to-transparent'
  },
  coverLetter: {
    type: 'coverLetter',
    title: 'Cover Letter',
    description: 'Generate a tailored cover letter',
    icon: 'file',
    creditCost: 10,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['BASIC', 'PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-orange-50 to-transparent'
  },
  careerCoach: {
    type: 'careerCoach',
    title: 'Career Coach',
    description: 'Get personalized career advice',
    icon: 'user',
    creditCost: 20,
    requiresJob: true,
    requiresResume: true,
    availableInPlans: ['ULTIMATE'],
    gradient: 'bg-gradient-to-r from-pink-50 to-transparent'
  },
  recruitmentAgencies: {
    type: 'recruitmentAgencies',
    title: 'Recruitment Agencies',
    description: 'Find recruitment agencies that match your profile',
    icon: 'building',
    creditCost: 10,
    requiresJob: false,
    requiresResume: false,
    availableInPlans: ['BASIC', 'PRO', 'ULTIMATE'],
    gradient: 'bg-gradient-to-r from-cyan-50 to-transparent'
  }
};
