// Create: components/analytics/SignupConversionTracker.tsx
'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

export function SignupConversionTracker() {
  const searchParams = useSearchParams();

  useEffect(() => {
    const trackSignupConversion = () => {
      // Method 1: Check URL parameters
      const signupParam = searchParams.get('signup');
      const statusParam = searchParams.get('status');

      // Method 2: Check cookie
      const cookies = document.cookie.split(';');
      const signupCookie = cookies.find((cookie) =>
        cookie.trim().startsWith('signup_success=true')
      );

      const shouldTrack =
        (signupParam === 'true' && statusParam === 'Success!') || signupCookie;

      if (shouldTrack && typeof window !== 'undefined' && window.gtag) {
        console.log('🎯 Tracking signup conversion');

        // Track GA4 event for Google Ads to import
        window.gtag('event', 'sign_up', {
          method: 'email',
          value: 5,
          currency: 'GBP',
          user_type: 'free_trial'
        });

        // Also track as custom conversion event
        window.gtag('event', 'free_trial_signup', {
          method: 'email',
          value: 5,
          currency: 'GBP'
        });

        console.log('✅ Signup conversion tracked successfully');

        // Clear the cookie to prevent duplicate tracking
        if (signupCookie) {
          document.cookie =
            'signup_success=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
          console.log('🧹 Signup tracking cookie cleared');
        }

        // Optional: Clear URL params to clean up URL
        if (signupParam && window.history.replaceState) {
          const url = new URL(window.location.href);
          url.searchParams.delete('signup');
          window.history.replaceState({}, '', url.toString());
        }
      }
    };

    // Track immediately if already loaded
    trackSignupConversion();

    // Also track after a small delay to ensure gtag is ready
    const timeout = setTimeout(trackSignupConversion, 1000);

    return () => clearTimeout(timeout);
  }, [searchParams]);

  return null; // This component doesn't render anything
}

// Alternative: Simple URL-based tracker
export function SimpleSignupTracker() {
  const searchParams = useSearchParams();

  useEffect(() => {
    const signup = searchParams.get('signup');
    const status = searchParams.get('status');

    if (
      signup === 'true' &&
      status === 'Success!' &&
      typeof window !== 'undefined' &&
      window.gtag
    ) {
      console.log('🎯 Simple signup tracking fired');

      window.gtag('event', 'sign_up', {
        method: 'email',
        value: 5,
        currency: 'GBP'
      });

      console.log('✅ Simple signup tracked');
    }
  }, [searchParams]);

  return null;
}
