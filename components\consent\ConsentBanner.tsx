'use client';

import React, { useState } from 'react';
import { useConsent } from '@/hooks/useConsentManager';

export function ConsentBanner() {
  const {
    isConsentBannerVisible,
    acceptAllConsent,
    rejectAllConsent,
    saveConsent
  } = useConsent();

  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState({
    analytics: false,
    advertising: false,
    functional: true
  });

  if (!isConsentBannerVisible) return null;

  const handleCustomConsent = () => {
    saveConsent(preferences);
  };

  return (
    <div
      className="fixed bottom-0 left-0 right-0 z-[9999] p-4 md:p-6"
      style={{
        background: 'rgba(1, 6, 11, 0.95)',
        backdropFilter: 'blur(10px)',
        borderTop: '1px solid rgba(255, 255, 255, 0.1)'
      }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-2">
              We value your privacy
            </h3>
            <p className="text-sm text-gray-300 mb-4">
              JobSpace AI uses cookies to enhance your job search experience,
              analyze platform usage, and provide personalized job
              recommendations. Choose your preferences below.
            </p>

            {showDetails && (
              <div
                className="space-y-3 mb-4 p-4 rounded-lg"
                style={{ background: 'rgba(255, 255, 255, 0.05)' }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-white">
                      Essential Cookies
                    </label>
                    <p className="text-xs text-gray-400">
                      Required for JobSpace AI to function properly
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={true}
                    disabled
                    className="rounded border-gray-600 bg-gray-700 text-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-white">
                      Analytics Cookies
                    </label>
                    <p className="text-xs text-gray-400">
                      Help us improve job matching algorithms and user
                      experience
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.analytics}
                    onChange={(e) =>
                      setPreferences((prev) => ({
                        ...prev,
                        analytics: e.target.checked
                      }))
                    }
                    className="rounded border-gray-600 bg-gray-700 text-blue-500"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-white">
                      Marketing Cookies
                    </label>
                    <p className="text-xs text-gray-400">
                      Enable personalized job recommendations and relevant
                      career content
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.advertising}
                    onChange={(e) =>
                      setPreferences((prev) => ({
                        ...prev,
                        advertising: e.target.checked
                      }))
                    }
                    className="rounded border-gray-600 bg-gray-700 text-blue-500"
                  />
                </div>
              </div>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="px-4 py-2 text-sm text-gray-300 hover:text-white underline"
            >
              {showDetails ? 'Hide Options' : 'Customize'}
            </button>

            <button
              onClick={rejectAllConsent}
              className="px-4 py-2 text-sm border border-gray-600 rounded-md hover:bg-gray-800 text-white transition-colors"
            >
              Reject All
            </button>

            {showDetails && (
              <button
                onClick={handleCustomConsent}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Save Preferences
              </button>
            )}

            <button
              onClick={acceptAllConsent}
              className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Accept All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
