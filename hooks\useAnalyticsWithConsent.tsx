// hooks/useAnalyticsWithConsent.tsx
'use client';

import { useAnalytics } from '@/lib/analytics-manager';
import { useConsent } from '@/hooks/useConsentManager';
import { useCallback } from 'react';
import type { CustomEventParams } from '@/types/events';

/**
 * A compatibility hook that combines your analytics manager
 * with your existing consent manager
 */
export function useAnalyticsWithConsent() {
  const analytics = useAnalytics();
  const { consentState } = useConsent();

  // ✅ Consent-aware event tracking
  const trackEvent = useCallback(
    (eventName: string, parameters?: Record<string, unknown>) => {
      if (!consentState?.analytics || !consentState?.hasConsented) {
        console.log(
          `🚫 Analytics tracking blocked - no consent for: ${eventName}`
        );
        return;
      }

      try {
        // Use the properly typed trackCustomEvent instead of the generic trackEvent
        analytics.trackCustomEvent(eventName, parameters as CustomEventParams);
      } catch (error) {
        console.warn('Analytics tracking error:', error);
      }
    },
    [analytics, consentState?.analytics, consentState?.hasConsented]
  );

  // ✅ Consent-aware custom event tracking
  const trackCustomEvent = useCallback(
    (
      eventName: string,
      parameters?: CustomEventParams,
      clarityTags?: Record<string, string>
    ) => {
      if (!consentState?.analytics || !consentState?.hasConsented) {
        console.log(
          `🚫 Custom event tracking blocked - no consent for: ${eventName}`
        );
        return;
      }

      try {
        analytics.trackCustomEvent(eventName, parameters, clarityTags);
      } catch (error) {
        console.warn('Custom event tracking error:', error);
      }
    },
    [analytics, consentState?.analytics, consentState?.hasConsented]
  );

  // ✅ Consent-aware page view tracking
  const trackPageView = useCallback(
    (path: string, title?: string) => {
      if (!consentState?.analytics || !consentState?.hasConsented) {
        console.log(`🚫 Page view tracking blocked - no consent for: ${path}`);
        return;
      }

      try {
        analytics.trackPageView(path, title);
      } catch (error) {
        console.warn('Page view tracking error:', error);
      }
    },
    [analytics, consentState?.analytics, consentState?.hasConsented]
  );

  // ✅ Consent-aware Clarity tags
  const setClarityTags = useCallback(
    (tags: Record<string, string>) => {
      if (!consentState?.analytics || !consentState?.hasConsented) {
        console.log('🚫 Clarity tags blocked - no consent');
        return;
      }

      try {
        analytics.setClarityTags(tags);
      } catch (error) {
        console.warn('Clarity tags error:', error);
      }
    },
    [analytics, consentState?.analytics, consentState?.hasConsented]
  );

  // ✅ Status checks
  const isAnalyticsAllowed = useCallback(() => {
    return !!(consentState?.analytics && consentState?.hasConsented);
  }, [consentState?.analytics, consentState?.hasConsented]);

  const isReady = useCallback(() => {
    return (
      isAnalyticsAllowed() &&
      (analytics.isGAReady() || analytics.isClarityReady())
    );
  }, [isAnalyticsAllowed, analytics]);

  return {
    // Consent-aware methods
    trackEvent,
    trackCustomEvent,
    trackPageView,
    setClarityTags,

    // Direct analytics methods (will be blocked if no consent)
    trackPurchase: analytics.trackPurchase,
    trackViewItemList: analytics.trackViewItemList,
    updateConsent: analytics.updateConsent,
    identifyUser: analytics.identifyUser,

    // Status methods
    isAnalyticsAllowed,
    isReady,
    isGAReady: analytics.isGAReady,
    isClarityReady: analytics.isClarityReady,
    recheckClarityReadiness: analytics.recheckClarityReadiness,

    // Consent state for components
    consentState,
    hasAnalyticsConsent: isAnalyticsAllowed()
  };
}
