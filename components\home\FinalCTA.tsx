// components/home/<USER>
'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/app/utils/supabase/client';
import { HybridButton } from '@/components/ui/themed-button';
import SectionHeading, { Highlight } from '../ui/section-heading';
import { trackCTAClick, trackUserInteraction } from '@/lib/ga-events';

export const FinalCTASection = () => {
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();

    const getUser = async () => {
      try {
        setIsLoading(true);
        const {
          data: { user }
        } = await supabase.auth.getUser();
        setUserId(user?.id || null);
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    getUser();
  }, []);

  return (
    <section
      className="relative isolate max-w-4xl mx-auto py-16 overflow-hidden scroll-mt-16"
      id="get-started"
    >
      <div className="relative z-10 mx-auto w-full px-4 animate-in fade-in duration-700">
        <div className=" text-center">
          <SectionHeading className="text-center ">
            Ready to <Highlight> Transform </Highlight>Your Career?
          </SectionHeading>
          <p className="text-xl mx-auto mb-6 text-slate-200">
            Join thousands of professionals who have transformed their careers
            with Job Space AI. Sign up and be the next success story!
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <span className="backdrop-blur-sm bg-white/10 border border-white/20 px-4 py-2 rounded-full text-sm text-slate-200">
              AI CV Optimization
            </span>
            <span className="backdrop-blur-sm bg-white/10 border border-white/20 px-4 py-2 rounded-full text-sm text-slate-200">
              Skill Gap Analysis
            </span>
            <span className="backdrop-blur-sm bg-white/10 border border-white/20 px-4 py-2 rounded-full text-sm text-slate-200">
              Interview Preparation
            </span>
            <span className="backdrop-blur-sm bg-white/10 border border-white/20 px-4 py-2 rounded-full text-sm text-slate-200">
              ATS Compatibility
            </span>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <HybridButton
              href="/onboarding"
              variant="primary"
              size="lg"
              className="text-[16px] md:text-[18px] font-semibold"
              onClick={() => {
                trackCTAClick(
                  'get_personalized_insights',
                  'final_cta_section',
                  'Get My Personalized Career Insights'
                );
                trackUserInteraction({
                  interaction_type: 'cta_click',
                  element_type: 'final_cta_primary',
                  user_journey: 'marketing',
                  user_type: userId ? 'authenticated' : 'anonymous',
                  button_text: 'Get My Personalized Career Insights',
                  destination_url: '/onboarding'
                });
              }}
            >
              Get My Personalized Career Insights
            </HybridButton>

            <HybridButton
              href={userId ? `/dashboard/${userId}` : '/signin/password_signin'}
              variant="secondary"
              size="lg"
              className="text-[16px] md:text-[18px] font-semibold"
              onClick={() => {
                const buttonText = isLoading
                  ? 'Loading...'
                  : userId
                    ? 'View Dashboard'
                    : 'Sign In';
                const destination = userId
                  ? `/dashboard/${userId}`
                  : '/signin/password_signin';

                trackCTAClick(
                  userId ? 'view_dashboard' : 'sign_in',
                  'final_cta_section',
                  buttonText
                );
                trackUserInteraction({
                  interaction_type: 'cta_click',
                  element_type: 'final_cta_secondary',
                  user_journey: 'marketing',
                  user_type: userId ? 'authenticated' : 'anonymous',
                  button_text: buttonText,
                  destination_url: destination
                });
              }}
            >
              {isLoading ? 'Loading...' : userId ? 'View Dashboard' : 'Sign In'}
            </HybridButton>
          </div>
        </div>
      </div>
    </section>
  );
};
