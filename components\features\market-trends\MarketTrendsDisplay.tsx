'use client';
import React, { useState, useEffect } from 'react';
import { get } from '@/app/utils/get';
import {
  Job,
  MarketTrendsResult,
  SalaryTrend,
  JobGrowth,
  SkillDemand,
  RemoteWorkTrend
} from '@/app/types/globalTypes';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SalaryTrendsChart } from '@/components/market-trends/SalaryTrendsChart';
import { JobGrowthChart } from '@/components/market-trends/JobGrowthChart';
import { SkillDemandChart } from '@/components/market-trends/SkillDemandChart';
import { RemoteWorkChart } from '@/components/market-trends/RemoteWorkChart';

interface MarketTrendsDisplayProps {
  trends: MarketTrendsResult;
  job?: Job;
}

type TabKey = 'salaryTrends' | 'jobGrowth' | 'skillDemand' | 'remoteWork';

export const MarketTrendsDisplay: React.FC<MarketTrendsDisplayProps> = ({
  trends
}) => {
  const [activeTab, setActiveTab] = useState<
    'salaryTrends' | 'jobGrowth' | 'skillDemand' | 'remoteWork'
  >('salaryTrends');
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Force Recharts to recalculate when tab becomes visible
  useEffect(() => {
    // debounce a tick so the panel has time to become display:block
    const id = setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 50);
    return () => clearTimeout(id);
  }, [activeTab]);

  /** normalise once, strongly-typed */
  const salaryTrends =
    get<MarketTrendsResult, SalaryTrend[]>(
      trends,
      'salaryTrends',
      'salary_trends'
    ) ?? [];

  const jobGrowth =
    get<MarketTrendsResult, JobGrowth[]>(trends, 'jobGrowth', 'job_growth') ??
    [];

  const skillDemand =
    get<MarketTrendsResult, SkillDemand[]>(
      trends,
      'skillDemand',
      'skill_demand'
    ) ?? [];

  const remoteWorkTrends =
    get<MarketTrendsResult, RemoteWorkTrend[]>(
      trends,
      'remoteWorkTrends',
      'remote_work_trends'
    ) ?? [];

  if (!trends) return <div>No market trends data available</div>;

  return (
    <div className="rounded-lg border backdrop-blur-sm bg-white/10 border-white/20 shadow-lg overflow-hidden mb-8">
      {/* Header with gradient */}
      <div className="p-6 pb-4 flex flex-row items-center justify-between border-b bg-gradient-to-r from-red-500/20 to-transparent">
        <div>
          <h2 className="text-2xl font-semibold text-white">
            Market Trends Analysis
          </h2>
          <p className="text-sm text-slate-300 mt-1">
            Latest trends and insights for your industry
          </p>
        </div>
      </div>
      <div ref={containerRef}>
        <Tabs
          value={activeTab}
          onValueChange={(v: string) => setActiveTab(v as TabKey)}
        >
          <div className="border-b bg-white/5">
            <div className="px-6 pt-2 pb-0">
              <TabsList className="grid w-full grid-cols-3 h-12 p-1 bg-white/10">
                <TabsTrigger
                  value="salaryTrends"
                  className="flex items-center gap-1 py-2.5 data-[state=active]:bg-white/20 data-[state=active]:text-[hsl(var(--hero-yellow))] text-slate-300"
                >
                  <span>Salary Trends</span>
                </TabsTrigger>
                <TabsTrigger
                  value="skillDemand"
                  className="flex items-center gap-1 py-2.5 data-[state=active]:bg-white/20 data-[state=active]:text-[hsl(var(--hero-yellow))] text-slate-300"
                >
                  <span>Skills in Demand</span>
                </TabsTrigger>
                <TabsTrigger
                  value="remoteWork"
                  className="flex items-center gap-1 py-2.5 data-[state=active]:bg-white/20 data-[state=active]:text-[hsl(var(--hero-yellow))] text-slate-300"
                >
                  <span>Remote Work</span>
                </TabsTrigger>
              </TabsList>
            </div>
          </div>

          <TabsContent
            value="salaryTrends"
            className="p-6 space-y-6 backdrop-blur-sm bg-white/5"
          >
            {/* Salary Trends */}
            {salaryTrends.length > 0 && (
              <div className="border border-white/20 rounded-lg p-4 space-y-3 bg-white/5">
                <h4 className="text-md font-medium text-white">
                  Salary Trends
                </h4>
                <SalaryTrendsChart salaryTrends={salaryTrends} />
                <div className="overflow-x-auto mt-4">
                  <table className="min-w-full divide-y divide-white/10">
                    <thead>
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                          Month
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                          Tech
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                          Finance
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                          Healthcare
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-white/10">
                      {salaryTrends.map((trend, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 whitespace-nowrap text-slate-300">
                            {trend.month}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-slate-300">
                            ${trend.tech.toLocaleString()}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-slate-300">
                            ${trend.finance.toLocaleString()}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-slate-300">
                            ${trend.healthcare.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Job Growth */}
            {jobGrowth.length > 0 && (
              <div className="border border-white/20 rounded-lg p-4 space-y-3 bg-white/5">
                <h4 className="text-md font-medium text-white">Job Growth</h4>
                <JobGrowthChart jobGrowthData={jobGrowth} />
                <div className="space-y-2 mt-4">
                  {jobGrowth.map((growth, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center"
                    >
                      <span className="text-sm text-slate-300">
                        {growth.quarter}
                      </span>
                      <span
                        className={`text-sm ${
                          growth.growth > 0 ? 'text-green-400' : 'text-red-400'
                        }`}
                      >
                        {growth.growth > 0 ? '+' : ''}
                        {growth.growth}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Industry Breakdown */}
            {trends.industryBreakdown &&
              trends.industryBreakdown.length > 0 && (
                <div className="border border-white/20 rounded-lg p-4 space-y-3 bg-white/5">
                  <h4 className="text-md font-medium text-white">
                    Industry Breakdown
                  </h4>
                  <div className="space-y-2">
                    {trends.industryBreakdown.map((item, index) => (
                      <div
                        key={index}
                        className="flex justify-between items-center"
                      >
                        <span className="text-sm text-slate-300">
                          {item.industry}
                        </span>
                        <span className="text-sm text-slate-300">
                          {item.percentage}%
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </TabsContent>

          <TabsContent
            value="skillDemand"
            className="p-6 space-y-6 backdrop-blur-sm bg-white/5"
          >
            {/* Top Skills in Demand */}
            {skillDemand.length > 0 && (
              <div className="border border-white/20 rounded-lg p-4 space-y-3 bg-white/5">
                <h4 className="text-md font-medium text-white">
                  Top Skills in Demand
                </h4>
                <SkillDemandChart skillDemand={skillDemand} />
                <div className="flex flex-wrap gap-2 mt-4">
                  {skillDemand.map((skill, index) => (
                    <div
                      key={index}
                      className="text-sm bg-[hsl(var(--hero-yellow))]/20 text-[hsl(var(--hero-yellow))] px-2 py-1 rounded border border-[hsl(var(--hero-yellow))]/30"
                    >
                      {skill.skill} ({skill.count} mentions)
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent
            value="remoteWork"
            className="p-6 space-y-6 backdrop-blur-sm bg-white/5"
          >
            {/* Remote Work Trends */}
            {remoteWorkTrends.length > 0 && (
              <div className="border border-white/20 rounded-lg p-4 space-y-3 bg-white/5">
                <h4 className="text-md font-medium text-white">
                  Remote Work Trends
                </h4>
                <RemoteWorkChart remoteWorkTrends={remoteWorkTrends} />
                <div className="space-y-4 mt-4">
                  {remoteWorkTrends.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-white">
                          {item.type}
                        </span>
                        <span className="text-sm text-slate-300">
                          {item.percentage}% of jobs
                        </span>
                      </div>
                      <div className="text-sm text-slate-300">
                        Average Salary: ${item.averageSalary.toLocaleString()}
                      </div>

                      {item.industryBreakdown &&
                        item.industryBreakdown.length > 0 && (
                          <div className="mt-2">
                            <div className="text-xs text-slate-400 mb-1">
                              Industry Breakdown:
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                              {item.industryBreakdown.map((industry, i) => (
                                <div key={i} className="text-xs text-slate-300">
                                  {industry.industry}: {industry.percentage}%
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
