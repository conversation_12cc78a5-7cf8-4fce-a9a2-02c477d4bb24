import { useState, useCallback, useEffect } from 'react';

// Types for recognition events and objects
interface SpeechRecognitionResultItem {
  transcript: string;
  confidence: number;
}

interface SpeechRecognitionResultObject {
  0: SpeechRecognitionResultItem;
  isFinal: boolean;
  length: number;
}

interface SpeechRecognitionEventObject {
  results: {
    [index: number]: SpeechRecognitionResultObject;
    length: number;
  };
}

interface SpeechRecognitionErrorObject {
  error: string;
}

// Define a type for the Speech Recognition instance
interface SpeechRecognitionInstance {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onresult: (event: SpeechRecognitionEventObject) => void;
  onerror: (event: SpeechRecognitionErrorObject) => void;
  onend: () => void;
  onstart: () => void;
}

// We won't try to extend the Window interface to avoid conflicts
// Instead, we'll just use type assertions when needed

// Extend the window interface with only our custom functions
declare global {
  interface Window {
    startListeningFn?: () => void;
    stopListeningFn?: () => void;
  }
}

// Helper function for question phrases
export const getQuestionPhrase = (index: number, total: number): string => {
  if (index === 0) {
    return "Let's begin with your first question. ";
  } else if (index === total - 1) {
    return 'For our final question. ';
  } else {
    const phrases = [
      'Moving on to the next question. ',
      'For our next question. ',
      "Let's continue with this question. "
    ];
    return phrases[index % phrases.length];
  }
};

// Define the return type for the hook
interface UseSpeechReturn {
  isVoiceSupported: boolean;
  isListening: boolean;
  transcript: string;
  toggleListening: () => void;
  updateTranscript: (text: string) => void;
  clearTranscript: () => void;
  startListening: () => void;
  stopListening: () => void;
  isSpeechAvailable: boolean;
  isSpeaking: boolean;
  isProcessing: boolean;
  voices: SpeechSynthesisVoice[];
  speakSimple: (text: string) => Promise<boolean>;
  speakQuestion: (
    questionText: string,
    index?: number,
    total?: number
  ) => Promise<boolean>;
  speakIntroduction: () => Promise<boolean>;
  speakFeedback: (summary: string, score: number) => Promise<boolean>;
  stopAllSpeech: () => void;
  setIsSpeaking: (state: boolean) => void;
  setIsListening: (state: boolean) => void;
  setIsProcessing: (state: boolean) => void;
}

/**
 * Combined hook to provide all speech functionality (synthesis and recognition)
 */
const useSpeech = (): UseSpeechReturn => {
  // Speech state
  const [isListening, setIsListening] = useState<boolean>(false);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [transcript, setTranscript] = useState<string>('');
  const [voicesAvailable, setVoicesAvailable] = useState<
    SpeechSynthesisVoice[]
  >([]);

  // Safely check for speech synthesis availability
  const isSpeechAvailable: boolean =
    typeof window !== 'undefined' && 'speechSynthesis' in window;

  // Safely check for speech recognition support
  const checkVoiceSupport = useCallback((): boolean => {
    if (typeof window === 'undefined') return false;
    // Use feature detection without accessing the properties directly
    return 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
  }, []);

  const isVoiceSupported = checkVoiceSupport();

  // Load available voices on mount
  useEffect(() => {
    if (!isSpeechAvailable) return;

    const loadVoices = (): void => {
      try {
        const voices = window.speechSynthesis.getVoices();
        if (voices.length > 0) {
          setVoicesAvailable(voices);
        }
      } catch (error) {
        console.error('Error loading voices:', error);
      }
    };

    // Try to load voices immediately
    loadVoices();

    // Set up the voiceschanged event
    window.speechSynthesis.onvoiceschanged = loadVoices;

    // Chrome sometimes doesn't fire onvoiceschanged
    const chromeFix = setTimeout(loadVoices, 1000);

    return () => {
      window.speechSynthesis.onvoiceschanged = null;
      clearTimeout(chromeFix);
    };
  }, [isSpeechAvailable]);

  // Method to update transcript (for speech recognition)
  const updateTranscript = useCallback((text: string): void => {
    setTranscript(text);
  }, []);

  // Method to clear transcript
  const clearTranscript = useCallback((): void => {
    setTranscript('');
  }, []);

  // Toggle listening state
  const toggleListening = useCallback((): void => {
    setIsListening((prev) => !prev);
  }, []);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Reference to recognition instance
    let recognition: SpeechRecognitionInstance | null = null;

    // Helper function to get the speech recognition constructor
    const getSpeechRecognition = (): unknown => {
      // Try standard first, then prefixed
      if ('SpeechRecognition' in window) {
        return (
          window as unknown as {
            SpeechRecognition: unknown;
          }
        ).SpeechRecognition;
      }

      if ('webkitSpeechRecognition' in window) {
        return (
          window as unknown as {
            webkitSpeechRecognition: unknown;
          }
        ).webkitSpeechRecognition;
      }

      return null;
    };

    const SpeechRecognitionClass = getSpeechRecognition();

    if (SpeechRecognitionClass) {
      try {
        // Type assertion here is necessary
        recognition = new (SpeechRecognitionClass as {
          new (): SpeechRecognitionInstance;
        })();

        // Only configure if recognition was created successfully
        if (recognition) {
          recognition.continuous = true;
          recognition.interimResults = true;
          recognition.lang = 'en-US';

          recognition.onresult = (
            event: SpeechRecognitionEventObject
          ): void => {
            let finalTranscript = '';

            // Manually iterate through results
            for (let i = 0; i < event.results.length; i++) {
              const result = event.results[i];
              if (result && result[0]) {
                const transcript = result[0].transcript || '';

                if (result.isFinal) {
                  finalTranscript += transcript;
                } else {
                  // For non-final results, update with interim results
                  updateTranscript(transcript);
                }
              }
            }

            // If we have final transcripts, update with those
            if (finalTranscript) {
              updateTranscript(finalTranscript);
            }
          };

          recognition.onerror = (event: SpeechRecognitionErrorObject): void => {
            console.error('Speech recognition error:', event.error);
          };

          recognition.onend = (): void => {
            console.log('Speech recognition ended');
            setIsListening(false);
          };

          recognition.onstart = (): void => {
            console.log('Speech recognition started');
            setIsListening(true);
          };
        }
      } catch (err) {
        console.error(
          'Failed to initialize speech recognition:',
          err instanceof Error ? err.message : String(err)
        );
      }
    }

    // Start listening function
    const startListening = (): void => {
      if (recognition) {
        try {
          // Check if recognition is already active before starting
          if (!isListening) {
            recognition.start();
            setIsListening(true);
          } else {
            console.log(
              'Speech recognition is already active, not starting again'
            );
          }
        } catch (err) {
          console.error(
            'Failed to start recognition:',
            err instanceof Error ? err.message : String(err)
          );
          setIsListening(false);
        }
      } else {
        console.error('Speech recognition not initialized');
      }
    };

    // Stop listening function
    const stopListening = (): void => {
      if (recognition) {
        try {
          recognition.stop();
          setIsListening(false);
        } catch (err) {
          console.error(
            'Failed to stop recognition:',
            err instanceof Error ? err.message : String(err)
          );
        }
      }
    };

    // Update external functions to use our local ones
    window.startListeningFn = startListening;
    window.stopListeningFn = stopListening;

    return () => {
      if (recognition) {
        try {
          recognition.stop();
        } catch (err) {
          // Ignore errors from stopping recognition
          console.log(
            'Error stopping recognition during cleanup:',
            err instanceof Error ? err.message : String(err)
          );
        }
      }
    };
  }, [updateTranscript]);

  /**
   * Start listening for speech
   */
  const startListening = useCallback((): void => {
    if (window.startListeningFn) {
      window.startListeningFn();
    }
  }, []);

  /**
   * Stop listening for speech
   */
  const stopListening = useCallback((): void => {
    if (window.stopListeningFn) {
      window.stopListeningFn();
    }
  }, []);

  /**
   * Update processing state
   */
  const setProcessingState = useCallback((state: boolean): void => {
    setIsProcessing(state);
  }, []);

  // In useSpeech.ts, combined approach for speakSimple
  const speakSimple = useCallback(
    async (text: string, voiceName?: string): Promise<boolean> => {
      try {
        // Set speaking state
        setIsSpeaking(true);

        // Make sure listening is off while speaking
        setIsListening(false);

        // Cancel any existing speech
        if (window.speechSynthesis) {
          window.speechSynthesis.cancel();
        }

        // Delay for cleanup (shorter)
        await new Promise<void>((resolve) => setTimeout(resolve, 200));

        // Get the selected voice
        const voices = window.speechSynthesis.getVoices();
        let voice = voices.find((v) => v.name === voiceName);

        if (!voice) {
          voice =
            voices.find((v) => v.name === 'Google UK English Female') ||
            voices.find((v) => v.name.includes('Google')) ||
            voices[0];
        }

        // If no voice is available, just use timeout
        if (!voice) {
          console.error('No voices available');
          // Wait a moment to simulate speech
          await new Promise((resolve) => setTimeout(resolve, 2000));
          setIsSpeaking(false);
          return false;
        }

        console.log(`Using voice for speech: ${voice.name}`);

        // Simple timeout calculation (simpler is better)
        const wordsCount = text.split(/\s+/).length;
        const msPerWord = 400; // 400ms per word - conservative estimate
        const timeout = Math.max(2000, wordsCount * msPerWord);

        console.log(`Speech timeout: ${timeout}ms for ${wordsCount} words`);

        // Create utterance without any event handlers
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.voice = voice;
        utterance.rate = 0.9; // Slightly slower for better clarity
        utterance.volume = 1.0;

        // Start speech without attaching any event handlers
        window.speechSynthesis.speak(utterance);

        // Simple timeout approach - no events or complexities
        await new Promise((resolve) => setTimeout(resolve, timeout));

        // Ensure speech is properly canceled
        window.speechSynthesis.cancel();

        // Reset speaking state
        setIsSpeaking(false);

        return true;
      } catch (err) {
        console.error('Speech error:', err);
        setIsSpeaking(false);
        return false;
      }
    },
    [setIsSpeaking, setIsListening]
  );

  /**
   * Introduction speech
   */
  const speakIntroduction = useCallback(
    async (voiceName?: string): Promise<boolean> => {
      const introText =
        "Welcome to your mock interview. I'll ask you some questions related to the job position. Please respond as if you were in an actual interview.";
      return speakSimple(introText, voiceName);
    },
    [speakSimple]
  );

  /**
   * Question speech with context-aware phrases
   */
  const speakQuestion = useCallback(
    async (
      questionText: string,
      index: number = 0,
      total: number = 5,
      voiceName?: string
    ): Promise<boolean> => {
      const phrase = getQuestionPhrase(index, total);
      console.log(
        `Speaking question ${index + 1}/${total} with phrase: "${phrase}"`
      );
      const formattedQuestion = `${phrase}${questionText}`;
      return speakSimple(formattedQuestion, voiceName);
    },
    [speakSimple]
  );

  /**
   * Very basic feedback
   */
  const speakFeedback = useCallback(
    async (
      summary: string,
      score: number,
      voiceName?: string
    ): Promise<boolean> => {
      const feedbackText = `Thank you for completing this interview. Your score is ${score} out of 10.`;
      return speakSimple(feedbackText, voiceName);
    },
    [speakSimple]
  );

  /**
   * Stop all speech
   */
  const stopAllSpeech = useCallback((): void => {
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
    }
    setIsSpeaking(false);
    setProcessingState(false);
  }, [setProcessingState]);

  // Return everything needed by consumers of this hook
  return {
    // Recognition states and functions
    isVoiceSupported,
    isListening,
    transcript,
    toggleListening,
    updateTranscript,
    clearTranscript,
    startListening,
    stopListening,

    // Synthesis states and functions
    isSpeechAvailable,
    isSpeaking,
    isProcessing,
    voices: voicesAvailable,
    speakSimple,
    speakQuestion,
    speakIntroduction,
    speakFeedback,
    stopAllSpeech,

    // Expose the setter functions
    setIsSpeaking,
    setIsListening,
    setIsProcessing
  };
};

export default useSpeech;
