// app/cookies/page.tsx

import React from 'react';
import { Metadata } from 'next';
import { <PERSON>, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { <PERSON>ie, Shield, Globe, Bell, Sparkles } from 'lucide-react';
import Link from 'next/link';
import { constructMetadata } from '@/lib/seo-config';

export const metadata: Metadata = constructMetadata({
  title: 'Cookie Policy | Job Space AI',
  description: 'Learn about how Job Space AI uses cookies on our website.',
  path: '/cookies'
});

const PolicySection = ({
  icon,
  title,
  children
}: {
  icon: React.ReactNode;
  title: string;
  children: React.ReactNode;
}) => (
  <Card className="mb-6 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg text-white overflow-hidden">
    <CardHeader>
      <CardTitle className="flex items-center text-xl font-semibold text-white">
        {icon}
        <span className="ml-2">{title}</span>
      </CardTitle>
    </CardHeader>
    <CardContent className="text-slate-200">{children}</CardContent>
  </Card>
);

const CookiePolicyPage = () => {
  return (
    <div className="relative isolate min-h-screen overflow-hidden">
      {/* Background with animated blobs */}
      <div className="absolute inset-0 -z-10 pointer-events-none">
        {/* Subtle background features */}
        <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-50"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000 opacity-50"></div>
        <div className="absolute top-2/3 left-1/2 w-[550px] h-[550px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000 opacity-50"></div>
        <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000 opacity-50"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-16 max-w-[1200px]">
        <div className="text-center mb-12">
          <span className="text-yellow-300 font-semibold inline-flex items-center gap-2">
            <Sparkles className="w-4 h-4" /> LEGAL INFORMATION
          </span>
          <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
            Cookie{' '}
            <span className="text-yellow-400 text-shadow-yellow">Policy</span>
          </h1>
          <p className="text-slate-400 mb-8 text-center">
            Last Updated: April 10, 2025
          </p>
        </div>

        <PolicySection
          icon={<Cookie className="w-6 h-6" />}
          title="Introduction"
        >
          <p className="mb-4">
            This Cookie Policy explains how Job Space AI (&quot;we&quot;,
            &quot;us&quot;, or &quot;our&quot;), a trading name of NRB
            CONSULTING LTD (Company Number: 16375535), uses cookies and similar
            technologies on our website.
          </p>
          <p>
            By using our website, you consent to the use of cookies in
            accordance with this Cookie Policy. If you do not agree to our use
            of cookies, you should set your browser settings accordingly or not
            use our website.
          </p>
        </PolicySection>

        <PolicySection
          icon={<Globe className="w-6 h-6" />}
          title="What Are Cookies?"
        >
          <p className="mb-4">
            Cookies are small text files that are placed on your device when you
            visit a website. They are widely used to make websites work more
            efficiently and provide information to the website owners.
          </p>
          <p>
            Cookies serve various functions, including enabling certain
            features, remembering your preferences, and generally improving your
            browsing experience.
          </p>
        </PolicySection>

        <PolicySection
          icon={<Shield className="w-6 h-6" />}
          title="Types of Cookies We Use"
        >
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Essential Cookies</h3>
              <p>
                These cookies are necessary for the website to function
                properly. They enable core functionality such as security,
                network management, and account access.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Preference Cookies</h3>
              <p>
                These cookies enable our website to remember information that
                changes the way the website behaves or looks, such as your
                preferred language or the region you are in.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Analytics Cookies</h3>
              <p>
                These cookies help us understand how visitors interact with our
                website by collecting and reporting information anonymously.
                This helps us improve our website structure and content.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Marketing Cookies</h3>
              <p>
                These cookies are used to track visitors across websites. The
                intention is to display ads that are relevant and engaging for
                the individual user.
              </p>
            </div>
          </div>
        </PolicySection>

        <PolicySection
          icon={<Bell className="w-6 h-6" />}
          title="Specific Cookies We Use"
        >
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-white/20">
              <thead>
                <tr className="bg-white/10">
                  <th className="border border-white/20 px-4 py-2 text-left text-white">
                    Cookie Name
                  </th>
                  <th className="border border-white/20 px-4 py-2 text-left text-white">
                    Type
                  </th>
                  <th className="border border-white/20 px-4 py-2 text-left text-white">
                    Purpose
                  </th>
                  <th className="border border-white/20 px-4 py-2 text-left text-white">
                    Duration
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    sb-[project-ref]-auth-token
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Essential
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Stores authentication information for Supabase
                    authentication
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Session (expires when session ends)
                  </td>
                </tr>
                <tr>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    preferredSignInView
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Preference
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Remembers your preferred sign-in method (email or password)
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    30 days
                  </td>
                </tr>
                <tr>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    returnTo
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Essential
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Temporarily stores the page you were on before signing in to
                    redirect you back after authentication
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    5 minutes
                  </td>
                </tr>
                <tr>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    auth_success
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Essential
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Indicates successful authentication to help with redirect
                    flow
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    30 seconds
                  </td>
                </tr>
                <tr>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    _ga, _gid, _gat
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Analytics
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Google Analytics cookies used to distinguish users and
                    throttle request rate
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    _ga: 2 years, _gid: 24 hours, _gat: 1 minute
                  </td>
                </tr>
                <tr>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    fbp, fr
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Marketing
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Facebook cookies used for tracking and ad delivery
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    90 days
                  </td>
                </tr>
                <tr>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    li_gc, lidc, bcookie
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Marketing
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    LinkedIn cookies used for tracking and ad delivery
                  </td>
                  <td className="border border-white/20 px-4 py-2 text-slate-200">
                    Varies from 1 day to 2 years
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </PolicySection>

        <Accordion
          type="single"
          collapsible
          className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
        >
          <AccordionItem value="third-party" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              Third-Party Cookies
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              <p className="mb-4">
                Some cookies are placed by third parties on our website. These
                third parties include:
              </p>
              <ul className="list-disc pl-6 mb-4">
                <li>Google Analytics (for website analytics)</li>
                <li>Facebook (for marketing purposes)</li>
                <li>LinkedIn (for marketing purposes)</li>
              </ul>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="management" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              How to Manage Cookies
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              <p className="mb-4">
                Most web browsers allow you to manage your cookie preferences.
                You can:
              </p>
              <ul className="list-disc pl-6 mb-4">
                <li>Delete cookies from your device</li>
                <li>Block cookies by activating browser settings</li>
                <li>Set your browser to notify you when receiving cookies</li>
              </ul>
              <div className="space-y-2">
                <p className="font-medium">Browser-specific instructions:</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <a
                    href="https://support.google.com/chrome/answer/95647"
                    className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Google Chrome
                  </a>
                  <a
                    href="https://support.microsoft.com/en-us/microsoft-edge/delete-cookies-in-microsoft-edge-63947406-40ac-c3b8-57b9-2a946a29ae09"
                    className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Microsoft Edge
                  </a>
                  <a
                    href="https://support.mozilla.org/en-US/kb/clear-cookies-and-site-data-firefox"
                    className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Mozilla Firefox
                  </a>
                  <a
                    href="https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471/mac"
                    className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Apple Safari
                  </a>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="contact" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              Contact Us
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              <p className="mb-4">
                If you have any questions about our use of cookies, please
                contact us at:
              </p>
              <div className="space-y-2">
                <p>
                  Email:{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <Card className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg text-white overflow-hidden">
          <CardContent className="pt-6">
            <p className="text-slate-200">
              For more information about how we process your personal data,
              please refer to our{' '}
              <Link
                href="/privacy"
                className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
              >
                Privacy Policy
              </Link>
              .
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CookiePolicyPage;
