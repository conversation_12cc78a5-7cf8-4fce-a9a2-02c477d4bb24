// components/analytics/AnalyticsProvider.tsx - Single analytics provider
'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode
} from 'react';
import Script from 'next/script';
import { GA4_CUSTOM_MAP } from '@/lib/custom-dimension-map';

// ✅ Use your existing types
interface TrackingParameters {
  [key: string]: string | number | boolean | null | undefined;
}

// ✅ Extend your existing Window interface
declare global {
  interface Window {
    clarity?: {
      (command: 'setTag', key: string, value: string): void;
      (command: 'event', eventName: string): void;
      q?: Array<unknown>;
    };
  }
}

interface AnalyticsContextType {
  isLoaded: boolean;
  trackEvent: (eventName: string, parameters?: TrackingParameters) => void;
  trackPageView: (path?: string) => void;
  trackConversion: (eventName: string, value?: number) => void;
  isClarityReady: () => boolean;
  setUserProperties: (
    userId: string,
    userType: string,
    planType?: string
  ) => void;
  trackScrollDepth: (depthPercent: number, pageName: string) => void;
}

const AnalyticsContext = createContext<AnalyticsContextType>({
  isLoaded: false,
  trackEvent: () => {},
  trackPageView: () => {},
  trackConversion: () => {},
  isClarityReady: () => false,
  setUserProperties: () => {},
  trackScrollDepth: () => {}
});

interface AnalyticsProviderProps {
  children: ReactNode;
  gaId?: string;
  clarityId?: string;
  enabled?: boolean;
}

export function AnalyticsProvider({
  children,
  gaId,
  clarityId,
  enabled = true
}: AnalyticsProviderProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(false);
  const [eventQueue, setEventQueue] = useState<Array<() => void>>([]);

  // Progressive loading logic
  useEffect(() => {
    if (!enabled || (!gaId && !clarityId)) return;

    let hasInteracted = false;

    const loadAnalytics = () => {
      if (!hasInteracted) {
        hasInteracted = true;
        setShouldLoad(true);
        console.log('🚀 Analytics loading triggered');
      }
    };

    // Load after user interaction OR 3 seconds
    const timer = setTimeout(loadAnalytics, 3000);

    const events = ['scroll', 'mousedown', 'touchstart', 'keydown'];
    const handlers = events.map((event) => {
      const handler = () => {
        clearTimeout(timer);
        loadAnalytics();
      };
      document.addEventListener(event, handler, { once: true, passive: true });
      return { event, handler };
    });

    return () => {
      clearTimeout(timer);
      handlers.forEach(({ event, handler }) => {
        document.removeEventListener(event, handler);
      });
    };
  }, [enabled, gaId, clarityId]);

  // Initialize analytics when loaded
  useEffect(() => {
    if (!shouldLoad) return;

    const initializeAnalytics = () => {
      // ✅ Safe dataLayer initialization using your existing Window interface
      if (typeof window !== 'undefined') {
        // Use your existing dataLayer type (unknown[])
        window.dataLayer = window.dataLayer || [];

        function gtag(...args: unknown[]) {
          window.dataLayer?.push(args);
        }

        // Set up consent defaults
        gtag('consent', 'default', {
          analytics_storage: 'denied',
          ad_storage: 'denied',
          ad_user_data: 'denied',
          ad_personalization: 'denied',
          functionality_storage: 'granted',
          security_storage: 'granted',
          wait_for_update: 2000
        });

        // Configure GA4 if available
        if (gaId) {
          gtag('js', new Date());
          gtag('config', gaId, {
            send_page_view: false, // Manual page view tracking
            cookie_flags: 'max-age=63072000;secure;samesite=lax',
            allow_google_signals: false,
            allow_ad_personalization_signals: false,
            // ✅ Add custom dimension mapping
            custom_map: GA4_CUSTOM_MAP
          });
        }

        // ✅ Use your existing gtag type
        window.gtag = gtag;
        setIsLoaded(true);

        // ✅ Process queued events and clear queue safely
        const currentQueue = eventQueue;
        setEventQueue([]); // Clear queue first
        currentQueue.forEach((fn) => fn()); // Then process events
      }
    };

    // Wait a bit for scripts to load
    const timer = setTimeout(initializeAnalytics, 500);
    return () => clearTimeout(timer);
  }, [shouldLoad, gaId]); // ✅ Remove eventQueue from dependencies

  const trackEvent = (
    eventName: string,
    parameters: TrackingParameters = {}
  ) => {
    const trackFn = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', eventName, {
          ...parameters,
          timestamp: Date.now(),
          page_path: window.location.pathname
        });
      }
    };

    if (isLoaded) {
      trackFn();
    } else {
      setEventQueue((prev) => [...prev, trackFn]);
    }
  };

  const trackPageView = (path?: string) => {
    const trackFn = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'page_view', {
          page_path: path || window.location.pathname,
          page_title: document.title,
          page_location: window.location.href
        });
      }
    };

    if (isLoaded) {
      trackFn();
    } else {
      setEventQueue((prev) => [...prev, trackFn]);
    }
  };

  const trackConversion = (eventName: string, value?: number) => {
    const trackFn = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', eventName, {
          value,
          currency: 'GBP',
          event_category: 'conversion'
        });
      }
    };

    if (isLoaded) {
      trackFn();
    } else {
      setEventQueue((prev) => [...prev, trackFn]);
    }
  };

  const setUserProperties = (
    userId: string,
    userType: string,
    planType?: string
  ) => {
    const trackFn = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('config', gaId!, {
          user_id: userId,
          user_properties: {
            user_type: userType,
            plan_type: planType || 'free'
          }
        });
      }
    };

    if (isLoaded) {
      trackFn();
    } else {
      setEventQueue((prev) => [...prev, trackFn]);
    }
  };

  const trackScrollDepth = (depthPercent: number, pageName: string) => {
    const trackFn = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'scroll_depth', {
          event_category: 'engagement',
          scroll_depth_percent: depthPercent,
          page_title: pageName
        });
      }
    };

    if (isLoaded) {
      trackFn();
    } else {
      setEventQueue((prev) => [...prev, trackFn]);
    }
  };

  const isClarityReady = (): boolean => {
    if (typeof window === 'undefined') return false;

    return !!(
      window.clarity &&
      typeof window.clarity === 'function' &&
      !('q' in window.clarity)
    ); // Check if it's not the stub
  };

  return (
    <AnalyticsContext.Provider
      value={{
        isLoaded,
        trackEvent,
        trackPageView,
        trackConversion,
        isClarityReady,
        setUserProperties,
        trackScrollDepth
      }}
    >
      {/* Load GTM Script */}
      {shouldLoad && gaId && (
        <Script
          id="gtag-script"
          src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
          strategy="afterInteractive"
          onLoad={() => console.log('✅ GTM loaded')}
          onError={() => console.warn('❌ GTM failed to load')}
        />
      )}

      {/* Load Clarity Script */}
      {shouldLoad && clarityId && (
        <Script
          id="clarity-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;
                t.src="https://www.clarity.ms/tag/"+i+"?ref="+encodeURIComponent(location.hostname);
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window,document,"clarity","script","${clarityId}");
            `
          }}
        />
      )}

      {children}
    </AnalyticsContext.Provider>
  );
}

// Hook to use analytics
export function useAnalytics() {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within AnalyticsProvider');
  }
  return context;
}

// Enhanced Performance Monitor
export function EnhancedPerformanceMonitor() {
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window))
      return;

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        // Track GTM performance
        if (
          entry.name.includes('googletagmanager') ||
          entry.name.includes('gtag')
        ) {
          console.log(`📊 ${entry.name}: ${entry.duration.toFixed(2)}ms`);

          // Track slow loading
          if (entry.duration > 1000 && window.gtag) {
            window.gtag('event', 'slow_script_load', {
              script_name: entry.name,
              duration: Math.round(entry.duration),
              event_category: 'performance'
            });
          }
        }

        // Track Clarity performance
        if (entry.name.includes('clarity.ms')) {
          console.log(`🔍 Clarity loaded: ${entry.duration.toFixed(2)}ms`);
        }
      }
    });

    observer.observe({ entryTypes: ['resource'] });
    return () => observer.disconnect();
  }, []);

  return process.env.NODE_ENV === 'development' ? (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-2 text-xs z-50 rounded opacity-50 hover:opacity-100">
      📊 Perf Monitor
    </div>
  ) : null;
}
