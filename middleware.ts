import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// In-memory rate limiting store (simple but effective for single instance)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Clean up old entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Clean every minute

function getRateLimitKey(request: NextRequest): string {
  // Get IP from headers (Cloud Run provides these)
  const ip =
    request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') || // Cloudflare
    'unknown';

  const userAgent = request.headers.get('user-agent') || 'unknown';

  // Combine IP and first part of user agent for rate limiting key
  return `${ip}-${userAgent.substring(0, 20)}`;
}

function isRateLimited(
  key: string,
  limit: number = 100,
  windowMs: number = 60000
): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // New window or expired record
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return false;
  }

  if (record.count >= limit) {
    return true;
  }

  record.count++;
  return false;
}

function isSuspiciousRequest(request: NextRequest): boolean {
  const path = request.nextUrl.pathname.toLowerCase();
  const userAgent = request.headers.get('user-agent')?.toLowerCase() || '';

  // Common attack patterns from your logs
  const suspiciousPatterns = [
    '/wp-admin',
    '/wp-content',
    '/wp-includes',
    '/.env',
    '/admin',
    '/phpinfo',
    '/php',
    '/backend',
    '/api/env',
    '/tmp/',
    '/stats/',
    '/local.env',
    '/videos/',
    '/htdocs/',
    '/horde/',
    '/fileserver/',
    '/facturacion/',
    '/.git',
    '/config',
    '/database',
    '/phpmyadmin',
    '/mysql',
    '/sql',
    '/xmlrpc.php',
    '/wp-login.php',
    '/.well-known/security.txt',
    '/robots.txt.backup',
    '/sitemap.xml.backup'
  ];

  // Check for suspicious paths
  if (suspiciousPatterns.some((pattern) => path.includes(pattern))) {
    return true;
  }

  // Check for suspicious user agents (common bot patterns)
  const suspiciousAgents = [
    'masscan',
    'nmap',
    'nikto',
    'sqlmap',
    'gobuster',
    'dirb',
    'dirbuster',
    'wpscan',
    'nuclei'
  ];

  if (suspiciousAgents.some((agent) => userAgent.includes(agent))) {
    return true;
  }

  // Check for multiple consecutive dots or suspicious file extensions
  if (path.includes('..') || path.match(/\.(php|asp|jsp|cgi)$/)) {
    return true;
  }

  return false;
}

function logSecurityEvent(
  request: NextRequest,
  event: string,
  details?: string
) {
  const ip =
    request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    'unknown';

  console.log(
    JSON.stringify({
      timestamp: new Date().toISOString(),
      event,
      ip,
      path: request.nextUrl.pathname,
      userAgent: request.headers.get('user-agent'),
      details,
      referer: request.headers.get('referer')
    })
  );
}

export async function middleware(request: NextRequest) {
  const startTime = Date.now();

  // Security checks first (before auth logic)
  const rateLimitKey = getRateLimitKey(request);

  // Rate limiting check
  if (isRateLimited(rateLimitKey, 150, 60000)) {
    // 150 requests per minute
    logSecurityEvent(request, 'RATE_LIMITED', `Key: ${rateLimitKey}`);
    return new NextResponse('Too Many Requests', {
      status: 429,
      headers: {
        'Retry-After': '60',
        'X-RateLimit-Limit': '150',
        'X-RateLimit-Remaining': '0'
      }
    });
  }

  // Suspicious request check
  if (isSuspiciousRequest(request)) {
    logSecurityEvent(request, 'SUSPICIOUS_REQUEST', 'Blocked attack pattern');

    // Apply stricter rate limiting for suspicious requests
    const suspiciousKey = `suspicious-${rateLimitKey}`;
    if (isRateLimited(suspiciousKey, 5, 300000)) {
      // 5 suspicious requests per 5 minutes
      logSecurityEvent(
        request,
        'SUSPICIOUS_RATE_LIMITED',
        'IP temporarily banned'
      );
      return new NextResponse('Forbidden', { status: 403 });
    }

    // Return 404 to not reveal the actual structure
    return new NextResponse('Not Found', { status: 404 });
  }

  // Your existing auth logic for protected routes
  const isProtectedRoute = request.nextUrl.pathname.startsWith(
    '/application-funnel'
  );

  if (isProtectedRoute) {
    const authCookies = [
      'sb-ylsypkzjfqwwaheobapi-auth-token.0',
      'sb-ylsypkzjfqwwaheobapi-auth-token.1'
    ];

    // Check if user has valid auth cookies (simple existence + length check)
    const hasAuth = authCookies.some((name) => {
      const cookie = request.cookies.get(name)?.value;
      return cookie && cookie.length > 10; // Basic validation
    });

    if (!hasAuth) {
      logSecurityEvent(request, 'AUTH_REDIRECT', 'No valid auth cookies');

      // Redirect to signin with next parameter preserved
      const signinUrl = new URL('/signin', request.url);
      signinUrl.searchParams.set(
        'next',
        request.nextUrl.pathname + request.nextUrl.search
      );
      return NextResponse.redirect(signinUrl);
    }
  }

  // Add security headers to all responses
  const response = NextResponse.next();

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // Optional: Add timing for monitoring
  const processingTime = Date.now() - startTime;
  response.headers.set('X-Processing-Time', processingTime.toString());

  return response;
}

export const config = {
  // Run on all routes for security, but auth logic only applies to protected routes
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)'
  ]
};
