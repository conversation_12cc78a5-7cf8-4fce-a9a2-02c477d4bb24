import React from 'react';
import { notFound } from 'next/navigation';
import { constructMetadata } from '@/lib/seo-config';
import { Metadata } from 'next';
import { articles } from '../articles-data';
import JsonLd from '@/components/seo/JsonLd';
import ArticleContent from '@/components/articles/ArticleContent';
export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const resolvedParams = await params;
  const article = articles.find((a) => a.slug === resolvedParams.slug);

  if (!article) {
    return constructMetadata({
      title: 'Article Not Found',
      description: 'The requested article could not be found.',
      noIndex: true
    });
  }

  // Note: openGraph is handled automatically by constructMetadata
  return constructMetadata({
    title: article.title,
    description: article.metaDescription,
    path: `/articles/${article.slug}`,
    image: article.schema.image || ''
  });
}

export default async function ArticlePage({
  params
}: {
  params: Promise<{ slug: string }>;
}) {
  // Resolve the params Promise
  const resolvedParams = await params;
  const slug = resolvedParams.slug;

  const article = articles.find((a) => a.slug === slug);

  if (!article) {
    notFound();
  }

  return (
    <>
      <ArticleContent article={article} />

      {/* --- BlogPosting JSON-LD --- */}
      <JsonLd
        id="blogposting-schema"
        data={{
          '@context': 'https://schema.org',
          '@type': 'BlogPosting',
          headline: article.title,
          description: article.metaDescription,
          datePublished: article.date,
            author: { '@type': 'Person', name: article.author },
            publisher: {
              '@type': 'Organization',
              name: 'JobSpaceAI',
              logo: {
                '@type': 'ImageObject',
                url: 'https://jobspaceai.com/logo-og.png'
              }
            },
            image: article.schema?.image
        }}
      />

      {/* --- CV Checker UK SoftwareApplication Schema --- */}
      <JsonLd
        id="cv-checker-schema"
        data={{
          '@context': 'https://schema.org',
          '@type': ['LocalBusiness', 'SoftwareApplication'],
          name: 'CV checker UK - JobSpaceAI',
          url: 'https://jobspaceai.com/ats-resume-checker',
          applicationCategory: 'BusinessApplication',
          operatingSystem: 'Any',
          offers: { '@type': 'Offer', price: '0', priceCurrency: 'GBP' },
          areaServed: { '@type': 'Country', name: 'United Kingdom' }
        }}
      />
    </>
  );
}
