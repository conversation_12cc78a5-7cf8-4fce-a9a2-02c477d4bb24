import * as React from 'react';
import { cn } from '@/lib/utils';

export interface SectionHeadingProps
  extends React.HTMLAttributes<HTMLHeadingElement> {
  as?: React.ElementType;
}

export const Highlight: React.FC<{ children: React.ReactNode }> = ({
  children
}) => <span className="text-yellow-400 text-shadow-yellow">{children}</span>;

const SectionHeading = React.forwardRef<
  HTMLHeadingElement,
  SectionHeadingProps
>(({ className, as: Component = 'h2', children, ...props }, ref) => (
  <Component
    ref={ref}
    className={cn(
      'font-roboto-condensed font-[700] !leading-[1.05] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]',
      className
    )}
    style={{ lineHeight: '1.05' }}
    {...props}
  >
    {children}
  </Component>
));
SectionHeading.displayName = 'SectionHeading';

export default SectionHeading;
export { SectionHeading };
