'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { signInWithPassword } from '@/app/utils/auth-helpers/server';
import { ThemedButton } from '@/components/ui/themed-button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { createClient } from '@/app/utils/supabase/client';
import Link from 'next/link';
import FormAlert from './FormAlert';
import { trackEvent, trackCTAClick } from '@/lib/ga-events';

interface PasswordSignInProps {
  allowEmail?: boolean;
  redirectMethod?: string;
  next?: string;
}

export default function PasswordSignIn({
  allowEmail,
  next
}: Readonly<PasswordSignInProps>) {
  const searchParams = useSearchParams();
  const status = searchParams.get('status');
  const statusDescription = searchParams.get('status_description');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  const isFromSignup = searchParams.get('signup') === 'true';
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const supabase = createClient();

  // Get the next parameter either from props or URL
  const nextUrl = next || searchParams.get('next');

  // 🆕 Track page view when component mounts
  useEffect(() => {
    // Track password signin page view
    trackEvent('page_view', {
      page_title: 'Password Sign In',
      user_journey: 'dashboard_direct',
      signin_method: 'password',
      from_signup: isFromSignup
    });

    // Track if user came from signup flow
    if (isFromSignup) {
      trackEvent('post_signup_flow', {
        event_category: 'authentication',
        flow_type: 'signup_to_signin',
        signin_method: 'password'
      });
    }
  }, [isFromSignup]);

  // Reset loading state on mount and clear on unmount
  useEffect(() => {
    // Reset loading state when component mounts
    setIsSubmitting(false);

    // Clear loading state when component unmounts
    return () => {
      if (isSubmitting) {
        console.log('Clearing loading state on unmount');
        setIsSubmitting(false);
      }
    };
  }, [isSubmitting]);

  // Set up safety timeout to clear loading state
  useEffect(() => {
    let safetyTimeout: NodeJS.Timeout | null = null;

    if (isSubmitting) {
      safetyTimeout = setTimeout(() => {
        console.log('Safety timeout triggered to clear loading state');
        setIsSubmitting(false);
      }, 5000); // 5 seconds max wait time
    }

    return () => {
      if (safetyTimeout) {
        clearTimeout(safetyTimeout);
      }
    };
  }, [isSubmitting]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // If already submitting, don't allow another submission
    if (isSubmitting) return;

    // 🆕 Track password signin attempt
    trackEvent('login_attempt', {
      event_category: 'authentication',
      method: 'password',
      user_journey: 'dashboard_direct',
      from_signup: isFromSignup
    });

    trackCTAClick('password_signin', 'signin_form', 'Sign in');

    setIsSubmitting(true);

    // Create a local variable to track if we should update state
    // This helps prevent state updates after component unmount
    let isMounted = true;
    let redirectPath = '';

    try {
      // include the funnel return URL
      const formData = new FormData(e.currentTarget);
      if (nextUrl) formData.append('next', nextUrl);

      // Log email and password for debugging
      console.log('Attempting sign in with email:', formData.get('email'));
      console.log(
        'Attempting sign in with password:',
        formData.get('password') ? '***' : '(no password)'
      );

      redirectPath = await signInWithPassword(formData);

      if (redirectPath.includes('error=')) {
        const params = new URLSearchParams(redirectPath.split('?')[1]);
        const errorDesc = params.get('error_description');

        // 🆕 Track password signin failure
        trackEvent('login_failure', {
          event_category: 'error',
          method: 'password',
          error_type: 'invalid_credentials',
          error_message: errorDesc?.substring(0, 100) || 'Sign in failed',
          user_journey: 'dashboard_direct',
          from_signup: isFromSignup
        });

        // Show toast with error message immediately here
        if (isMounted) {
          toast({
            title: 'Authentication Error',
            description: errorDesc || 'Sign in failed',
            variant: 'destructive'
          });
          setIsSubmitting(false);
        }

        // Throw error to be caught below as well
        throw new Error(errorDesc || 'Sign in failed');
      }

      // 🆕 Track successful password signin
      trackEvent('login_success', {
        event_category: 'authentication',
        method: 'password',
        user_journey: 'dashboard_direct',
        from_signup: isFromSignup,
        returning_user: !isFromSignup
      });

      // Show success toast
      toast({
        title: 'Success',
        description: 'Signing you in...',
        variant: 'default'
      });

      try {
        // Clear any logged out flag
        sessionStorage.removeItem('logged_out');

        // Explicitly refresh the session to ensure Navigation knows we're logged in
        await supabase.auth.refreshSession();
      } catch (refreshError) {
        console.error('Session refresh error (non-blocking):', refreshError);
        // Continue with redirect even if refresh fails
      }

      // Navigate immediately to avoid getting stuck
      console.log('Redirecting to:', redirectPath);
      window.location.replace(redirectPath);
    } catch (error) {
      console.error('Login error caught:', error);

      // 🆕 Track general signin failure if not already tracked above
      if (!redirectPath || !redirectPath.includes('error=')) {
        trackEvent('login_failure', {
          event_category: 'error',
          method: 'password',
          error_type: 'signin_error',
          error_message:
            error instanceof Error
              ? error.message.substring(0, 100)
              : 'Unknown error',
          user_journey: 'dashboard_direct'
        });
      }

      // The toast for error is already shown above, so no need to show again here
      if (isMounted) {
        setIsSubmitting(false);
      }
    }

    // Set cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  };

  return (
    <div className="my-8 text-white">
      {/* Use FormAlert component - include signup-specific messaging */}
      <FormAlert
        status={status}
        statusDescription={statusDescription}
        error={error}
        errorDescription={errorDescription}
      />

      {/* Show additional context for users coming from signup */}
      {isFromSignup && !error && (
        <div className="mb-4 p-3 bg-blue-50/20 backdrop-blur-sm border border-blue-200/30 rounded-md">
          <p className="text-sm text-blue-100">
            ✨ <strong>Account created successfully!</strong> Once you confirm
            your email, you can sign in below with your credentials.
          </p>
        </div>
      )}

      <form noValidate className="mb-4" onSubmit={handleSubmit}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <label htmlFor="email" className="text-white">
              Email
            </label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              name="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isSubmitting}
              required
              className="w-full p-3 rounded-md bg-white/10 border-white/20 text-white auth-input"
            />
            <label htmlFor="password" className="text-white">
              Password
            </label>
            <Input
              id="password"
              placeholder="Password"
              type="password"
              name="password"
              autoComplete="current-password"
              disabled={isSubmitting}
              required
              className="w-full p-3 rounded-md bg-white/10 border-white/20 text-white auth-input"
            />
          </div>
          <ThemedButton
            type="submit"
            variant="primary"
            fullWidth
            disabled={isSubmitting}
            className="mt-1"
          >
            {isSubmitting ? 'Signing in...' : 'Sign in'}
          </ThemedButton>
        </div>
      </form>

      <div className="mt-4 text-sm">
        <p>
          <Link
            href="/signin/forgot_password"
            className="text-[hsl(var(--hero-yellow))] hover:underline"
            onClick={() => {
              // 🆕 Track forgot password click
              trackCTAClick(
                'forgot_password',
                'signin_links',
                'Forgot password?'
              );
              trackEvent('password_reset_initiated', {
                event_category: 'authentication',
                source_page: 'password_signin',
                user_journey: 'dashboard_direct'
              });
            }}
          >
            Forgot password?
          </Link>
        </p>
        {allowEmail && (
          <p className="mt-1">
            <Link
              href="/signin/email_signin"
              className="text-[hsl(var(--hero-yellow))] hover:underline"
              onClick={() => {
                // 🆕 Track navigation to magic link signin
                trackCTAClick(
                  'navigation_magic_link',
                  'signin_links',
                  'Sign in with magic link'
                );
                trackEvent('signin_method_switch', {
                  event_category: 'authentication',
                  from_method: 'password',
                  to_method: 'magic_link',
                  user_journey: 'dashboard_direct'
                });
              }}
            >
              Sign in with magic link
            </Link>
          </p>
        )}
        <p className="mt-1">
          <Link
            href={`/signin/signup?next=${encodeURIComponent(nextUrl ?? '/dashboard')}`}
            className="text-[hsl(var(--hero-yellow))] hover:underline"
            onClick={() => {
              // 🆕 Track navigation to signup
              trackCTAClick(
                'navigation_signup',
                'signin_links',
                "Don't have an account? Sign up"
              );
              trackEvent('signin_to_signup_flow', {
                event_category: 'authentication',
                from_page: 'password_signin',
                user_journey: 'onboarding'
              });
            }}
          >
            Don&apos;t have an account? Sign up
          </Link>
        </p>
      </div>
    </div>
  );
}
