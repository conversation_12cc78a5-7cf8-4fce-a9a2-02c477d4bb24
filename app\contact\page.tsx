import React from 'react';
import { Metada<PERSON> } from 'next';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Mail, Sparkles } from 'lucide-react';
import ContactForm from '@/components/footer/ContactForm';
import { constructMetadata } from '@/lib/seo-config';

export const metadata: Metadata = constructMetadata({
  title: 'Contact Us | Job Space AI',
  description:
    "Get in touch with Job Space AI for any questions, concerns, or feedback. We're here to help!",
  path: '/contact'
});

export default function ContactPage() {
  return (
    <div className="relative isolate min-h-screen overflow-hidden">
      {/* Background with animated blobs */}
      <div className="absolute inset-0 -z-10 pointer-events-none">
        {/* Subtle background features */}
        <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-50"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000 opacity-50"></div>
        <div className="absolute top-2/3 left-1/2 w-[550px] h-[550px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000 opacity-50"></div>
        <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000 opacity-50"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-16 max-w-[1200px]">
        <div className="text-center mb-12">
          <span className="text-yellow-300 font-semibold inline-flex items-center gap-2">
            <Sparkles className="w-4 h-4" /> GET IN TOUCH
          </span>
          <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
            Contact{' '}
            <span className="text-yellow-400 text-shadow-yellow">Us</span>
          </h1>
          <p className="text-slate-200 max-w-2xl mx-auto text-[16px] md:text-[18px]">
            We&apos;re here to help! If you have any questions, concerns, or
            feedback, please don&apos;t hesitate to reach out to us.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
          <div>
            <Card className="mb-6 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg text-white overflow-hidden">
              <CardHeader>
                <CardTitle className="flex items-center text-white">
                  <Mail className="mr-2 text-hero-yellow" /> Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-200">
                  Email:{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
                  >
                    <EMAIL>
                  </a>
                </p>
              </CardContent>
            </Card>
          </div>
          <div>
            <ContactForm />
          </div>
        </div>
      </div>
    </div>
  );
}
