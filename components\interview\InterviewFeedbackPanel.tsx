import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { InterviewFeedbackResult } from '@/app/types/globalTypes';

interface InterviewFeedbackPanelProps {
  /** The feedback to display */
  feedback: InterviewFeedbackResult;

  /** Callback to reset the interview */
  onReset: () => void;

  /** Callback to close the interview */
  onClose: () => void;
}

/**
 * Displays interview feedback including score, summary, strengths and weaknesses
 */
const InterviewFeedbackPanel: React.FC<InterviewFeedbackPanelProps> = ({
  feedback,
  onReset,
  onClose
}) => {
  // Check if feedback is properly populated before rendering
  if (!feedback || typeof feedback !== 'object') {
    return (
      <div className="mt-8 space-y-6" aria-live="polite">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-amber-300 to-amber-400 bg-clip-text text-transparent drop-shadow-[0_0_8px_rgba(255,192,71,.35)]">
          Interview Feedback
        </h2>
        <p className="text-red-500">Error loading feedback data</p>
        <div className="flex gap-4 pt-4">
          <Button
            variant="default"
            onClick={onReset}
            type="button"
            aria-label="Start a new interview"
            className="bg-[hsl(var(--hero-yellow))] text-[#111827] hover:bg-[hsl(var(--hero-yellow-light))] hover:border hover:border-yellow-300"
          >
            New Interview
          </Button>
          <Button
            variant="outline"
            onClick={onClose}
            type="button"
            aria-label="Close the interview"
            className="border-gray-700 text-white bg-transparent hover:bg-[hsl(var(--hero-bg))]/50 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))]"
          >
            Close
          </Button>
        </div>
      </div>
    );
  }

  // Ensure properties exist with defaults if missing
  const {
    overall_score = 0,
    overall_feedback = 'No feedback summary available',
    questions = []
  } = feedback;

  // Extract feedback from questions
  const answer_feedbacks = questions
    .filter((q) => q.feedback && q.score !== undefined)
    .map((q) => ({
      feedback: q.feedback || '',
      score: q.score || 0
    }));

  // Convert overall_score from 0-100 to 0-10 scale for display
  const displayScore = Math.round(overall_score / 10);

  // Group feedback into strengths and weaknesses
  const strengths = answer_feedbacks
    .filter((item) => item.score >= 5)
    .map((item) => item.feedback);

  const weaknesses = answer_feedbacks
    .filter((item) => item.score < 5)
    .map((item) => item.feedback);

  // Add default messages if needed
  if (strengths.length === 0) {
    strengths.push('You completed the interview process.');
  }

  if (weaknesses.length === 0) {
    weaknesses.push(
      'Consider providing more detailed responses in future interviews.'
    );
  }

  return (
    <div className="mt-8 space-y-6" aria-live="polite">
      <h2
        className="text-2xl font-bold bg-gradient-to-r from-amber-300 to-amber-400 bg-clip-text text-transparent drop-shadow-[0_0_8px_rgba(255,192,71,.35)]"
        id="feedback-heading"
      >
        Interview Feedback
      </h2>

      <div className="p-4 rounded-md shadow-sm backdrop-blur-sm bg-[hsl(var(--hero-bg))]/90 border border-gray-800">
        <h3 className="text-xl font-semibold mb-2 text-white">
          Overall Score: {displayScore}/10
        </h3>

        <p className="text-slate-300">{overall_feedback}</p>
      </div>

      <div>
        <h3
          className="text-lg font-medium mb-2 text-white"
          id="strengths-heading"
        >
          Strengths:
        </h3>
        <ul
          className="list-disc pl-5 space-y-1"
          aria-labelledby="strengths-heading"
        >
          {strengths.map((strength, index) => (
            <li key={`strength-${index}`} className="text-sm text-slate-300">
              {strength}
            </li>
          ))}
        </ul>
      </div>

      <Separator className="bg-gray-800" />

      <div>
        <h3
          className="text-lg font-medium mb-2 text-white"
          id="improvements-heading"
        >
          Areas for Improvement:
        </h3>
        <ul
          className="list-disc pl-5 space-y-1"
          aria-labelledby="improvements-heading"
        >
          {weaknesses.map((weakness, index) => (
            <li key={`weakness-${index}`} className="text-sm text-slate-300">
              {weakness}
            </li>
          ))}
        </ul>
      </div>

      <div className="flex gap-4 pt-4">
        <Button
          variant="default"
          onClick={onReset}
          type="button"
          aria-label="Start a new interview"
          className="bg-[hsl(var(--hero-yellow))] text-[#111827] hover:bg-[hsl(var(--hero-yellow-light))] hover:border hover:border-yellow-300"
        >
          New Interview
        </Button>
        <Button
          variant="outline"
          onClick={onClose}
          type="button"
          aria-label="Close the interview"
          className="border-gray-700 text-white bg-transparent hover:bg-[hsl(var(--hero-bg))]/50 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))]"
        >
          Close
        </Button>
      </div>
    </div>
  );
};

export default InterviewFeedbackPanel;
