import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export const createClient = async () => {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(name, value, options) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            console.error('Error setting cookie in server component:', error);
            // This happens in server components where we can't set cookies directly
            // The middleware will handle setting cookies for auth operations
          }
        },
        remove(name, options) {
          try {
            cookieStore.set(name, '', { ...options, maxAge: 0 });
          } catch (error) {
            console.error('Error removing cookie in server component:', error);
            // This happens in server components where we can't set cookies directly
            // The middleware will handle setting cookies for auth operations
          }
        }
      }
    }
  );
};
