'use client';

import React, { useEffect, useState } from 'react';
import type { User } from '@supabase/supabase-js';
import { createClient } from '@/app/utils/supabase/client';
import { HybridButton } from '@/components/ui/themed-button';
import { trackCTAClick, trackUserInteraction } from '@/lib/ga-events';

const CTAButton = () => {
  const [user, setUser] = useState<User | null>(null);
  const supabase = createClient();

  useEffect(() => {
    // Get initial auth state
    supabase.auth.getUser().then(({ data: { user } }) => {
      console.log('[CTAButton] Initial user state:', user);
      setUser(user);
    });

    // Subscribe to auth changes
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((_event, session) => {
      console.log('[CTAButton] Auth state changed:', {
        event: _event,
        user: session?.user
      });
      setUser(session?.user || null);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [supabase.auth]);

  // Encode the return URL properly
  const returnUrl = encodeURIComponent('/application-funnel?step=1');

  const ctaConfig = {
    text: user ? 'Get Started' : 'Unlock My Career Potential',
    href: user ? '/application-funnel?step=1' : `/signin?next=${returnUrl}`
  };

  console.log('[CTAButton] Current config:', ctaConfig);

  const handleCTAClick = () => {
    // Track CTA click with context
    trackCTAClick(
      user ? 'get_started_authenticated' : 'unlock_career_potential',
      'onboarding_page',
      ctaConfig.text
    );

    // Track user interaction with additional context
    trackUserInteraction({
      interaction_type: 'cta_click',
      element_type: 'primary_cta_button',
      user_journey: 'onboarding',
      user_type: user ? 'authenticated' : 'anonymous',
      button_text: ctaConfig.text,
      destination_url: ctaConfig.href
    });

    console.log('[CTAButton] Button clicked, redirecting to:', ctaConfig.href);
  };

  return (
    <HybridButton
      href={ctaConfig.href}
      variant="primary"
      size="lg"
      showArrow={true}
      onClick={handleCTAClick}
    >
      {ctaConfig.text}
    </HybridButton>
  );
};

export default CTAButton;
