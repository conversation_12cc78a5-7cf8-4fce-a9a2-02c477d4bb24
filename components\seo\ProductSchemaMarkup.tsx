import React from 'react';
import JsonLd from '@/components/seo/JsonLd';

interface ProductSchemaMarkupProps {
  name: string;
  description: string;
  image: string;
  price?: number;
  priceCurrency?: string;
  ratingValue?: number;
  ratingCount?: number;
  features?: string[];
}

// Define the schema type with all possible properties
interface ProductSchema {
  '@context': string;
  '@type': string;
  name: string;
  description: string;
  image: string;
  offers: {
    '@type': string;
    price: string;
    priceCurrency: string;
    availability: string;
  };
  aggregateRating: {
    '@type': string;
    ratingValue: string;
    ratingCount: string;
    bestRating: string;
    worstRating: string;
  };
  additionalProperty?: Array<{
    '@type': string;
    name: string;
    value: string;
  }>;
}

const ProductSchemaMarkup: React.FC<ProductSchemaMarkupProps> = ({
  name,
  description,
  image,
  price = 0,
  priceCurrency = 'GBP',
  ratingValue = 4.8,
  ratingCount = 250,
  features = []
}) => {
  const productSchema: ProductSchema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name,
    description,
    image,
    offers: {
      '@type': 'Offer',
      price: price.toString(),
      priceCurrency,
      availability: 'https://schema.org/InStock'
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: ratingValue.toString(),
      ratingCount: ratingCount.toString(),
      bestRating: '5',
      worstRating: '1'
    }
  };

  // Add features if provided
  if (features.length > 0) {
    productSchema['additionalProperty'] = features.map((feature) => ({
      '@type': 'PropertyValue',
      name: 'feature',
      value: feature
    }));
  }

  return <JsonLd id="product-schema" data={productSchema} />;
};

export default ProductSchemaMarkup;
