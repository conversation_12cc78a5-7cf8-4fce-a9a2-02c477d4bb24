// components/home/<USER>
'use client';

import React from 'react';
import { Upload, FileText, Brain, Target, ArrowRight } from 'lucide-react';
import { CTAButton } from '@/components/ui/themed-button';
import SectionHeading, { Highlight } from '../ui/section-heading';

const steps = [
  {
    number: '01',
    icon: Upload,
    title: 'Upload Your CV',
    description:
      'Upload your existing CV or build a new one from scratch using our AI-powered builder',
    details: [
      'PDF or Word format supported',
      'AI extracts and analyzes content',
      'Secure, encrypted processing'
    ]
  },
  {
    number: '02',
    icon: FileText,
    title: 'Paste Job Description',
    description:
      'Copy and paste the job description from any job board, company website, or job listing',
    details: [
      'Works with any job posting format',
      'AI extracts key requirements',
      'Identifies must-have skills'
    ]
  },
  {
    number: '03',
    icon: Brain,
    title: 'Get AI Analysis',
    description:
      'Our AI analyzes the match between your CV and the job, providing instant feedback and success predictions',
    details: [
      'Success rate prediction',
      'Skill gap identification',
      'ATS compatibility check',
      'Tailored improvement suggestions'
    ]
  },
  {
    number: '04',
    icon: Target,
    title: 'Apply with Confidence',
    description:
      'Get your optimized CV, tailored cover letter, and interview preparation - all ready in under 60 seconds',
    details: [
      'ATS-optimized CV',
      'Custom cover letter',
      'Interview questions prep',
      'Application tracking'
    ]
  }
];

export const HowItWorksProcess = () => {
  return (
    <section className="relative isolate py-16 lg:py-24">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <span className="text-yellow-400 font-semibold text-sm uppercase tracking-wider">
            How It Works
          </span>

          <SectionHeading className="text-center max-w-4xl mx-auto">
            How Our <Highlight>AI Job Preparation</Highlight> Works
          </SectionHeading>
          <p className="mt-6 text-lg text-slate-300 max-w-3xl mx-auto">
            From CV upload to job application success - our AI guides you
            through every step of the job preparation process in under 60
            seconds.
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isLast = index === steps.length - 1;

            return (
              <div key={step.number} className="relative">
                {/* Connection Arrow - Hidden on last item and mobile */}
                {!isLast && (
                  <div className="hidden lg:block absolute top-16 -right-4 z-10">
                    <ArrowRight className="w-8 h-8 text-yellow-400/60" />
                  </div>
                )}

                {/* Step Card */}
                <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300 group h-full flex flex-col min-h-[400px]">
                  {/* Step Number */}
                  <div className="absolute -top-3 -left-3 w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
                    <span className="text-black font-bold text-lg">
                      {step.number}
                    </span>
                  </div>

                  {/* Icon */}
                  <div className="mb-6 mt-4">
                    <div className="w-16 h-16 bg-yellow-400/10 rounded-lg flex items-center justify-center group-hover:bg-yellow-400/20 transition-colors">
                      <Icon className="w-8 h-8 text-yellow-400" />
                    </div>
                  </div>

                  {/* Content - Flex grow to fill available space */}
                  <div className="space-y-4 flex-grow flex flex-col">
                    <h3 className="text-xl font-bold text-white">
                      {step.title}
                    </h3>

                    <p className="text-slate-300 leading-relaxed">
                      {step.description}
                    </p>

                    {/* Details List - Push to bottom */}
                    <div className="mt-auto">
                      <ul className="space-y-2">
                        {step.details.map((detail, idx) => (
                          <li
                            key={idx}
                            className="text-sm text-slate-400 flex items-center gap-2"
                          >
                            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full flex-shrink-0" />
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA - Using themed button */}
        <div className="text-center mt-16">
          <p className="text-slate-300 mb-6">
            Ready to transform your job search with AI?
          </p>
          <CTAButton href="/onboarding" size="lg" className="text-lg font-bold">
            Start Your Job Preparation Journey
          </CTAButton>
        </div>
      </div>
    </section>
  );
};
