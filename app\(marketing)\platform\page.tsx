import { CallToAction } from 'components/CallToAction';
import { RecentArticles } from 'components/articles/RecentArticles';
import { getUser } from 'app/utils/auth-helpers/server';
import { faqLd, FAQItem } from 'lib/faq';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from 'components/ui/accordion';
import JsonLd from '@/components/seo/JsonLd';

import { FiFileText, FiMail, FiCheckCircle, FiUserCheck } from 'react-icons/fi';
import { PricingSection } from 'components/home/<USER>';
import { ScreenshotsGrid } from 'components/ScreenshotsGrid';

export const revalidate = 86400;

export const metadata = {
  title: 'All-in-One AI Job Application Platform | JobSpaceAI.',
  description:
    'Automate your entire job search—CVs, cover letters, interviews, and ATS checks—from a single dashboard. Available now for UK job seekers.'
};

const faqs: FAQItem[] = [
  {
    question: 'How secure is my data?',
    answer:
      'We use industry-standard encryption (AES-256) and host all data in secure UK-based servers via Supabase. You retain full ownership.'
  },
  {
    question: 'Is there a free trial?',
    answer:
      'Yes—every new user gets 10 free credits to test the AI CV Builder, Cover Letter Generator, and ATS Checker before upgrading.'
  },
  {
    question: 'Can I cancel anytime?',
    answer:
      'Absolutely. You can cancel your subscription at any time from your dashboard without penalties.'
  },
  {
    question: 'Do you integrate with LinkedIn?',
    answer:
      'Yes, you can import job descriptions directly from LinkedIn to tailor your applications.'
  }
];

export default async function PlatformPage() {
  const user = await getUser();
  const href = user ? `/dashboard/${user.id}` : '/signin';

  return (
    <>
      {/* ===== HEADER ===== */}
      <div className="container mx-auto px-4 max-w-[1200px]">
        {/* Decorative blobs (all at -z-10) */}
        <div
          className="absolute -z-10 inset-0 pointer-events-none"
          aria-hidden="true"
        >
          <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-80 will-change-transform"></div>
          <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] rounded-full bg-hero-purple-glow blur-3xl animate-blob delay-2000 opacity-80 will-change-transform"></div>
          <div className="absolute top-2/3 left-1/2 w-[550px] h-[550px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob delay-4000 opacity-80 will-change-transform"></div>
          <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob delay-6000 opacity-80 will-change-transform"></div>
        </div>
      </div>
      {/* Header content */}
      <div className="relative px-4 py-4 sm:py-16 rounded-3xl backdrop-blur-sm text-center mx-4">
        <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
          AI{' '}
          <span className="text-yellow-400 text-shadow-yellow">
            Job-Application Platform
          </span>
        </h1>
        <p className="text-slate-200 max-w-[1200px] mx-auto text-[16px] md:text-[18px] mb-8 leading-relaxed">
          At JobSpaceAI, our vision is to consolidate every essential job search
          feature into a seamless, all-in-one workflow. From crafting tailored
          CVs and cover letters to preparing for interviews and performing ATS
          checks, everything is automated and accessible from a single intuitive
          dashboard. This unified platform empowers UK job seekers to streamline
          their application process, save time, and increase their chances of
          success. Experience the comprehensive solution available now and{' '}
          <a href={href} className="text-yellow-400 underline">
            learn more
          </a>{' '}
          about how JobSpaceAI transforms your job search experience.
        </p>
        <div className='mx-auto'>
          <CallToAction
            label="Learn more"
            sublabel="Your all-in-one AI job application platform"
            href={href}
          />
        </div>
        <RecentArticles />
      </div>

      {/* ===== Feature Overview ===== */}
      <section className="py-16  mb-16" aria-labelledby="features-heading">
        <h2 className="mb-4 text-2xl text-center font-semibold">Features</h2>

        <div className="container mx-auto px-4 max-w-[1200px] flex flex-col md:flex-row">
          <div className="flex-grow min-w-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-white">
              <div className="p-6 rounded-lg flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300 break-words">
                <FiFileText
                  className="h-12 w-12 mb-4 text-yellow-400 flex-shrink-0"
                  aria-hidden="true"
                />
                <h3 className="text-xl font-semibold mb-2">AI CV Builder</h3>
                <p>
                  Generate ATS-optimized CVs in seconds and land more
                  interviews.
                </p>
              </div>
              <div className="p-6 rounded-lg flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300 break-words">
                <FiMail
                  className="h-12 w-12 mb-4 text-yellow-400 flex-shrink-0"
                  aria-hidden="true"
                />
                <h3 className="text-xl font-semibold mb-2">
                  AI Cover Letter Generator
                </h3>
                <p>
                  Automatically tailor your cover letter to any job description.
                </p>
              </div>
              <div className="p-6 rounded-lg flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300 break-words">
                <FiCheckCircle
                  className="h-12 w-12 mb-4 text-yellow-400 flex-shrink-0"
                  aria-hidden="true"
                />
                <h3 className="text-xl font-semibold mb-2">ATS CV Checker</h3>
                <p>
                  Identify missing keywords and formatting issues before you
                  apply.
                </p>
              </div>
              <div className="p-6 rounded-lg flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300 break-words">
                <FiUserCheck
                  className="h-12 w-12 mb-4 text-yellow-400 flex-shrink-0"
                  aria-hidden="true"
                />
                <h3 className="text-xl font-semibold mb-2">
                  AI Interview Coach
                </h3>
                <p>
                  Practice with real‐time teleprompter prompts and pacing
                  analysis.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ===== Visual Product Tour ===== */}
      <section className="py-16 mb-16" aria-labelledby="visual-tour-heading">
        <div className="container mx-auto px-4 w-full">
          <h2
            id="visual-tour-heading"
            className="text-2xl text-center font-bold text-white mb-6"
          >
            See the Platform in Action
          </h2>
          <div>
            <ScreenshotsGrid />
          </div>
        </div>
      </section>

      {/* ===== Pricing Preview ===== */}
      <section className="py-12 mb-16" aria-labelledby="pricing-heading">
        <div className="container mx-auto px-4 text-white">
          <div>
            <PricingSection />
          </div>
        </div>
      </section>

      {/* ===== FAQ Section ===== */}
      <section id="faqs" className="py-16 mb-16">
        <div className="container mx-auto px-4 max-w-[1200px] rounded-3xl backdrop-blur-sm text-center">
          <h2 className="mb-4 text-2xl font-semibold text-white">
            Frequently Asked Questions
          </h2>
          <Accordion
            type="single"
            collapsible
            className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
          >
            {faqs.map(({ question, answer }, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="border-white/10"
              >
                <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-sm sm:text-lg font-medium">
                  {question}
                </AccordionTrigger>
                <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
                  {answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
          <JsonLd id="faq-schema" data={faqLd('/platform', faqs)} />
        </div>
      </section>

      {/* ===== Integrations & Compliance ===== */}
      <section className="py-16">
        <div className="container mx-auto px-4 max-w-[1200px] text-white">
          <h2 className="text-2xl font-bold mb-6">Integrations & Compliance</h2>
          <ul className="list-disc pl-6 text-lg leading-relaxed space-y-2">
            <li>Connect directly with LinkedIn to import job descriptions.</li>
            <li>Export CVs as PDF, DOCX, or shareable link.</li>
            <li>GDPR-compliant data storage in Supabase (UK-based servers).</li>
            <li>Secure payments via Stripe (PCI DSS–certified).</li>
          </ul>
        </div>
      </section>
    </>
  );
}
