import React from 'react';
import { ThemedButton } from '@/components/ui/themed-button';
import { trackAnalysisStart, trackButtonClick } from '@/lib/ga-events';

interface ATSAnalysisButtonProps {
  onClick: () => void;
  disabled: boolean;
  resume?: { id: string; resume?: string };
  job?: { id: string; description?: string };
}

export const ATSAnalysisButton: React.FC<ATSAnalysisButtonProps> = ({
  onClick,
  disabled,
  resume,
  job
}) => {
  const handleClick = () => {
    // Track button click
    trackButtonClick({
      button_name: 'perform_ats_analysis',
      button_location: 'dashboard_features',
      user_journey: 'dashboard'
    });

    // Track analysis start
    trackAnalysisStart({
      analysis_type: 'ats_analysis',
      feature_used: 'ats_checker',
      user_journey: 'dashboard',
      resume_file_size: resume?.resume?.length || 0,
      job_description_length: job?.description?.length || 0
    });

    // Call the original onClick handler
    onClick();
  };

  return (
    <ThemedButton
      onClick={handleClick}
      disabled={disabled}
      variant="primary"
      size="md"
      className="mb-8"
    >
      Perform ATS Analysis
    </ThemedButton>
  );
};
