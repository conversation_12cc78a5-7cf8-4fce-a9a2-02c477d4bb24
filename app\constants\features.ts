import {
  FeatureCardProps,
  FEATURE_TO_SERVICE_TYPE,
  FEATURE_TO_STORAGE_KEY,
  FEATURE_TO_PROGRESS_KEY
} from '@/app/types/featureTypes';

// Main features data
export const FEATURES: FeatureCardProps[] = [
  {
    title: 'ATS',
    description:
      'See how applicant tracking system (ATS) software views your CV.',
    icon: '📋',
    details: {
      howItWorks:
        'Our AI analyzes your CV through the lens of popular ATS systems to ensure maximum visibility and compatibility.',
      benefits: [
        'Identify ATS compatibility issues',
        'Optimize keyword matching',
        'Improve CV parsing accuracy',
        'Increase application success rate'
      ],
      keyPoints: [
        'Format compatibility check',
        'Keyword optimization',
        'Parsing accuracy analysis',
        'Improvement recommendations'
      ]
    }
  },
  {
    title: 'Skill Gap Analysis',
    description:
      'Analyze your CV and find missing skills for your target job roles.',
    icon: '📊',
    details: {
      howItWorks:
        'Our AI analyzes your CV against thousands of job postings to identify skill gaps and provide targeted recommendations for improvement.',
      benefits: [
        'Identify crucial missing skills for your target roles',
        'Get personalized learning recommendations',
        'Track your skill development progress',
        'Compare your profile with successful candidates'
      ],
      keyPoints: [
        'Real-time skill market analysis',
        'Industry-specific recommendations',
        'Automated skill tracking',
        'Custom learning paths'
      ]
    }
  },
  {
    title: 'AI Career Matching',
    description:
      'Find the perfect career matches based on your skills and experience.',
    icon: '🎯',
    details: {
      howItWorks:
        'Our AI analyzes your profile and matches it with thousands of career paths to find the best opportunities for your skills and goals.',
      benefits: [
        'Discover new career opportunities',
        'Understand your market value',
        'Find roles matching your skills',
        'Explore growth potential'
      ],
      keyPoints: [
        'AI-powered matching',
        'Personalized recommendations',
        'Market demand analysis',
        'Career path planning'
      ]
    }
  },
  {
    title: 'Mock Interview',
    description: 'Practice interviewing with AI for your target role.',
    icon: '🎯',
    details: {
      howItWorks:
        'Our AI creates personalized interview scenarios based on your target role and provides real-time feedback on your responses.',
      benefits: [
        'Practice with role-specific questions',
        'Get instant feedback',
        'Improve interview confidence',
        'Learn from mistakes safely'
      ],
      keyPoints: [
        'Customized scenarios',
        'Real-time feedback',
        'Performance analytics',
        'Interview strategies'
      ]
    }
  },
  {
    title: 'Learning Resources',
    description: 'Access curated learning materials to build your skills.',
    icon: '📚',
    details: {
      howItWorks:
        'Our platform aggregates and recommends learning resources based on your skill gaps and career goals.',
      benefits: [
        'Targeted skill development',
        'Curated quality content',
        'Track learning progress',
        'Industry-relevant materials'
      ],
      keyPoints: [
        'Personalized recommendations',
        'Progress tracking',
        'Resource quality scoring',
        'Learning path creation'
      ]
    }
  },
  {
    title: 'Market Trends',
    description: 'Stay informed about industry trends and job market demands.',
    icon: '📈',
    hidden: true, // Add this line to hide the feature
    details: {
      howItWorks:
        'Our AI analyzes real-time market data to provide insights about industry trends, salary ranges, and in-demand skills.',
      benefits: [
        'Stay ahead of market changes',
        'Make informed career decisions',
        'Understand salary trends',
        'Track skill demand'
      ],
      keyPoints: [
        'Real-time analytics',
        'Industry insights',
        'Salary data',
        'Skill demand tracking'
      ]
    }
  },
  {
    title: 'AI CV Improvement',
    slug: 'ai-cv-builder',
    public: true,
    description: 'Get AI-powered suggestions to enhance your CV.',
    icon: '✍️',
    details: {
      howItWorks:
        'Our AI analyzes your CV and provides specific recommendations for improvement based on industry best practices and job requirements.',
      benefits: [
        'Professional writing suggestions',
        'Content optimization',
        'Format improvements',
        'Industry-specific advice'
      ],
      keyPoints: [
        'Content analysis',
        'Style recommendations',
        'Format optimization',
        'Achievement highlighting'
      ]
    }
  },
  {
    title: 'ATS Resume Checker',
    slug: 'ats-resume-checker',
    public: true,
    description:
      'Paste your CV and job ad to instantly check ATS compatibility and get AI-powered fixes.',
    icon: '📋',
    details: {
      howItWorks:
        'Drop in your résumé and job description to see how well you pass ATS filters, with instant keyword-match scores and AI suggestions.',
      benefits: [
        'Instant ATS compatibility score',
        'Keyword and formatting issue detection',
        'AI-powered CV fixes',
        'No signup required'
      ],
      keyPoints: [
        'Upload or paste CV',
        'Add job description',
        'Get instant ATS score',
        'AI-driven improvements'
      ]
    }
  },
  {
    title: 'Cover Letter Generator',
    slug: 'ai-cover-letter',
    public: true,
    description: 'Generate tailored cover letters for your job applications.',
    icon: '📝',
    details: {
      howItWorks:
        'Our AI analyzes your CV and the job description to create a personalized, compelling cover letter that highlights your relevant experience.',
      benefits: [
        'Customized for each application',
        'Highlights relevant skills',
        'Professional formatting',
        'Time-saving automation'
      ],
      keyPoints: [
        'AI-powered customization',
        'Job-specific targeting',
        'Professional tone',
        'Quick generation'
      ]
    }
  },
  {
    title: 'AI Career Coach',
    slug: 'ai-interview-coach',
    public: true,
    description: 'Get personalized career guidance and advice.',
    icon: '🤝',
    details: {
      howItWorks:
        'Our AI career coach provides personalized guidance, answers your career-related questions, and helps you make informed decisions.',
      benefits: [
        'Personalized career advice',
        'Decision-making support',
        'Goal setting assistance',
        'Professional development planning'
      ],
      keyPoints: [
        'AI-powered guidance',
        'Personalized feedback',
        'Action planning',
        'Progress tracking'
      ]
    }
  },
  {
    title: 'JobSpaceAI Platform',
    slug: 'platform',
    public: true,
    description: 'Explore the all-in-one AI job application platform.',
    icon: '🧩',
    details: {
      howItWorks:
        'Access a unified dashboard that automates CVs, cover letters, interviews, and ATS checks for UK job seekers.',
      benefits: [
        'Streamlined job application workflow',
        'Automated document creation',
        'Interview preparation tools',
        'ATS compatibility checks'
      ],
      keyPoints: [
        'Single dashboard access',
        'Comprehensive job search tools',
        'AI-powered automation',
        'Available now for UK job seekers'
      ]
    }
  },
  {
    title: 'What is ATS? UK Guide',
    slug: 'what-is-ats-uk',
    public: true,
    description:
      'Understand how applicant tracking systems work in the UK and how to optimise your CV.',
    icon: '📖',
    details: {
      howItWorks:
        'Covers UK-specific ATS usage statistics, popular platforms and optimisation tips.',
      benefits: [
        'Learn why CVs get filtered',
        'See which UK companies use ATS',
        'Get formatting and keyword advice'
      ],
      keyPoints: [
        'UK ATS overview',
        'Common rejection reasons',
        'Optimisation checklist'
      ]
    }
  },
  {
    title: 'ATS CV Formatting Guide',
    slug: 'ats-cv-formatting-guide',
    public: true,
    description:
      '7 critical formatting mistakes that get UK CVs rejected by ATS systems.',
    icon: '⚠️',
    details: {
      howItWorks:
        'Learn the 7 deadly CV formatting sins that cause instant rejection and discover how to format your CV for UK ATS success.',
      benefits: [
        'Avoid common formatting mistakes',
        'Understand ATS parsing requirements',
        'Optimize CV for UK employers',
        'Increase interview invitation rates'
      ],
      keyPoints: [
        '7 critical mistakes breakdown',
        'UK ATS system analysis',
        'Expert recommendations',
        'Step-by-step fixes'
      ]
    }
  },
  {
    title: 'Free CV Scanner Guide',
    slug: 'free-cv-scanner-guide-uk-2025',
    public: true,
    description:
      'Step-by-step instructions to scan your CV and fix ATS issues.',
    icon: '🔍',
    details: {
      howItWorks:
        'Shows how to upload your CV, read the ATS report and apply improvements for UK jobs.',
      benefits: [
        'Optimise CV for UK employers',
        'Improve keyword matches',
        'Avoid formatting problems',
        'Increase interview chances'
      ],
      keyPoints: [
        'Free scanner walkthrough',
        'Checklist of fixes',
        'ATS FAQ',
        'Recommended tools'
      ]
    }
  }
] as const;

// Helper functions
export const getFeatureByTitle = (title: string) => {
  return FEATURES.find((feature) => feature.title === title);
};

export const getServiceType = (title: string) => {
  return FEATURE_TO_SERVICE_TYPE[title as keyof typeof FEATURE_TO_SERVICE_TYPE];
};

export const getStorageKey = (title: string) => {
  return FEATURE_TO_STORAGE_KEY[title as keyof typeof FEATURE_TO_STORAGE_KEY];
};

export const getProgressKey = (title: string) => {
  return FEATURE_TO_PROGRESS_KEY[title as keyof typeof FEATURE_TO_PROGRESS_KEY];
};
