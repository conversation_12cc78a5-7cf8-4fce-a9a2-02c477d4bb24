// lib/custom-dimension-map.ts
import { CustomDimensionKeys } from '@/types/events';

/**
 * GA4 Custom Dimension Mapping
 * Maps our custom dimension keys to their GA4 index numbers
 *
 * ⚠️ IMPORTANT: These index numbers must match what you configure in your GA4 property
 * Go to: GA4 Property → Admin → Custom Definitions → Custom Dimensions
 */
export const CUSTOM_DIMENSION_MAP = {
  [CustomDimensionKeys.USER_JOURNEY]: 'custom_dimension_1',
  [CustomDimensionKeys.FILE_TYPE]: 'custom_dimension_2',
  [CustomDimensionKeys.FEATURE_USED]: 'custom_dimension_3',
  [CustomDimensionKeys.PLAN_TYPE]: 'custom_dimension_4',
  [CustomDimensionKeys.USER_TYPE]: 'custom_dimension_5',
  [CustomDimensionKeys.ANALYSIS_TYPE]: 'custom_dimension_6',
  [CustomDimensionKeys.ERROR_TYPE]: 'custom_dimension_7',
  [CustomDimensionKeys.TRAFFIC_SOURCE]: 'custom_dimension_8'
} as const;

/**
 * Helper function to get GA4 custom dimension parameter name
 */
export const getCustomDimensionKey = (
  dimensionKey: CustomDimensionKeys
): string => {
  return CUSTOM_DIMENSION_MAP[dimensionKey];
};

/**
 * Custom dimension configuration for GA4 setup
 * Use this when configuring your AnalyticsProvider
 */
export const GA4_CUSTOM_MAP = {
  // All using event scope since session scope isn't available in your GA4 setup
  user_journey: 'user_journey',
  file_type: 'file_type',
  feature_name: 'feature_name',
  plan_type: 'plan_type',
  user_type: 'user_type',
  analysis_type: 'analysis_type',
  error_type: 'error_type',
  traffic_source: 'traffic_source'
};

/**
 * Predefined values for consistent tracking
 */
export const DIMENSION_VALUES = {
  USER_JOURNEY: {
    ONBOARDING: 'onboarding',
    DASHBOARD_DIRECT: 'dashboard_direct'
  },

  FILE_TYPE: {
    JOB_DESCRIPTION: 'job_description',
    CV_RESUME: 'cv_resume'
  },

  FEATURE_NAMES: {
    CV_ANALYZER: 'cv_analyzer',
    COVER_LETTER_GENERATOR: 'cover_letter_generator',
    INTERVIEW_PREP: 'interview_prep',
    ATS_CHECKER: 'ats_checker'
  },

  PLAN_TYPES: {
    FREE: 'free',
    TRIAL: 'trial',
    PREMIUM: 'premium',
    ENTERPRISE: 'enterprise'
  },

  USER_TYPES: {
    NEW: 'new',
    RETURNING: 'returning',
    TRIAL: 'trial',
    PAID: 'paid'
  },

  ANALYSIS_TYPES: {
    CV_ANALYSIS: 'cv_analysis',
    ATS_CHECK: 'ats_check',
    COVER_LETTER: 'cover_letter',
    JOB_MATCH: 'job_match'
  },

  ERROR_TYPES: {
    UPLOAD_FAILED: 'upload_failed',
    ANALYSIS_FAILED: 'analysis_failed',
    PAYMENT_FAILED: 'payment_failed',
    AUTH_FAILED: 'auth_failed',
    API_ERROR: 'api_error'
  },

  TRAFFIC_SOURCES: {
    ORGANIC: 'organic',
    PAID: 'paid',
    SOCIAL: 'social',
    DIRECT: 'direct',
    REFERRAL: 'referral'
  }
} as const;

// Export types for TypeScript safety
export type UserJourneyType =
  (typeof DIMENSION_VALUES.USER_JOURNEY)[keyof typeof DIMENSION_VALUES.USER_JOURNEY];
export type FileType =
  (typeof DIMENSION_VALUES.FILE_TYPE)[keyof typeof DIMENSION_VALUES.FILE_TYPE];
export type FeatureName =
  (typeof DIMENSION_VALUES.FEATURE_NAMES)[keyof typeof DIMENSION_VALUES.FEATURE_NAMES];
export type PlanType =
  (typeof DIMENSION_VALUES.PLAN_TYPES)[keyof typeof DIMENSION_VALUES.PLAN_TYPES];
export type UserType =
  (typeof DIMENSION_VALUES.USER_TYPES)[keyof typeof DIMENSION_VALUES.USER_TYPES];
export type AnalysisType =
  (typeof DIMENSION_VALUES.ANALYSIS_TYPES)[keyof typeof DIMENSION_VALUES.ANALYSIS_TYPES];
export type ErrorType =
  (typeof DIMENSION_VALUES.ERROR_TYPES)[keyof typeof DIMENSION_VALUES.ERROR_TYPES];
export type TrafficSource =
  (typeof DIMENSION_VALUES.TRAFFIC_SOURCES)[keyof typeof DIMENSION_VALUES.TRAFFIC_SOURCES];
