import React from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import type { ResumeAnalysisResult } from '@/app/types/globalTypes';

interface ResumeAnalysisDialogProps {
  analysis: ResumeAnalysisResult | null;
  isOpen: boolean;
  onClose: () => void;
}

const ResumeAnalysisDialog: React.FC<ResumeAnalysisDialogProps> = ({
  analysis,
  isOpen,
  onClose
}) => {
  if (!analysis) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] w-full max-w-[95vw] overflow-y-auto p-4 sm:p-6 sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] backdrop-blur-sm bg-hero-bg/90 border border-white/20 text-white">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl text-[hsl(var(--hero-yellow))]">
            CV Analysis
          </DialogTitle>
          <DialogDescription className="text-slate-300">
            Detailed analysis of your CV content and structure
          </DialogDescription>
        </DialogHeader>

        <div className="mt-6">
          {analysis.sections && analysis.sections.length > 0 ? (
            <div className="space-y-8">
              {analysis.sections.map((section, index) => (
                <div key={index} className="space-y-3">
                  <h3 className="font-semibold text-base sm:text-lg text-[hsl(var(--hero-yellow))]">
                    {section.title}
                  </h3>
                  <div className="space-y-2.5">
                    {Array.isArray(section.content) ? (
                      section.content.map((item, itemIndex) => (
                        <div
                          key={itemIndex}
                          className="flex gap-3 text-sm sm:text-base"
                        >
                          <span className="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-[hsl(var(--hero-yellow))]" />
                          <span className="text-slate-200 leading-relaxed">
                            {typeof item === 'string'
                              ? item
                              : JSON.stringify(item)}
                          </span>
                        </div>
                      ))
                    ) : (
                      <div className="flex gap-3 text-sm sm:text-base">
                        <span className="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-[hsl(var(--hero-yellow))]" />
                        <span className="text-slate-200 leading-relaxed">
                          {typeof section.content === 'string'
                            ? section.content
                            : JSON.stringify(section.content)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-slate-300">No analysis sections available.</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ResumeAnalysisDialog;
