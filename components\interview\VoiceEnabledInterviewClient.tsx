import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Play, Pause, Volume2 } from 'lucide-react';

interface VoiceEnabledInterviewClientProps {
  isVoiceMode: boolean;
  isInterviewActive: boolean;
  isPaused: boolean;
  showSpeakButton?: boolean;
  onStartInterview: () => void;
  onPauseInterview: () => void;
  onContinueInterview: () => void;
  onSpeakQuestion: () => void;
}
/**
 * Voice controls for the interview process
 */
const VoiceEnabledInterviewClient: React.FC<
  VoiceEnabledInterviewClientProps
> = ({
  isVoiceMode,
  isInterviewActive,
  isPaused,
  showSpeakButton = false,
  onStartInterview,
  onPauseInterview,
  onContinueInterview,
  onSpeakQuestion
}) => {
  if (!isVoiceMode) return null;

  return (
    <div className="mb-6 p-4 border border-white/30 rounded-lg bg-[#0f172a] shadow-sm">
      <h3 className="text-lg font-medium text-white mb-3">
        Voice Interview Controls
      </h3>

      <div className="flex flex-wrap gap-3 mb-2">
        {!isInterviewActive && showSpeakButton && (
          <Button
            onClick={onStartInterview}
            className="bg-[#F6A03C] text-[#111827] hover:bg-[#F6A03C]/90 h-9 px-4 font-medium rounded-md"
            size="default"
          >
            <Play size={16} className="mr-2" />
            <span>Start Interview</span>
          </Button>
        )}

        {isInterviewActive && !isPaused && (
          <Button
            onClick={onPauseInterview}
            variant="outline"
            className="border-white bg-[#1e293b] text-white hover:bg-[#1e293b]/90 hover:text-white h-10 px-4 font-medium rounded-md"
            size="default"
          >
            <Pause size={16} className="mr-2" />
            <span>Pause</span>
          </Button>
        )}

        {isInterviewActive && isPaused && (
          <Button
            onClick={onContinueInterview}
            className="bg-[#F6A03C] text-[#111827] hover:bg-[#F6A03C]/90 h-9 px-4 font-medium rounded-md"
            size="default"
          >
            <Play size={16} className="mr-2" />
            <span>Continue</span>
          </Button>
        )}

        {/* Add a "Speak Question" button that's always available during an active interview */}
        {isInterviewActive && (
          <Button
            onClick={onSpeakQuestion}
            variant="outline"
            className="border-white bg-[#1e293b] text-white hover:bg-[#1e293b]/90 hover:text-white h-10 px-4 font-medium rounded-md"
            size="default"
          >
            <Volume2 size={16} className="mr-2" />
            <span>Repeat Question</span>
          </Button>
        )}

        {isInterviewActive && (
          <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-[#F6A03C] border border-[#F8B968]">
            {isPaused ? (
              <MicOff size={16} className="text-[#111827]" />
            ) : (
              <Mic size={16} className="text-[#111827] animate-pulse" />
            )}
            <span className="text-sm font-medium text-[#111827]">
              {isPaused ? 'Microphone paused' : 'Microphone active'}
            </span>
          </div>
        )}
      </div>

      {isInterviewActive && !isPaused && (
        <p className="text-sm mt-4 text-white bg-[#1e293b] p-3 rounded-md border border-white/30">
          <Mic size={16} className="inline-block mr-2 text-[#F6A03C]" />
          Speak clearly into your microphone to answer the question.
        </p>
      )}
    </div>
  );
};

export default VoiceEnabledInterviewClient;
