import React, { useState, useEffect } from 'react';
import {
  ComprehensiveAnalysisResult,
  Job,
  Resume,
  ATSAnalysisResponse
} from '@/app/types/globalTypes';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import ATSResultsRender from '@/components/ats/ATSResultsRender';
import { trackAnalysisComplete, trackUserInteraction } from '@/lib/ga-events';

type PlanType = 'BASIC' | 'PRO' | 'ULTIMATE';

interface ATSAnalysisDisplayProps {
  result: ComprehensiveAnalysisResult;
  error?: string | null;
  mode?: 'component' | 'dialog';
  variant?: PlanType;
  job?: Job;
  resume?: Resume;
  analysisStartTime?: number; // Optional: to calculate analysis duration
}

export const ATSAnalysisDisplay: React.FC<ATSAnalysisDisplayProps> = ({
  result,
  error,
  job,
  resume,
  analysisStartTime
}) => {
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [hasTrackedCompletion, setHasTrackedCompletion] = useState(false);

  // Track analysis completion when results are first displayed
  useEffect(() => {
    if (result && !hasTrackedCompletion) {
      const analysisTime = analysisStartTime
        ? Date.now() - analysisStartTime
        : undefined;

      trackAnalysisComplete({
        analysis_type: 'ats_analysis',
        analysis_time_ms: analysisTime,
        feature_used: 'ats_checker',
        user_journey: 'dashboard'
      });

      setHasTrackedCompletion(true);
    }
  }, [result, hasTrackedCompletion, analysisStartTime]);

  // Handle tab changes with tracking
  const handleTabChange = (newTab: string) => {
    trackUserInteraction({
      interaction_type: 'tab_change',
      element_type: 'ats_results_tab',
      user_journey: 'dashboard',
      tab_name: newTab,
      previous_tab: activeTab
    });

    setActiveTab(newTab);
  };

  if (error) {
    return (
      <div className="p-4 bg-muted rounded-lg">
        <p className="text-muted-foreground">Error: {error}</p>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="p-4 bg-muted rounded-lg">
        <p className="text-muted-foreground">
          No ATS analysis results available
        </p>
      </div>
    );
  }

  // Convert ComprehensiveAnalysisResult to a format compatible with ATSResultsRender
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const resultWithAny = result as any;

  // Create a mock section analysis if it doesn't exist
  const mockSectionAnalysis = {
    'contact information': { quality_score: 0.8 },
    education: { quality_score: 0.7 },
    experience: { quality_score: 0.6 },
    skills: { quality_score: 0.5 }
  };

  // Process the overall match score - handle both decimal (0.35) and percentage (35) formats
  let overallMatchPercentage = 0;
  if (typeof result.overall_match === 'string') {
    overallMatchPercentage = parseInt(result.overall_match);
  } else if (typeof result.overall_match === 'number') {
    // If the number is less than 1, it's likely a decimal (0.35) that needs to be converted to percentage
    overallMatchPercentage =
      result.overall_match < 1
        ? Math.round(result.overall_match * 100)
        : result.overall_match;
  }

  // Same processing for other percentage fields
  const processPercentage = (value: number | undefined) => {
    if (value === undefined) return 0;
    return value < 1 ? Math.round(value * 100) : value;
  };

  const convertedResult = {
    overall_match_percentage: overallMatchPercentage,
    keyword_match_percentage: processPercentage(
      result.keyword_match_percentage
    ),
    skill_match_percentage: processPercentage(result.skill_match_percentage),
    section_analysis: resultWithAny.section_analysis || mockSectionAnalysis,
    keyword_matches: resultWithAny.keyword_matches || [],
    missing_keywords: resultWithAny.missing_keywords || [],
    skill_matches: resultWithAny.skill_matches || [],
    missing_skills: resultWithAny.missing_skills || [],
    recommendations: resultWithAny.recommendations || []
  } as unknown as ATSAnalysisResponse;

  return (
    <div className="rounded-lg border backdrop-blur-sm bg-black/90 border-gray-800 shadow-lg overflow-hidden mb-8">
      <div className="p-6 pb-4 flex flex-row items-center justify-between border-b border-gray-800 bg-gradient-to-r from-blue-900/30 to-black">
        <div>
          <h2 className="text-2xl font-semibold text-white">ATS Analysis</h2>
          <p className="text-sm text-slate-300 mt-1">
            Optimize your CV for Applicant Tracking Systems
          </p>
        </div>

        <div className="flex items-center gap-3">
          <div className="text-right">
            <span className="text-sm font-medium text-slate-300">
              Overall Match
            </span>
            <div className="text-2xl font-bold text-white">
              {convertedResult.overall_match_percentage}%
            </div>
          </div>
          <Badge
            className={cn(
              'text-sm py-1 px-3',
              convertedResult.overall_match_percentage > 70
                ? 'bg-[hsl(var(--hero-yellow))]/20 text-[hsl(var(--hero-yellow))]'
                : convertedResult.overall_match_percentage > 40
                  ? 'bg-amber-500/20 text-amber-400'
                  : 'bg-red-500/20 text-red-400'
            )}
          >
            {convertedResult.overall_match_percentage > 70
              ? 'Excellent'
              : convertedResult.overall_match_percentage > 40
                ? 'Good'
                : 'Needs Work'}
          </Badge>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="border-b border-gray-800 bg-black/80">
          <div className="px-6 pt-2 pb-0">
            <TabsList className="grid w-full grid-cols-3 h-12 p-1 bg-gray-900/50">
              <TabsTrigger
                value="overview"
                className="flex items-center gap-1 py-2.5 data-[state=active]:bg-gray-800 data-[state=active]:text-[hsl(var(--hero-yellow))] text-slate-300"
              >
                <span>Overview</span>
              </TabsTrigger>
              <TabsTrigger
                value="keywords"
                className="flex items-center gap-1 py-2.5 data-[state=active]:bg-gray-800 data-[state=active]:text-[hsl(var(--hero-yellow))] text-slate-300"
              >
                <span>Keywords</span>
              </TabsTrigger>
              <TabsTrigger
                value="improvements"
                className="flex items-center gap-1 py-2.5 data-[state=active]:bg-gray-800 data-[state=active]:text-[hsl(var(--hero-yellow))] text-slate-300"
              >
                <span>Improvements</span>
              </TabsTrigger>
            </TabsList>
          </div>
        </div>

        <div className="px-6 py-6 overflow-y-auto backdrop-blur-sm bg-black/90">
          <TabsContent value="overview" className="mt-0 p-0">
            <ATSResultsRender
              analysisResult={convertedResult}
              atsResult={convertedResult}
              activeTab="overview"
            />
          </TabsContent>
          <TabsContent value="keywords" className="mt-0 p-0">
            <ATSResultsRender
              analysisResult={convertedResult}
              atsResult={convertedResult}
              activeTab="keywords"
            />
          </TabsContent>
          <TabsContent value="improvements" className="mt-0 p-0">
            <ATSResultsRender
              analysisResult={convertedResult}
              atsResult={convertedResult}
              activeTab="improvements"
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};
