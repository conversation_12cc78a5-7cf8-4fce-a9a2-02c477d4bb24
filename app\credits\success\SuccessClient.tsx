'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import <PERSON>rip<PERSON> from 'next/script';
import { formatGBP } from '@/app/utils/format-currency';
import { GoogleAdsConversion } from '@/app/components/GoogleAdsConversion';
import { trackUserInteraction } from '@/lib/ga-events';

interface SuccessClientProps {
  stripeSessionId: string;
  userId: string;
  amount: number;
  credits: number;
  returnUrl: string;
}

interface PaymentStatus {
  status: 'processing' | 'success' | 'error';
  message: string;
}

export function SuccessClient({
  stripeSessionId,
  userId,
  amount,
  credits,
  returnUrl
}: Readonly<SuccessClientProps>) {
  const router = useRouter();
  const [status, setStatus] = useState<PaymentStatus>({
    status: 'processing',
    message: 'Processing your purchase...'
  });

  useEffect(() => {
    // Check if this session has already been processed
    const processedSessions = JSON.parse(
      localStorage.getItem('processedSessions') || '[]'
    );

    if (processedSessions.includes(stripeSessionId)) {
      router.push('/credits?success=true');
      return;
    }

    const verifyPayment = async () => {
      try {
        setStatus({
          status: 'processing',
          message: 'Processing your purchase...'
        });

        // Track payment verification start
        trackUserInteraction({
          interaction_type: 'payment_verification_start',
          element_type: 'success_page',
          user_journey: 'pricing',
          session_id: stripeSessionId,
          amount_gbp: amount / 100,
          credits_purchased: credits
        });

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_URL}/api/credits/verify`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              sessionId: stripeSessionId,
              userId
            })
          }
        );

        if (!response.ok) {
          throw new Error('Payment verification failed');
        }

        // Mark this session as processed
        processedSessions.push(stripeSessionId);
        localStorage.setItem(
          'processedSessions',
          JSON.stringify(processedSessions)
        );

        setStatus({
          status: 'success',
          message: 'Payment successful! Redirecting...'
        });

        // Track successful payment verification
        trackUserInteraction({
          interaction_type: 'payment_verification_success',
          element_type: 'success_page',
          user_journey: 'pricing',
          session_id: stripeSessionId,
          amount_gbp: amount / 100,
          credits_purchased: credits
        });

        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Construct redirect URL with success parameter
        const redirectUrl = returnUrl.includes('?')
          ? `${returnUrl}&payment_success=true`
          : `${returnUrl}?payment_success=true`;

        router.push(redirectUrl);
      } catch (error) {
        console.error('Payment verification error:', error);

        // Track payment verification failure
        trackUserInteraction({
          interaction_type: 'payment_verification_failed',
          element_type: 'success_page',
          user_journey: 'pricing',
          error_message:
            error instanceof Error ? error.message : 'Unknown error',
          session_id: stripeSessionId,
          amount_gbp: amount / 100,
          credits_purchased: credits
        });

        setStatus({
          status: 'error',
          message:
            'There was a problem verifying your payment. Please contact support.'
        });

        await new Promise((resolve) => setTimeout(resolve, 3000));
        router.push('/credits?error=verification_failed');
      }
    };

    verifyPayment();

    // Keep session marked as processed for 5 minutes to prevent duplicates
    const cleanupTimeout = setTimeout(
      () => {
        const sessions = JSON.parse(
          localStorage.getItem('processedSessions') || '[]'
        );
        const updatedSessions = sessions.filter(
          (session: string) => session !== stripeSessionId
        );
        localStorage.setItem(
          'processedSessions',
          JSON.stringify(updatedSessions)
        );
      },
      5 * 60 * 1000
    ); // 5 minutes

    return () => clearTimeout(cleanupTimeout);
  }, [stripeSessionId, userId, router, returnUrl]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-hero-bg">
      <div className="max-w-md w-full mx-auto p-8">
        <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-lg shadow-lg p-6 text-center">
          <h1 className="text-2xl font-bold mb-4 text-card-foreground">
            {status.status === 'success'
              ? 'Payment Successful!'
              : status.message}
          </h1>

          {status.status === 'processing' && (
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
          )}

          {status.status === 'success' && (
            <div className="space-y-4">
              <p className="text-success">
                {credits} credits have been added to your account
              </p>
              <p className="text-sm text-muted-foreground">
                Amount paid: {formatGBP(amount)}
              </p>

              {/* Google Ads Conversion Tracking */}
              <GoogleAdsConversion
                conversionId={
                  process.env.NEXT_PUBLIC_GOOGLE_ADS_ID || 'AW-XXXXXXXXXX'
                }
                conversionLabel={
                  process.env.NEXT_PUBLIC_GOOGLE_ADS_PURCHASE_LABEL ||
                  'XXXXXXXXXXXXXXXXXX'
                }
                conversionValue={amount / 100} // Convert from pence to pounds
                transactionId={stripeSessionId}
                currency="GBP"
              />

              {/* Direct implementation of Google Ads conversion event snippet */}
              {status.status === 'success' && (
                <Script
                  id="google-ads-direct-event"
                  strategy="afterInteractive"
                >
                  {`
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag('event', 'ads_conversion_Purchase_1', {
                      // Transaction details
                      value: ${amount / 100},
                      currency: 'GBP',
                      transaction_id: '${stripeSessionId}'
                    });

                    // Also send a purchase event to GA4
                    gtag('event', 'purchase', {
                      transaction_id: '${stripeSessionId}',
                      value: ${amount / 100},
                      currency: 'GBP',
                      items: [{
                        item_name: 'Credits Purchase',
                        quantity: 1,
                        price: ${amount / 100}
                      }]
                    });
                  `}
                </Script>
              )}
            </div>
          )}

          {status.status === 'error' && (
            <div className="text-destructive">
              <p>{status.message}</p>
              <button
                onClick={() => router.push('/credits')}
                className="mt-4 text-sm text-primary hover:text-primary/80 underline"
              >
                Return to credits page
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
