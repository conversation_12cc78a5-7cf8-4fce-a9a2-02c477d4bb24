'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { signInWithOAuth } from '@/app/utils/auth-helpers/client';
import { type Provider } from '@supabase/supabase-js';
import { Linkedin } from 'lucide-react';
import { Input } from '../input';
import GoogleIcon from '@/components/icons/google';
import FormAlert from './FormAlert';

type OAuthProviders = {
  name: Provider;
  displayName: string;
  icon: JSX.Element;
  bgColor: string;
  hoverColor: string;
  textColor: string;
};

interface OauthSignInProps {
  next?: string;
}

export default function OauthSignIn({ next }: OauthSignInProps) {
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState<string | null>(null);

  // Get status and error messages from URL parameters
  const status = searchParams.get('status');
  const statusDescription = searchParams.get('status_description');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  const oAuthProviders: OAuthProviders[] = [
    {
      name: 'google',
      displayName: 'Continue with Google',
      icon: (
        <GoogleIcon className="h-5 w-5 group-hover:text-[hsl(var(--hero-yellow))]" />
      ),
      bgColor: 'bg-[hsl(var(--secondary))]',
      hoverColor: 'hover:bg-[hsl(var(--secondary))/90]',
      textColor:
        'text-[hsl(var(--secondary-foreground))] group-hover:text-[hsl(var(--hero-yellow))]'
    },
    {
      name: 'linkedin_oidc',
      displayName: 'Continue with LinkedIn',
      icon: (
        <Linkedin className="h-5 w-5 group-hover:text-[hsl(var(--hero-yellow))]" />
      ),
      bgColor: 'bg-[hsl(var(--secondary))]',
      hoverColor: 'hover:bg-[hsl(var(--secondary))/90]',
      textColor:
        'text-[hsl(var(--secondary-foreground))] group-hover:text-[hsl(var(--hero-yellow))]'
    }
  ];

  // Reset submitting state on mount
  useEffect(() => {
    setIsSubmitting(null);
  }, []);

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>,
    providerName: string
  ) => {
    e.preventDefault();
    setIsSubmitting(providerName);

    try {
      // Debug log the next prop value
      console.debug('OauthSignIn: next prop value:', next);

      // shove next into a hidden input or read from props
      if (next) {
        const fd = new FormData(e.currentTarget);
        // Debug log FormData entries before appending next
        for (const [key, value] of fd.entries()) {
          console.debug(`FormData before append: ${key} = ${value}`);
        }
        fd.append('next', next);
        // Debug log FormData entries after appending next
        for (const [key, value] of fd.entries()) {
          console.debug(`FormData after append: ${key} = ${value}`);
        }
        await signInWithOAuth(fd);
      } else {
        await signInWithOAuth(new FormData(e.currentTarget));
      }
    } catch (error) {
      console.error('OAuth sign-in error:', error);
      // Don't clear submitting state here as the page might redirect
    }

    setIsSubmitting(null);
  };

  return (
    <div className="mt-8">
      {/* Use FormAlert component for OAuth-specific messages */}
      <FormAlert
        status={status}
        statusDescription={statusDescription}
        error={error}
        errorDescription={errorDescription}
      />

      <div className="flex flex-col space-y-3">
        {oAuthProviders.map((provider) => (
          <form
            key={provider.name}
            onSubmit={(e) => handleSubmit(e, provider.name)}
          >
            <Input type="hidden" name="provider" value={provider.name} />
            <Button
              variant="outline"
              type="submit"
              className={`w-full h-12 font-medium relative group ${provider.bgColor} ${provider.hoverColor} ${provider.textColor} border transition-all duration-150 ease-in-out hover:border-[hsl(var(--hero-yellow))]
                ${provider.name === 'google' ? 'border-gray-200' : 'border-transparent'}`}
              disabled={isSubmitting !== null}
            >
              <div className="flex items-center justify-center">
                <span className="absolute left-4">{provider.icon}</span>
                <span className="group-hover:text-[hsl(var(--hero-yellow))]">
                  {provider.displayName}
                </span>
              </div>
              {isSubmitting === provider.name && (
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <div className="w-5 h-5 border-t-2 border-[hsl(var(--hero-yellow))] rounded-full animate-spin" />
                </div>
              )}
            </Button>
          </form>
        ))}
      </div>
    </div>
  );
}
