// lib/dynamic-imports.tsx - Type-safe dynamic imports
import dynamic from 'next/dynamic';

// ✅ Heavy UI Components - Load only when needed
export const DynamicChart = dynamic(
  () => import('recharts').then((mod) => ({ default: mod.LineChart })),
  {
    loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded" />,
    ssr: false
  }
);

export const DynamicPDFViewer = dynamic(
  () => import('react-pdf').then((mod) => ({ default: mod.Document })),
  {
    loading: () => <div className="animate-pulse bg-gray-200 h-96 rounded" />
  }
);

export const DynamicMarkdown = dynamic(() => import('react-markdown'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-32 rounded" />
});

export const DynamicMotionDiv = dynamic(
  () => import('framer-motion').then((mod) => ({ default: mod.motion.div })),
  {
    ssr: false
  }
);

// ✅ Heavy Form Components
export const DynamicFormProvider = dynamic(
  () =>
    import('react-hook-form').then((mod) => ({ default: mod.FormProvider })),
  {
    loading: () => null
  }
);

// ✅ Type-safe Lucide Icon Components - Using main exports
export const DynamicFileText = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.FileText })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicDownload = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Download })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicUpload = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Upload })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicUser = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.User })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicSettings = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Settings })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicMenu = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Menu })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicX = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.X })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicChevronDown = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.ChevronDown })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicCheck = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Check })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicPlus = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Plus })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicArrowRight = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.ArrowRight })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicArrowLeft = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.ArrowLeft })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicStar = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Star })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicHeart = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Heart })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicEye = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Eye })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicTrash = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Trash })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicEdit = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Edit })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

export const DynamicSearch = dynamic(
  () => import('lucide-react').then((mod) => ({ default: mod.Search })),
  {
    loading: () => <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
  }
);

// ✅ PDF Processing - Server-side only
export const loadPDFJS = async () => {
  if (typeof window === 'undefined') return null;
  const pdfjs = await import('pdfjs-dist');
  return pdfjs;
};

// ✅ Document Processing
export const loadMammoth = async () => {
  const mammoth = await import('mammoth');
  return mammoth;
};

export const loadDocx = async () => {
  const docx = await import('docx');
  return docx;
};

// ✅ Usage Examples:
/*
// Instead of:
import { FileText, Download, User, Search, Settings } from 'lucide-react';
import { LineChart } from 'recharts';
import { motion } from 'framer-motion';

// Use:
import { 
  DynamicFileText, 
  DynamicDownload, 
  DynamicUser,
  DynamicSearch,
  DynamicSettings,
  DynamicChart, 
  DynamicMotionDiv 
} from '@/lib/dynamic-imports';

function MyComponent() {
  return (
    <div>
      <DynamicFileText className="w-4 h-4" />
      <DynamicUser className="w-6 h-6" />
      <DynamicSearch className="w-4 h-4" />
      <DynamicChart data={chartData} />
      <DynamicMotionDiv animate={{ opacity: 1 }}>
        Content
      </DynamicMotionDiv>
    </div>
  );
}
*/
