import type { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        disallow: [
          '/api/*', // Block all API endpoints
          '/dashboard/*', // Block user dashboard
          '/ats-skill-test', // Your existing route
          '/auth/*' // Block auth pages (login/signup/reset)
        ]
      }
    ],
    sitemap: 'https://jobspaceai.com/sitemap.xml'
  };
}
