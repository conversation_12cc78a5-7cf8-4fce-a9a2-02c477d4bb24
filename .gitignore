# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env.production
.env.production.template
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# secrets
secrets.txt
cloudbuild.yaml

# sensitive scripts
scripts/verify-stripe-webhook-secret.js
scripts/test-stripe-webhook.js
scripts/update-stripe-webhook.js

# IDE
.vscode/
.idea/

# Project tracking files
GA4_TRACKING_CHECKLIST.md
