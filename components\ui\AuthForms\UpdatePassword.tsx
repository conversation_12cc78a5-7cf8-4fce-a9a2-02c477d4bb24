'use client';

import { Button } from '@/components/ui/button';
import { updatePassword } from '@/app/utils/auth-helpers/server';
import { useSearchParams } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { Input } from '../input';
import { useToast } from '@/hooks/use-toast';
import FormAlert from './FormAlert';

interface UpdatePasswordProps {
  redirectMethod: string;
}

export default function UpdatePassword({}: Readonly<UpdatePasswordProps>) {
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Get status and error messages from URL parameters
  const status = searchParams.get('status');
  const statusDescription = searchParams.get('status_description');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  // Show toast notifications for errors or success messages
  useEffect(() => {
    if (error && errorDescription) {
      toast({
        title: error,
        description: errorDescription,
        variant: 'destructive'
      });
    } else if (status && statusDescription) {
      // Only show toast for success messages, not informational ones
      if (status.toLowerCase().includes('success')) {
        toast({
          title: status,
          description: statusDescription,
          variant: 'default'
        });
      }
    }
  }, [error, errorDescription, status, statusDescription, toast]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true); // Disable the button while the request is being handled

    try {
      // Get form data
      const formData = new FormData(e.currentTarget);
      const password = formData.get('password') as string;
      const passwordConfirm = formData.get('passwordConfirm') as string;

      // Basic client-side validation
      if (!password || password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      if (password !== passwordConfirm) {
        throw new Error('Passwords do not match');
      }

      // Show success toast before redirecting
      toast({
        title: 'Success',
        description: 'Password updated successfully!',
        variant: 'default'
      });

      // Use direct form submission instead of handleRequest to avoid client-side validation issues
      await updatePassword(formData);

      // Show success message and redirect to sign-in page
      toast({
        title: 'Password Updated',
        description: 'You can now sign in with your new password',
        variant: 'default'
      });

      // Use a form submission to navigate to avoid client-side routing issues
      const form = document.createElement('form');
      form.method = 'GET';

      // Use the current origin to ensure we're using the correct port
      const { origin } = window.location;
      form.action = `${origin}/signin/password_signin`;

      document.body.appendChild(form);
      form.submit();
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update password',
        variant: 'destructive'
      });
      setIsSubmitting(false);
    }
  };

  return (
    <div className="my-8 text-white">
      {/* Use FormAlert component */}
      <FormAlert
        status={status}
        statusDescription={statusDescription}
        error={error}
        errorDescription={errorDescription}
      />

      <form noValidate={true} className="mb-4" onSubmit={handleSubmit}>
        <div className="grid gap-2">
          {/* Hidden email field for accessibility */}
          <input
            type="email"
            name="email"
            autoComplete="username"
            style={{ display: 'none' }}
            aria-hidden="true"
            tabIndex={-1}
          />

          <div className="grid gap-1">
            <label htmlFor="password" className="text-white">
              New Password
            </label>
            <Input
              id="password"
              placeholder="Password"
              type="password"
              name="password"
              autoComplete="new-password"
              className="w-full p-3 rounded-md bg-white/10 border-white/20 text-white auth-input"
              required
              minLength={6}
              disabled={isSubmitting}
            />
            <label htmlFor="passwordConfirm" className="text-white">
              Confirm New Password
            </label>
            <Input
              id="passwordConfirm"
              placeholder="Confirm Password"
              type="password"
              name="passwordConfirm"
              autoComplete="new-password"
              className="w-full p-3 rounded-md bg-white/10 border-white/20 text-white auth-input"
              required
              minLength={6}
              disabled={isSubmitting}
            />
          </div>
          <Button
            type="submit"
            className="mt-1 bg-[hsl(var(--hero-yellow))] hover:bg-[hsl(var(--hero-yellow-light))] text-[hsl(var(--foreground))]"
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            Update Password
          </Button>
        </div>
      </form>
    </div>
  );
}
