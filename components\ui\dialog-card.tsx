// components/ui/dialog-card.tsx
import { DialogContent } from '@/components/ui/dialog';
import { X } from 'lucide-react';
import clsx from 'clsx';

interface DialogCardProps
  extends React.ComponentPropsWithoutRef<typeof DialogContent> {
  /** Text that appears in the gradient header */
  title: string;
  /** Optional JSX rendered to the right of the title (e.g. an icon stack) */
  actions?: React.ReactNode;
  /** When you want the little yellow accent bar on the left of the header */
  accent?: boolean;
}

export function DialogCard({
  title,
  className,
  actions,
  accent = true,
  children,
  ...props
}: DialogCardProps) {
  return (
    <DialogContent
      className={clsx(
        'shadow-sm backdrop-blur-sm bg-black/80 border border-gray-800',
        'p-0 w-[95vw] sm:w-[90vw] max-w-[1300px] h-[90vh] sm:max-h-[90vh]',
        className
      )}
      {...props}
    >
      {/* gradient header */}
      <div className="relative flex items-center justify-between px-6 py-4 bg-gradient-to-r from-gray-900 to-gray-800">
        {accent && (
          <span className="absolute -left-0.5 top-0 h-full w-1.5 bg-[hsl(var(--hero-yellow))] rounded-r-md shadow-[0_0_8px_rgba(246,160,60,0.5)]" />
        )}
        <h2 className="text-lg font-bold text-white drop-shadow-sm">{title}</h2>
        {actions ?? <DialogCard.CloseButton /> /* default X button */}
      </div>

      {/* scrollable body */}
      <div className="flex-1 overflow-y-auto bg-gray-900 p-6">{children}</div>
    </DialogContent>
  );
}

/* Re-export the built-in close primitive so callers don’t need to import it */
DialogCard.CloseButton = function CloseButton() {
  return (
    <button
      className="rounded-full w-8 h-8 flex items-center justify-center text-slate-400 hover:text-white hover:bg-gray-800 transition-colors"
      aria-label="Close dialog"
      data-close
    >
      <X className="h-5 w-5" />
    </button>
  );
};
