'use client';

import Link from 'next/link';
import Image from 'next/image';
import {
  Menu,
  LogOut,
  User as UserIcon,
  Zap,
  Home,
  Settings,
  Info,
  FileText,
  DollarSign,
  LogIn
} from 'lucide-react';
import dynamic from 'next/dynamic';
import { usePathname } from 'next/navigation';
import clsx from 'clsx';
import {
  Sheet,
  SheetTrigger,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle
} from '@/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent
} from '@/components/ui/dropdown-menu';
import { MarketingNavItems } from '@/components/MarketingNav';
import {
  ThemedButton,
  ThemedLinkButton,
  CTAButton,
  HybridButton
} from '@/components/ui/themed-button';

import { useState, useEffect } from 'react';
import { createClient } from '@/app/utils/supabase/client';
import type { User } from '@supabase/supabase-js';
import { signOut } from '@/app/actions/auth';

/* -------------------------------------------------------------------------- */
/*                                   Utils                                    */
/* -------------------------------------------------------------------------- */

const UserSection = dynamic(() => import('./UserSection'), {
  ssr: false,
  loading: () => (
    <div className="h-8 w-8 rounded-full bg-gray-700 animate-pulse" />
  )
});

const useIsActive = () => {
  const pathname = usePathname();
  return (href: string) =>
    href === '/' ? pathname === '/' : pathname?.startsWith(href);
};

/* -------------------------------------------------------------------------- */
/*                               Re‑usable UI                                 */
/* -------------------------------------------------------------------------- */

interface NavLinkProps {
  href: string;
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  active?: boolean;
  compact?: boolean;
}

const NavLink = ({
  href,
  label,
  icon,
  onClick,
  active,
  compact
}: NavLinkProps) => (
  <Link
    href={href}
    aria-current={active ? 'page' : undefined}
    onClick={onClick}
    className={clsx(
      'group flex items-center rounded-xl border border-transparent transition-all duration-200',
      compact
        ? 'gap-3 py-3 px-3 text-sm hover:bg-hero-yellow/10 hover:border-hero-yellow/20'
        : 'gap-4 py-4 px-4 text-lg hover:bg-hero-yellow/10 hover:border-hero-yellow/20 whitespace-nowrap',
      active
        ? 'text-hero-yellow bg-hero-yellow/10 border-hero-yellow/30 shadow-sm'
        : 'text-white/90 hover:text-hero-yellow'
    )}
  >
    {icon && (
      <div
        className={clsx(
          'flex items-center justify-center rounded-lg transition-colors',
          compact ? 'h-6 w-6' : 'h-8 w-8',
          active
            ? 'bg-hero-yellow/20 text-hero-yellow'
            : 'bg-white/10 text-white/70 group-hover:bg-hero-yellow/20 group-hover:text-hero-yellow'
        )}
      >
        {icon}
      </div>
    )}
    <span className="font-medium transition-transform group-hover:translate-x-0.5">
      {label}
    </span>
  </Link>
);

/* ---------------------------- Mobile Feature Group ---------------------------- */

interface MobileFeatureGroupProps {
  label: string;
  open: boolean;
  toggle: () => void;
}

const MobileFeatureGroup: React.FC<MobileFeatureGroupProps> = ({
  label,
  open,
  toggle
}) => (
  <div className="mt-2">
    <button
      className="flex w-full items-center justify-between rounded-xl border border-white/10 bg-white/5 py-3 px-4 text-sm font-medium transition-all hover:bg-hero-yellow/10 hover:border-hero-yellow/20 hover:text-hero-yellow"
      aria-expanded={open}
      onClick={toggle}
    >
      <span className="flex items-center gap-3">
        <div className="flex h-6 w-6 items-center justify-center rounded-lg bg-hero-yellow/20">
          <Settings className="h-4 w-4 text-hero-yellow" />
        </div>
        {label}
      </span>
      <svg
        className={clsx(
          'h-4 w-4 transform text-hero-yellow transition-transform duration-200',
          open && 'rotate-180'
        )}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 9l-7 7-7-7"
        />
      </svg>
    </button>
  </div>
);

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

export default function Header() {
  const supabase = createClient();
  const pathname = usePathname();
  const isActive = useIsActive();

  const [user, setUser] = useState<User | null>(null);
  const [userLoading, setUserLoading] = useState(true);
  const [openSheet, setOpenSheet] = useState(false);
  const [mobileFeaturesOpen, setMobileFeaturesOpen] = useState(false);
  const [desktopFeaturesOpen, setDesktopFeaturesOpen] = useState(false);

  /* -------------------------- Supabase session check ------------------------- */
  useEffect(() => {
    let mounted = true;
    (async () => {
      const { data } = await supabase.auth.getSession();
      if (!mounted) return;
      setUser(data?.session?.user ?? null);
      setUserLoading(false);
    })();

    const { data: sub } = supabase.auth.onAuthStateChange((_, session) => {
      if (!mounted) return;
      setUser(session?.user ?? null);
    });
    return () => {
      mounted = false;
      sub?.subscription.unsubscribe();
    };
  }, [supabase]);

  /* ----------------------------- Auth helpers ------------------------------ */
  const handleSignOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
    await signOut();
  };

  const handleSignIn = () => {
    // Store current path for redirect after login
    const currentPath = window.location.pathname + window.location.search;
    const nextParam = encodeURIComponent(currentPath);

    // Mark that we're trying to log in (for session detection later)
    localStorage.setItem('auth_redirect_pending', 'true');

    // Close the mobile menu
    setOpenSheet(false);

    // Navigate to login page
    window.location.href = `/signin/password_signin?next=${nextParam}`;
  };

  /* --------------------------------- JSX ---------------------------------- */
  return (
    <header className="fixed inset-x-0 top-0 z-50 border-b border-white/10 bg-hero-bg/70 backdrop-blur-md">
      <div className="mx-auto max-w-[1200px]">
        <Sheet open={openSheet} onOpenChange={setOpenSheet}>
          <nav className="relative flex h-16 items-center px-4 md:px-6">
            {/* Left: Mobile trigger + desktop logo */}
            <div className="flex flex-1 items-center gap-3">
              <SheetTrigger asChild>
                <ThemedButton
                  variant="ghost"
                  size="sm"
                  className="lg:hidden text-white hover:bg-white/10 p-2"
                >
                  <Menu className="w-5 h-5" />
                  <span className="sr-only">Open menu</span>
                </ThemedButton>
              </SheetTrigger>

              <Link
                href="/"
                className="hidden min-w-0 items-center gap-2 lg:flex"
              >
                <Image
                  src="/images/logo_light.png"
                  alt="Job Space AI Logo"
                  width={32}
                  height={32}
                  className="h-8 w-8 shrink-0"
                />
                <span className="truncate font-roboto-condensed text-xl font-bold text-white">
                  Job Space AI
                </span>
              </Link>
            </div>

            {/* Mobile centred logo */}
            <div className="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2 lg:hidden">
              <Link href="/" className="flex items-center gap-2">
                <Image
                  src="/images/logo_light.png"
                  alt="Job Space AI Logo"
                  width={28}
                  height={28}
                  className="h-7 w-7 shrink-0"
                  priority
                />
                <span className="font-roboto-condensed text-lg font-bold text-white">
                  Job Space AI
                </span>
              </Link>
            </div>

            {/* Desktop navigation */}
            <div className="hidden flex-1 items-center justify-center lg:flex">
              {/* Features dropdown */}
              <DropdownMenu
                open={desktopFeaturesOpen}
                onOpenChange={setDesktopFeaturesOpen}
              >
                <DropdownMenuTrigger asChild>
                  <button
                    className={clsx(
                      'relative whitespace-nowrap transition-colors after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-hero-yellow after:transition-all gap-4 py-4 px-4 text-lg font-medium',
                      isActive('/platform')
                        ? 'text-yellow-400 after:w-full'
                        : 'text-white hover:text-yellow-300 after:w-0 hover:after:w-full'
                    )}
                  >
                    Features ▾
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className=" rounded-md border border-white/10 bg-slate-800/80 p-2 backdrop-blur">
                  <MarketingNavItems
                    onItemClick={() => setDesktopFeaturesOpen(false)}
                  />
                </DropdownMenuContent>
              </DropdownMenu>

              <NavLink
                href="/about"
                label="About Us"
                active={isActive('/about')}
              />
              <NavLink
                href="/articles"
                label="Articles"
                active={isActive('/articles')}
              />
              <NavLink
                href="/credits"
                label="Pricing"
                active={isActive('/credits')}
              />

              <CTAButton
                href="/onboarding"
                size="sm"
                className="ml-4 whitespace-nowrap"
              >
                Get Started
              </CTAButton>
            </div>

            {/* Right: dashboard + avatar */}
            <div className="flex flex-1 items-center justify-end gap-4">
              {user && !pathname.startsWith('/dashboard') && (
                <ThemedLinkButton
                  href={`/dashboard/${user.id}`}
                  variant="primary"
                  size="sm"
                  className="hidden lg:block"
                >
                  Dashboard
                </ThemedLinkButton>
              )}
              <UserSection />
            </div>
          </nav>

          {/* ------------------------------- MOBILE ------------------------------ */}
          <SheetContent
            side="left"
            aria-label="Navigation panel"
            className="flex max-h-screen w-80 flex-col overflow-hidden bg-gradient-to-b from-hero-bg to-hero-bg/95 text-white p-0 border-r border-white/10"
          >
            <SheetHeader className="sr-only">
              <SheetTitle>Navigation Menu</SheetTitle>
            </SheetHeader>
            {/* User section -------------------------------------------------- */}
            <div className="border-b border-white/10 p-6">
              {userLoading ? (
                <div className="h-24 animate-pulse rounded-2xl bg-white/5" />
              ) : user ? (
                <div className="space-y-4">
                  {/* User info */}
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 shrink-0 items-center justify-center overflow-hidden rounded-2xl bg-gradient-to-br from-hero-yellow to-yellow-500 text-base font-bold text-gray-900 shadow-lg">
                      {user.user_metadata?.avatar_url ? (
                        /* eslint-disable-next-line @next/next/no-img-element */
                        <img
                          src={user.user_metadata.avatar_url}
                          alt="avatar"
                          className="h-full w-full object-cover"
                          onError={(e) =>
                            (e.currentTarget.style.display = 'none')
                          }
                        />
                      ) : (
                        <UserIcon className="h-6 w-6" />
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-base font-semibold text-white">
                        Welcome back!
                      </p>
                      <p className="truncate text-sm text-white/70">
                        {user.email}
                      </p>
                    </div>
                  </div>

                  {/* Quick actions - Using themed buttons */}
                  <div className="flex gap-3">
                    <HybridButton
                      href={`/dashboard/${user.id}`}
                      variant="primary"
                      size="sm"
                      className="flex-1"
                      onClick={() => setOpenSheet(false)}
                    >
                      <UserIcon className="h-4 w-4" />
                      Dashboard
                    </HybridButton>
                    <HybridButton
                      href="/onboarding"
                      variant="secondary"
                      size="sm"
                      className="flex-1"
                      onClick={() => setOpenSheet(false)}
                    >
                      <Zap className="h-4 w-4" />
                      Analyze
                    </HybridButton>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-hero-yellow/20 mx-auto">
                    <UserIcon className="h-8 w-8 text-hero-yellow" />
                  </div>
                  <h3 className="mb-2 text-lg font-semibold text-white">
                    Ready to get started?
                  </h3>
                  <p className="mb-4 text-sm text-white/70">
                    Join thousands of job seekers improving their careers
                  </p>
                  <div className="space-y-3">
                    {/* Get Started Button - Using themed button */}
                    <HybridButton
                      href="/onboarding"
                      variant="primary"
                      size="md"
                      className="w-full"
                      onClick={() => setOpenSheet(false)}
                      showArrow
                    >
                      <Zap className="h-5 w-5" />
                      Get Started Free
                    </HybridButton>
                    {/* Sign In Button - Using themed button */}
                    <ThemedButton
                      variant="secondary"
                      size="md"
                      className="w-full"
                      onClick={handleSignIn}
                    >
                      <LogIn className="h-5 w-5" />
                      Sign In
                    </ThemedButton>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation links ---------------------------------------------- */}
            <div className="flex-1 overflow-y-auto px-6 py-6 space-y-2">
              <NavLink
                href="/"
                label="Home"
                active={pathname === '/'}
                compact
                onClick={() => setOpenSheet(false)}
                icon={<Home className="h-4 w-4" />}
              />

              {/* Features section */}
              <MobileFeatureGroup
                label="Features"
                open={mobileFeaturesOpen}
                toggle={() => setMobileFeaturesOpen((p) => !p)}
              />

              {mobileFeaturesOpen && (
                <div className="ml-4 space-y-1 rounded-xl border border-white/10 bg-white/5 p-3 backdrop-blur">
                  <NavLink
                    compact
                    href="/ai-cv-builder"
                    label="CV Improvement"
                    onClick={() => setOpenSheet(false)}
                  />
                  <NavLink
                    compact
                    href="/ats-resume-checker"
                    label="ATS Checker"
                    onClick={() => setOpenSheet(false)}
                  />
                  <NavLink
                    compact
                    href="/ai-cover-letter"
                    label="Cover Letters"
                    onClick={() => setOpenSheet(false)}
                  />
                  <NavLink
                    compact
                    href="/ai-interview-coach"
                    label="Career Coach"
                    onClick={() => setOpenSheet(false)}
                  />
                  <NavLink
                    compact
                    href="/what-is-ats-uk"
                    label="What is ATS?"
                    onClick={() => setOpenSheet(false)}
                  />
                  <NavLink
                    compact
                    href="/ats-cv-formatting-guide"
                    label="ATS CV Guide"
                    onClick={() => setOpenSheet(false)}
                    active={isActive('/ats-cv-formatting-guide')}
                  />
                  <NavLink
                    compact
                    href="/free-cv-scanner-guide-uk-2025"
                    label="CV Scanner Guide"
                    onClick={() => setOpenSheet(false)}
                    active={isActive('/free-cv-scanner-guide-uk-2025')}
                  />
                  <NavLink
                    compact
                    href="/platform"
                    label="Full Platform"
                    onClick={() => setOpenSheet(false)}
                    active={isActive('/platform')}
                  />
                </div>
              )}

              {/* Other navigation items */}
              <NavLink
                compact
                href="/about"
                label="About Us"
                active={isActive('/about')}
                onClick={() => setOpenSheet(false)}
                icon={<Info className="h-4 w-4" />}
              />
              <NavLink
                compact
                href="/articles"
                label="Articles"
                active={isActive('/articles')}
                onClick={() => setOpenSheet(false)}
                icon={<FileText className="h-4 w-4" />}
              />
              <NavLink
                compact
                href="/credits"
                label="Pricing"
                active={isActive('/credits')}
                onClick={() => setOpenSheet(false)}
                icon={<DollarSign className="h-4 w-4" />}
              />
            </div>

            {/* Sign out button (only for authenticated users) - Using themed button */}
            {user && (
              <div className="border-t border-white/10 p-6">
                <ThemedButton
                  variant="danger"
                  size="md"
                  className="w-full"
                  onClick={() => {
                    setOpenSheet(false);
                    handleSignOut();
                  }}
                >
                  <LogOut className="h-4 w-4" />
                  Sign Out
                </ThemedButton>
              </div>
            )}
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
}
