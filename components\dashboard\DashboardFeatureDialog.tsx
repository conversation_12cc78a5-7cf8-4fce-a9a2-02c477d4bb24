import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogTitle
} from '@/components/ui/dialog';
import { ThemedButton } from '@/components/ui/themed-button';
import { FeatureRenderer } from '@/components/shared/FeatureRenderer';
import { FeatureType, FEATURE_CONFIGS } from '@/app/types/featureTypes';
import Visually<PERSON><PERSON><PERSON> from '@/components/ui/visually-hidden';
import { Loader2 } from 'lucide-react';
import {
  Job,
  Resume,
  DashboardCoverLetterResult,
  FeatureResult
} from '@/app/types/globalTypes';
import { CoverLetterDialog } from '@/components/features/cover-letter/CoverLetterDialog';

interface DashboardFeatureDialogProps {
  isOpen: boolean;
  onClose: () => void;
  featureType: FeatureType;
  result: FeatureResult[keyof FeatureResult] | null;
  error: string | null;
  isPending: boolean;
  job?: Job | null;
  resume?: Resume | null;
}

export const DashboardFeatureDialog: React.FC<DashboardFeatureDialogProps> = ({
  isOpen,
  onClose,
  featureType,
  result,
  error,
  isPending,
  job,
  resume
}) => {
  console.log('DashboardFeatureDialog - Rendering with props:', {
    featureType,
    isOpen,
    hasResult: !!result,
    hasError: !!error,
    isPending
  });

  if (!isOpen) {
    return null;
  }

  // Feature title and description are now handled by FeatureRenderer

  // Function to trigger UI updates when dialog is closed
  const handleClose = () => {
    console.log('DashboardFeatureDialog - Triggering UI update on close');

    // Force localStorage update for this feature
    if (featureType && localStorage) {
      try {
        // Get all localStorage keys
        const keys = Object.keys(localStorage);

        // Find keys that match this feature
        const matchingKeys = keys.filter((key) => {
          // Check if the key contains the feature name in any format
          const normalizedFeature = featureType.replace(/-/g, ' ');
          const upperFeature = normalizedFeature
            .replace(/ /g, '_')
            .toUpperCase();
          return key.includes(upperFeature) || key.includes(normalizedFeature);
        });

        // If we have matching keys, force a UI update
        if (matchingKeys.length > 0) {
          // Dispatch multiple storage events to ensure UI updates
          for (let i = 0; i < 3; i++) {
            setTimeout(() => {
              window.dispatchEvent(new Event('storage'));
            }, i * 100);
          }
        }
      } catch (error) {
        console.error('Error checking localStorage:', error);
      }
    }

    onClose();
  };

  // Determine dialog content based on feature type
  let dialogContent;

  // For cover letter, we need to handle it differently to avoid duplicate headers
  if (featureType === 'coverLetter' && result) {
    console.log('DashboardFeatureDialog - coverLetter result:', result);

    // Ensure the result has the expected structure
    const coverLetterResult: DashboardCoverLetterResult = {
      content:
        'content' in result && typeof result.content === 'string'
          ? result.content
          : 'cover_letter' in result && typeof result.cover_letter === 'string'
            ? result.cover_letter
            : '',
      key_points_highlighted:
        'key_points_highlighted' in result &&
        Array.isArray(result.key_points_highlighted)
          ? result.key_points_highlighted
          : []
    };

    return (
      <CoverLetterDialog
        isOpen={isOpen}
        onClose={handleClose}
        coverLetter={coverLetterResult}
      />
    );
  }

  // Determine content based on loading/error/result state
  if (isPending) {
    dialogContent = (
      <div className="flex flex-col items-center justify-center p-4 sm:p-8 space-y-6 sm:space-y-8 min-h-[300px] sm:min-h-[400px]">
        {/* Animated spinner with glow effect */}
        <div className="relative">
          <div className="absolute inset-0 rounded-full bg-hero-yellow/20 blur-xl animate-pulse"></div>
          <Loader2 className="h-16 w-16 sm:h-20 sm:w-20 animate-spin text-[hsl(var(--hero-yellow))] relative z-10" />
        </div>

        <div className="text-center space-y-3 sm:space-y-4">
          <h3 className="text-xl sm:text-2xl font-semibold text-white">
            Processing
          </h3>
          <p className="text-sm sm:text-base text-slate-300">
            Please wait while we analyze your data...
          </p>
        </div>

        {/* Progress bar */}
        <div className="w-full max-w-md mx-auto">
          <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden">
            <div className="h-full bg-[hsl(var(--hero-yellow))] rounded-full animate-progress shadow-[0_0_8px_rgba(246,160,60,0.5)]"></div>
          </div>
        </div>
      </div>
    );
  } else if (error) {
    dialogContent = (
      <div className="flex flex-col items-center justify-center p-4 sm:p-8 space-y-6 sm:space-y-8 min-h-[300px] sm:min-h-[400px]">
        <div className="relative">
          <div className="absolute inset-0 rounded-full bg-red-500/20 blur-xl animate-pulse"></div>
          <div className="relative z-10 w-16 h-16 sm:w-20 sm:h-20 flex items-center justify-center rounded-full border-2 border-red-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
        </div>
        <div className="text-center space-y-3 sm:space-y-4">
          <h3 className="text-xl sm:text-2xl font-semibold text-white">
            Error
          </h3>
          <p className="text-sm sm:text-base text-slate-300 max-w-md mx-auto">
            {error}
          </p>
        </div>
      </div>
    );
  } else if (result) {
    // For all features, we'll directly render the component without the outer card wrapper
    dialogContent = (
      <div className="flex-1 min-h-0 overflow-hidden">
        <FeatureRenderer
          type={featureType}
          result={result}
          mode="card" // Use card mode to get the component's own styling
          job={job || undefined}
          resume={resume || undefined}
          onClose={onClose}
        />
      </div>
    );
  }

  // Get feature title for accessibility
  const featureTitle = FEATURE_CONFIGS[featureType]?.title || 'Feature';

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) handleClose();
      }}
    >
      <DialogContent className="flex flex-col p-0 rounded-lg w-[95vw] sm:w-[90vw] max-w-[1300px] h-[90vh] sm:h-auto sm:max-h-[90vh] bg-gray-900 border border-gray-800 shadow-lg">
        {/* Add visually hidden DialogTitle for accessibility */}
        <VisuallyHidden>
          <DialogTitle>{featureTitle}</DialogTitle>
        </VisuallyHidden>

        <div
          className="flex-1 overflow-y-auto"
          style={{ maxHeight: 'calc(90vh - 80px)' }}
        >
          {dialogContent}
        </div>

        <DialogFooter className="p-3 sm:p-6 border-t border-gray-800 bg-gray-900 shrink-0">
          <ThemedButton
            onClick={handleClose}
            variant="primary"
            size="sm"
            fullWidth={true}
            className="sm:w-auto h-8 sm:h-10 text-xs sm:text-sm"
          >
            Close
          </ThemedButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
