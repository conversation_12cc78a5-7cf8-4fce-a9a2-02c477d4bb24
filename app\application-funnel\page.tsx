// No longer using redirect - using client-side redirection instead
import { Resume, JobAnalysisResult, Job } from '@/app/types/globalTypes';
import ApplicationFunnelClient from './ApplicationFunnelClient';
import { createClient } from '../utils/supabase/server';
import { revalidatePath } from 'next/cache';
import { Metadata } from 'next';
import { constructMetadata } from '@/lib/seo-config';
import SignInRequired from '@/components/ui/AuthForms/SignInRequired';

// Configure this route as dynamic
export const dynamic = 'force-dynamic';

export const metadata: Metadata = constructMetadata({
  title: 'Application Funnel | Job Space AI',
  description:
    'Track and manage your job applications with our AI-powered application funnel.',
  path: '/application-funnel'
});

export default async function ApplicationFunnelPage() {
  try {
    const supabase = await createClient();

    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
      // Use our new SignInRequired component
      return (
        <SignInRequired
          message="You need to sign in to access the application funnel."
          redirectPath="/signin"
        />
      );
    }

    const { data: resume, error } = await supabase
      .from('user_resumes')
      .select('*')
      .eq('user_id', user?.id)
      .single<Resume>();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching Resume:', error);
    }

    async function saveJob(
      jobDescription: string,
      jobLink: string,
      analysisResult: JobAnalysisResult
    ): Promise<Job | null> {
      'use server';
      try {
        const supabase = await createClient();
        const {
          data: { user }
        } = await supabase.auth.getUser();

        if (!user) {
          throw new Error('User not authenticated');
        }

        // Check if a job with the same link already exists for this user
        const { data: existingJob, error: fetchError } = await supabase
          .from('user_jobs')
          .select('*')
          .eq('user_id', user.id)
          .eq('job_link', jobLink)
          .eq('description', jobDescription)
          .single();

        if (fetchError && fetchError.code !== 'PGRST116') {
          console.error('Error fetching existing job:', fetchError);
          return null;
        }

        const newJobData: Omit<Job, 'id' | 'created_at' | 'updated_at'> = {
          user_id: user.id,
          title: analysisResult.job_title || 'Untitled Job',
          company: analysisResult.company_name || null,
          description: jobDescription,
          job_link: jobLink,
          salary_range: analysisResult.salary_range || null,
          location: analysisResult.location || null,
          analysis_result: analysisResult,
          status: 'saved'
        };

        let result: Job;

        if (existingJob) {
          const hasChanged = (
            Object.keys(newJobData) as Array<keyof typeof newJobData>
          ).some(
            (key) =>
              key !== 'user_id' &&
              key !== 'status' &&
              newJobData[key] !== (existingJob as Job)[key]
          );

          if (hasChanged) {
            const { data, error } = await supabase
              .from('user_jobs')
              .update(newJobData)
              .eq('id', existingJob.id)
              .select()
              .single();

            if (error) {
              console.error('Error updating job:', error);
              return null;
            }
            result = data as Job;
          } else {
            result = existingJob as Job;
          }
        } else {
          const { data, error } = await supabase
            .from('user_jobs')
            .insert(newJobData)
            .select()
            .single();

          if (error) {
            console.error('Error saving new job:', error);
            return null;
          }
          result = data as Job;
        }

        revalidatePath('/dashboard/[id]', 'page');
        return result;
      } catch (error) {
        console.error('Error in saveJob server action:', error);
        return null;
      }
    }

    return (
      <div>
        <ApplicationFunnelClient
          user={user}
          initialResume={resume || null}
          saveJob={saveJob}
        />
      </div>
    );
  } catch (error) {
    console.error('Error in ApplicationFunnelPage:', error);
    // Use our SignInRequired component with a custom error message
    return (
      <SignInRequired
        message="There was an error loading the application funnel."
        redirectPath="/signin"
      />
    );
  }
}
