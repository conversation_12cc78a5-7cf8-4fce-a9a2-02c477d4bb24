// types/events.ts
// ✅ Clean separation of event types to avoid index signature conflicts

// Base event parameters that all events can have
export interface BaseEventParams {
  event_category?: string;
  event_label?: string;
  value?: number;
  page_path?: string;
  page_title?: string;
  page_location?: string;
  user_id?: string;
  session_id?: string;
  engagement_time_msec?: number;
  screen_name?: string;
}

// Simple events that only use primitive types
export interface SimpleEventParams extends BaseEventParams {
  // Using a more specific index signature to avoid conflicts
  custom_parameter?: string;
  [key: `custom_${string}`]: string | number | boolean | undefined;
}

// Item interface for e-commerce
export interface GtagItem {
  item_id?: string;
  item_name?: string;
  item_category?: string;
  item_category2?: string;
  item_category3?: string;
  item_category4?: string;
  item_category5?: string;
  item_brand?: string;
  item_variant?: string;
  price?: number;
  quantity?: number;
  index?: number;
  coupon?: string;
  item_list_id?: string;
  item_list_name?: string;
  location_id?: string;
}

// E-commerce specific events
export interface PurchaseEventParams extends BaseEventParams {
  transaction_id: string;
  value: number;
  currency: string;
  items?: GtagItem[];
  coupon?: string;
  shipping?: number;
  tax?: number;
}

export interface AddToCartEventParams extends BaseEventParams {
  currency: string;
  value: number;
  items: GtagItem[];
}

export interface ViewItemListEventParams extends BaseEventParams {
  item_list_id?: string;
  item_list_name?: string;
  items: GtagItem[];
}

export interface ViewItemEventParams extends BaseEventParams {
  currency?: string;
  value?: number;
  items: GtagItem[];
}

export interface BeginCheckoutEventParams extends BaseEventParams {
  currency: string;
  value: number;
  items: GtagItem[];
  coupon?: string;
}

export interface RemoveFromCartEventParams extends BaseEventParams {
  currency: string;
  value: number;
  items: GtagItem[];
}

// Conversion events
export interface ConversionEventParams extends BaseEventParams {
  conversion_id?: string;
  conversion_label?: string;
  conversion_value?: number;
  conversion_currency?: string;
}

// Search events
export interface SearchEventParams extends BaseEventParams {
  search_term: string;
  items?: GtagItem[];
}

// Custom events with flexible parameters
export interface CustomEventParams extends BaseEventParams {
  custom_parameter_1?: string;
  custom_parameter_2?: string;
  custom_parameter_3?: string;
  // Common custom parameters
  button_location?: string;
  button_text?: string;
  page_section?: string;
  visitor_type?: string;
  conversion_intent?: string;
  cta_clicked?: string;
  visitor_engagement?: string;
  // Allow any custom_ prefixed parameters
  [key: `custom_${string}`]: string | number | boolean | undefined;
}

// Union type for all possible event parameters
export type AllEventParams =
  | SimpleEventParams
  | PurchaseEventParams
  | ViewItemListEventParams
  | ViewItemEventParams
  | BeginCheckoutEventParams
  | ConversionEventParams
  | SearchEventParams
  | CustomEventParams;

// Event name mapping for type safety
export interface EventTypeMap {
  page_view: SimpleEventParams;
  click: SimpleEventParams;
  scroll: SimpleEventParams;
  purchase: PurchaseEventParams;
  view_item_list: ViewItemListEventParams;
  view_item: ViewItemEventParams;
  begin_checkout: BeginCheckoutEventParams;
  search: SearchEventParams;
  conversion: ConversionEventParams;
  custom_event: CustomEventParams;
  // Allow any string for custom events
  [key: string]: AllEventParams;
}

// Type-safe event tracking function signature
export type TrackEventFunction = <T extends keyof EventTypeMap>(
  eventName: T,
  parameters?: EventTypeMap[T]
) => void;

// Helper type for extracting event parameters by event name
export type EventParamsFor<T extends keyof EventTypeMap> = EventTypeMap[T];

// JobSpace AI specific event parameters
export interface JobSpaceEventParams extends BaseEventParams {
  // User journey tracking
  user_journey?: 'onboarding' | 'dashboard_direct';
  journey_stage?:
    | 'awareness'
    | 'interest'
    | 'consideration'
    | 'conversion'
    | 'retention';
  onboarding_step?: number;

  // File upload tracking
  file_type?: 'job_description' | 'cv_resume';
  file_size_kb?: number;
  file_format?: string;
  upload_duration_ms?: number;

  // Analysis tracking
  analysis_type?: 'cv_analysis' | 'ats_check' | 'cover_letter' | 'job_match';
  analysis_time_ms?: number;
  analysis_success?: boolean;

  // Feature usage
  feature_name?:
    | 'cv_analyzer'
    | 'cover_letter_generator'
    | 'interview_prep'
    | 'ats_checker';
  feature_location?: 'dashboard_menu' | 'quick_actions' | 'sidebar' | 'hero';

  // Plan and pricing
  plan_type?: 'free' | 'trial' | 'premium' | 'enterprise';
  plan_id?: string;

  // CTA and engagement
  cta_type?: 'get_started' | 'try_free' | 'view_pricing' | 'sign_up';
  cta_location?: 'hero' | 'features' | 'pricing' | 'navigation';

  // Error tracking
  error_type?:
    | 'upload_failed'
    | 'analysis_failed'
    | 'payment_failed'
    | 'auth_failed';
  error_message?: string;
  error_location?: string;

  // User identification
  user_type?: 'new' | 'returning' | 'trial' | 'paid';
  returning_user?: boolean;

  // Timing and engagement
  time_spent_seconds?: number;
  scroll_depth_percent?: number;

  // Traffic source
  traffic_source?: 'organic' | 'paid' | 'social' | 'direct' | 'referral';
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

// Custom dimension keys for GA4 configuration
export enum CustomDimensionKeys {
  USER_JOURNEY = 'user_journey',
  FILE_TYPE = 'file_type',
  FEATURE_USED = 'feature_name',
  PLAN_TYPE = 'plan_type',
  USER_TYPE = 'user_type',
  ANALYSIS_TYPE = 'analysis_type',
  ERROR_TYPE = 'error_type',
  TRAFFIC_SOURCE = 'traffic_source'
}

// Event name constants for type safety
export const GA4_EVENTS = {
  // Onboarding Journey
  ONBOARDING_START: 'onboarding_start',
  SIGNUP_ATTEMPT: 'signup_attempt',
  SIGNUP_SUCCESS: 'sign_up',
  SIGNUP_FAILURE: 'signup_failure',

  // File Upload
  FILE_UPLOAD_START: 'file_upload_start',
  FILE_UPLOAD_SUCCESS: 'file_upload_success',
  FILE_UPLOAD_FAILURE: 'file_upload_failure',
  UPLOAD_ABANDON: 'upload_abandon',

  // Analysis
  ANALYSIS_START: 'analysis_start',
  ANALYSIS_COMPLETE: 'analysis_complete',
  ANALYSIS_FAILURE: 'analysis_failure',

  // Dashboard Journey
  DASHBOARD_VIEW: 'dashboard_view',
  FEATURE_DISCOVER: 'feature_discover',
  FEATURE_SELECT: 'feature_select',
  FEATURE_USE_START: 'feature_use_start',
  FEATURE_USE_COMPLETE: 'feature_use_complete',
  FEATURE_ABANDON: 'feature_abandon',

  // Engagement
  CTA_CLICK: 'cta_click',
  PRICING_PAGE_VIEW: 'pricing_page_view',
  PLAN_CARD_VIEW: 'plan_card_view',
  PLAN_SELECT: 'plan_select',
  DOCUMENT_DOWNLOAD: 'document_download',
  DOCUMENT_SHARE: 'document_share',

  // Support & Help
  HELP_VIEW: 'help_view',
  SITE_SEARCH: 'site_search',
  FEEDBACK_SUBMIT: 'feedback_submit',

  // Errors
  TECH_ERROR: 'tech_error',
  API_ERROR: 'api_error',
  JOURNEY_DROP_OFF: 'journey_drop_off'
} as const;

// Update EventTypeMap to include JobSpace events
export interface JobSpaceEventTypeMap extends EventTypeMap {
  [GA4_EVENTS.ONBOARDING_START]: JobSpaceEventParams;
  [GA4_EVENTS.SIGNUP_ATTEMPT]: JobSpaceEventParams;
  [GA4_EVENTS.FILE_UPLOAD_START]: JobSpaceEventParams;
  [GA4_EVENTS.ANALYSIS_COMPLETE]: JobSpaceEventParams;
  [GA4_EVENTS.CTA_CLICK]: JobSpaceEventParams;
  [GA4_EVENTS.DASHBOARD_VIEW]: JobSpaceEventParams;
  [GA4_EVENTS.FEATURE_SELECT]: JobSpaceEventParams;
  // Add more as needed
}

// Usage examples for documentation:
/*
  // ✅ Type-safe usage examples:
  
  // Simple event
  trackEvent('click', {
    event_category: 'button',
    event_label: 'signup',
    value: 1
  });
  
  // E-commerce event
  trackEvent('purchase', {
    transaction_id: 'tx_123',
    value: 29.99,
    currency: 'USD',
    items: [{
      item_id: 'product_123',
      item_name: 'Premium Plan',
      price: 29.99,
      quantity: 1
    }]
  });
  
  // Custom event with custom parameters
  trackCustomEvent('custom_event', {
    event_category: 'user_action',
    button_location: 'header',
    page_section: 'navigation',
    value: 42
  });
  */
