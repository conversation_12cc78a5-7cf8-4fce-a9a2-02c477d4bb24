import React from 'react';
import { Metadata } from 'next';
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { <PERSON>, FileText, Lock, UserCheck, Sparkles } from 'lucide-react';
import { constructMetadata } from '@/lib/seo-config';

export const metadata: Metadata = constructMetadata({
  title: 'Privacy Policy | Job Space AI',
  description:
    'Read our Privacy Policy to understand how Job Space AI collects, uses, and protects your personal information.',
  path: '/privacy'
});

const PolicySection = ({
  icon,
  title,
  children
}: {
  icon: React.ReactNode;
  title: string;
  children: React.ReactNode;
}) => (
  <Card className="mb-6 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg text-white overflow-hidden">
    <CardHeader>
      <CardTitle className="flex items-center text-xl font-semibold text-white">
        {icon}
        <span className="ml-2">{title}</span>
      </CardTitle>
    </CardHeader>
    <CardContent className="text-slate-200">{children}</CardContent>
  </Card>
);

export default function PrivacyPolicyPage() {
  return (
    <div className="relative isolate min-h-screen overflow-hidden">
      {/* Background with animated blobs */}
      <div className="absolute inset-0 -z-10 pointer-events-none">
        {/* Subtle background features */}
        <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-50"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000 opacity-50"></div>
        <div className="absolute top-2/3 left-1/2 w-[550px] h-[550px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000 opacity-50"></div>
        <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000 opacity-50"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-16 max-w-[1200px]">
        <div className="text-center mb-12">
          <span className="text-yellow-300 font-semibold inline-flex items-center gap-2">
            <Sparkles className="w-4 h-4" /> LEGAL INFORMATION
          </span>
          <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2.5rem,8vw,4.5rem)]">
            Privacy{' '}
            <span className="text-yellow-400 text-shadow-yellow">Policy</span>
          </h1>
          <p className="text-slate-200 max-w-2xl mx-auto text-[16px] md:text-[18px] mb-8">
            At Job Space AI, we are committed to protecting your privacy and
            ensuring the security of your personal information. This Privacy
            Policy outlines how we collect, use, and safeguard your data.
          </p>
        </div>

        <PolicySection
          icon={<FileText className="w-6 h-6" />}
          title="Information We Collect"
        >
          <p className="mb-4">
            We collect information you provide directly to us, such as:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>Name and contact information</li>
            <li>CV details</li>
            <li>Job preferences</li>
            <li>Account credentials</li>
          </ul>
          <p>
            We also collect certain information automatically when you use our
            platform, including:
          </p>
          <ul className="list-disc pl-6">
            <li>IP address</li>
            <li>Browser type</li>
            <li>Device information</li>
            <li>Usage data</li>
          </ul>
        </PolicySection>

        <PolicySection
          icon={<Shield className="w-6 h-6" />}
          title="How We Use Your Information"
        >
          <p className="mb-2">We use your information to:</p>
          <ul className="list-disc pl-6">
            <li>Provide and improve our services</li>
            <li>Personalize your experience</li>
            <li>Communicate with you about our services</li>
            <li>Analyze usage patterns to enhance our platform</li>
            <li>Comply with legal obligations</li>
          </ul>
        </PolicySection>

        <PolicySection
          icon={<Lock className="w-6 h-6" />}
          title="Data Security"
        >
          <p className="mb-4">
            We implement industry-standard security measures to protect your
            personal information from unauthorized access, disclosure,
            alteration, and destruction.
          </p>
          <p>Our security measures include:</p>
          <ul className="list-disc pl-6">
            <li>Encryption of sensitive data</li>
            <li>Regular security audits</li>
            <li>Access controls and authentication procedures</li>
            <li>Employee training on data protection</li>
          </ul>
        </PolicySection>

        <PolicySection
          icon={<UserCheck className="w-6 h-6" />}
          title="Your Rights"
        >
          <p className="mb-4">You have the right to:</p>
          <ul className="list-disc pl-6 mb-4">
            <li>Access your personal information</li>
            <li>Correct inaccurate or incomplete data</li>
            <li>Request deletion of your data</li>
            <li>Opt-out of certain data collection and use practices</li>
          </ul>
          <p>
            To exercise these rights or for more information about our privacy
            practices, please contact <NAME_EMAIL>.
          </p>
        </PolicySection>

        <Accordion
          type="single"
          collapsible
          className="mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
        >
          <AccordionItem value="updates" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              Policy Updates
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              We may update this Privacy Policy from time to time. We will
              notify you of any significant changes by posting the new Privacy
              Policy on this page and updating the &quot;Last Updated&quot; date
              at the top of this page.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="contact" className="border-white/10">
            <AccordionTrigger className="text-white hover:text-yellow-400 transition-colors py-4 px-6 text-lg font-medium">
              Contact Us
            </AccordionTrigger>
            <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
              If you have any questions or concerns about our Privacy Policy,
              please contact us at:
              <br />
              <br />
              Email:{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-hero-yellow hover:underline hover:text-hero-yellow-light transition-colors"
              >
                <EMAIL>
              </a>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <p className="text-sm text-slate-400 text-center">
          Last Updated: October 3, 2024
        </p>
      </div>
    </div>
  );
}
