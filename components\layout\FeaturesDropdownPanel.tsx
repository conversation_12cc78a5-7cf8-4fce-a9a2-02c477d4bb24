'use client';

import * as React from 'react';
import FeaturesDropdownContent from './FeaturesDropdownContent';

export default function FeaturesDropdownPanel() {
  const [open, setOpen] = React.useState(false);

  React.useEffect(() => {
    const trigger = document.getElementById('features-link');
    if (!trigger) return;

    const onMouseEnter = () => setOpen(true);
    const onMouseLeave = () => setOpen(false);

    trigger.addEventListener('mouseenter', onMouseEnter);
    trigger.addEventListener('mouseleave', onMouseLeave);

    const panel = document.getElementById('features-dropdown-panel');
    if (panel) {
      panel.addEventListener('mouseenter', onMouseEnter);
      panel.addEventListener('mouseleave', onMouseLeave);
    }

    return () => {
      trigger.removeEventListener('mouseenter', onMouseEnter);
      trigger.removeEventListener('mouseleave', onMouseLeave);
      if (panel) {
        panel.removeEventListener('mouseenter', onMouseEnter);
        panel.removeEventListener('mouseleave', onMouseLeave);
      }
    };
  }, []);

  return (
    <div
      id="features-dropdown-panel"
      className={`
        fixed top-16 z-50
        transition-all duration-200 ease-out
        ${
          open
            ? 'opacity-100 translate-y-0 pointer-events-auto'
            : 'opacity-0 -translate-y-2 pointer-events-none'
        }
      `}
      onMouseEnter={() => setOpen(true)}
      onMouseLeave={() => setOpen(false)}
      style={{
        left: '50%',
        transform: 'translateX(-50%)',
        width: '560px',
        minWidth: '560px',
        maxWidth: '560px'
      }}
    >
      <div
        className="
          max-h-[80vh] overflow-y-auto rounded-xl border border-white/20 
          bg-white/10 backdrop-blur-xl text-white shadow-2xl
          p-0 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-white/20
        "
        style={{
          width: '560px',
          minWidth: '560px',
          maxWidth: '560px'
        }}
      >
        <div style={{ width: '560px', minWidth: '560px', maxWidth: '560px' }}>
          <FeaturesDropdownContent />
        </div>
      </div>
    </div>
  );
}
