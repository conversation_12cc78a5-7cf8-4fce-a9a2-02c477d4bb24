import { Suspense, useEffect, useState } from 'react';
import FeatureDisplay from './FeaturesCarousel';
import { FEATURES } from './features-data';
import ProductSchemaMarkup from '../seo/ProductSchemaMarkup';
import SectionHeading, { Highlight } from '../ui/section-heading';

export const FeaturesSection = () => {
  // We'll use this state to control animations if needed later
  const [, setMounted] = useState(false);

  useEffect(() => {
    setTimeout(() => setMounted(true), 100);
  }, []);

  // Create schema markup for the main product (the platform)
  const platformDetails = {
    name: 'Job Space AI Platform',
    description:
      'AI-powered career development platform with CV optimization, ATS analysis, and skill gap analysis tools.',
    image: '/og-image.jpg',
    price: 0, // Free to start
    priceCurrency: 'GBP',
    features: [
      'ATS Analysis',
      'Skill Gap Analysis',
      'CV Optimization',
      'Cover Letter Generation',
      'Interview Preparation'
    ]
  };

  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      }
    >
      <div
        id="features-section"
        className="relative isolate py-16 overflow-hidden"
      >
        {/* Background with blobs similar to Hero */}
        {/* <div className="absolute inset-0 bg-hero-bg/95 home-section-bg">
          {/* Subtle background features */}
        {/* <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-hero-blue-glow blur-3xl animate-blob"></div>
          <div className="absolute bottom-1/3 left-1/3 w-80 h-80 rounded-full bg-hero-purple-glow blur-3xl animate-blob animation-delay-2000"></div>
          <div className="absolute top-2/3 right-1/2 w-72 h-72 rounded-full bg-hero-yellow-glow blur-3xl animate-blob animation-delay-4000"></div>
          <div className="absolute top-1/3 left-1/4 w-56 h-56 rounded-full bg-hero-cyan-glow blur-3xl animate-blob animation-delay-6000"></div> */}
        {/* </div> */}

        <div className="relative z-10 mx-auto w-full px-4">
          <div className=" mx-auto text-center">
            <span className="text-yellow-300 font-semibold">
              POWERFUL FEATURES
            </span>
            <SectionHeading className="text-center max-w-4xl mx-auto">
              Everything You Need to <Highlight>Succeed</Highlight>
            </SectionHeading>
            <p className="text-slate-200 mb-12 text-[16px] md:text-[18px]">
              Advanced tools powered by AI to accelerate your career growth
            </p>
          </div>
          <FeatureDisplay
            features={FEATURES.filter((feature) => !feature.hidden)}
          />

          {/* Add schema markup for SEO */}
          <ProductSchemaMarkup {...platformDetails} />
        </div>
      </div>
    </Suspense>
  );
};
