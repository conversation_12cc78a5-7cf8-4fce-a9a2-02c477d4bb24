// hooks/useScrollDepth.tsx
'use client';

import { useEffect, useRef } from 'react';
import { trackScrollDepth } from '@/lib/ga-events';

interface UseScrollDepthOptions {
  pageName: string;
  thresholds?: number[];
  enabled?: boolean;
}

export function useScrollDepth({
  pageName,
  thresholds = [25, 50, 75, 90],
  enabled = true
}: UseScrollDepthOptions) {
  const trackedThresholds = useRef<Set<number>>(new Set());

  useEffect(() => {
    if (!enabled) return;

    const handleScroll = () => {
      const scrollHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const scrollPercent = Math.round((scrollTop / scrollHeight) * 100);

      thresholds.forEach((threshold) => {
        if (
          scrollPercent >= threshold &&
          !trackedThresholds.current.has(threshold)
        ) {
          trackedThresholds.current.add(threshold);
          trackScrollDepth(threshold, pageName);
        }
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [pageName, thresholds, enabled]);

  // Reset tracking when page changes
  useEffect(() => {
    trackedThresholds.current.clear();
  }, [pageName]);
}
