import type { Metada<PERSON> } from 'next';
import Link from 'next/link';
import JsonLd from '@/components/seo/JsonLd';

import { faqLd, FAQItem } from 'lib/faq';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { CallToAction } from 'components/CallToAction';
import { RecentArticles } from 'components/articles/RecentArticles';
import { ImageCarousel } from 'components/ImageCarousel';

export const metadata: Metadata = {
  title: 'AI CV Builder UK 2025 | JobSpaceAI',
  description:
    'Generate an ATS-optimised CV in under 60 seconds. Trusted by UK job seekers—beat applicant tracking systems with our AI CV Builder.',
  alternates: {
    canonical: '/ai-cv-builder'
  }
};

const faqs: FAQItem[] = [
  {
    question: 'Is the CV really ATS-friendly?',
    answer:
      'Yes. We benchmark against the same parsing engines used by Workday and Greenhouse so your CV structure and keywords are recognised.'
  },
  {
    question: 'Does it work for graduate roles?',
    answer:
      'Absolutely. The builder tailors achievements and keywords to early-career criteria found in entry-level UK job ads.'
  },
  {
    question: 'Can I export to PDF?',
    answer: 'One click downloads a formatted PDF ready to attach or upload.'
  }
];

const sections = [
  { id: 'how-it-works', h2: 'How it works' },
  { id: 'key-features', h2: 'Key features' },
  { id: 'benefits', h2: 'Benefits at a glance' },
  { id: 'faqs', h2: 'Frequently asked questions' }
];

export default function AICvBuilder() {
  return (
    <div className="relative z-10 container px-4 pb-16 max-w-[1200px] rounded-3xl bg-transparent backdrop-blur-sm text-center flex flex-col items-center">
      <div className="relative z-10 container px-4 py-16 max-w-[1200px] rounded-3xl text-center">
        <h1 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] mt-2 mb-6 text-white text-[clamp(2rem,6vw,3.5rem)] sm:text-[clamp(2.5rem,8vw,4.5rem)]">
          AI{' '}
          <span className="text-yellow-400 text-shadow-yellow">
            CV Builder UK
          </span>
        </h1>
        <p className="text-slate-200 max-w-3xl mx-auto text-[14px] sm:text-[16px] md:text-[18px] mb-8 leading-relaxed">
          Our AI CV Builder parses job ads from UK job boards, extracting key
          requirements and keywords. It then matches these keywords against your
          CV content, optimising bullet points to improve ATS compatibility. The
          builder formats your CV specifically for the UK market, ensuring
          recruiter-friendly layouts and terminology. Users improve ATS match
          rates by <strong>35 per cent</strong> on average, helping them get
          noticed faster.
        </p>
        <p className="text-slate-200 max-w-3xl mx-auto text-[14px] sm:text-[16px] md:text-[18px] mb-8 leading-relaxed">
          Generate an ATS-optimised CV in under 60 seconds. Trusted by UK job
          seekers, our AI CV Builder helps you beat applicant tracking systems
          and land more interviews.
        </p>
        <p className="text-slate-200 max-w-3xl mx-auto text-[14px] sm:text-[16px] md:text-[18px] mb-8 leading-relaxed">
          For more insights on navigating the UK job market with AI, read our{' '}
          <Link
            href="/articles/ai-hiring-guide-uk-2025"
            prefetch={false}
            className="text-yellow-400 underline"
          >
            Beat the Bots: AI Hiring Guide UK 2025
          </Link>{' '}
          blog post.
        </p>

        {/* Use ReportsShowcase component for CV improvement images */}
        <ImageCarousel
          items={[
            {
              src: '/images/ai-cv-builder/cv-improvement-2.webp',
              alt: 'ATS-optimized CV improvement example showing keyword matching and formatting'
            },
            {
              src: '/images/ai-cv-builder/cv-improvement-1.webp',
              alt: 'UK job market tailored CV layout with recruiter-friendly terminology'
            },
            {
              src: '/images/ai-cv-builder/cv-improvement-3.webp',
              alt: 'AI CV Builder highlighting bullet point optimization for ATS compatibility'
            },
            {
              src: '/images/ai-cv-builder/cv-improvement-4.webp',
              alt: 'Example of CV formatting that improves ATS parsing and recruiter visibility'
            },
            {
              src: '/images/ai-cv-builder/cv-improvement-5.webp',
              alt: 'CV improvement with industry-specific keyword injection for UK job ads'
            },
            {
              src: '/images/ai-cv-builder/cv-improvement-6.webp',
              alt: 'Real-time ATS compatibility score displayed on optimized CV example'
            },
            {
              src: '/images/ai-cv-builder/cv-improvement-7.webp',
              alt: 'Downloadable ATS-safe CV formats including PDF and Word export'
            }
          ]}
        />

        {sections.map(({ id, h2 }) => {
          if (id === 'how-it-works') {
            return (
              <section id={id} key={id} className="mt-12">
                <h2 className="mb-4 text-2xl font-semibold">{h2}</h2>
                <ol className="space-y-2 list-decimal list-inside mx-auto text-center">
                  <li>
                    <strong>Paste a job link.</strong> We parse requirements
                    from Indeed, LinkedIn or any UK board.
                  </li>
                  <li>
                    <strong>Upload or paste your current CV.</strong> Our
                    transformer model scores every bullet against the job’s
                    keywords.
                  </li>
                  <li>
                    <strong>Generate & download.</strong> One click exports a
                    polished PDF, .docx or Google‑Docs link, already ATS‑safe.
                  </li>
                </ol>
              </section>
            );
          }
          if (id === 'key-features') {
            return (
              <section id={id} key={id} className="mt-12">
                <h2 className="mb-4 text-2xl font-semibold">{h2}</h2>
                <ul className="space-y-1 list-disc list-inside mx-auto text-center">
                  <li>Real‑time ATS compatibility score</li>
                  <li>Industry‑specific keyword injection</li>
                  <li>
                    Automatic formatting (no text boxes or tables that break
                    parsing)
                  </li>
                  <li>PDF & Word export plus cloud share‑link</li>
                </ul>
              </section>
            );
          }
          if (id === 'benefits') {
            return (
              <section id={id} key={id} className="mt-12">
                <h2 className="mb-4 text-2xl font-semibold">{h2}</h2>
                <p>
                  <strong>Stand out fast.</strong> Stop guessing which buzzwords
                  to include; our AI surfaces them instantly. Pair it with a
                  tailored{' '}
                  <Link
                    href="/ai-cover-letter"
                    prefetch={false}
                    className="text-yellow-400 underline ml-1"
                  >
                    cover letter generator
                  </Link>{' '}
                  for a complete, recruiter‑ready application. Also, explore our{' '}
                  <Link
                    href="/platform"
                    prefetch={false}
                    className="text-yellow-400 underline ml-1"
                  >
                    AI job-application platform
                  </Link>{' '}
                  for a seamless hiring experience.
                </p>
                <RecentArticles />
              </section>
            );
          }
          if (id === 'faqs') {
            return (
              <section id={id} key={id} className="mt-12">
                <CallToAction
                  label="Generate your AI‑optimised CV now"
                  href="/signin/signup"
                />
                <h2 className="mb-6 text-2xl font-semibold">{h2}</h2>
                <Accordion
                  type="single"
                  collapsible
                  className=" mb-8 backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-xl overflow-hidden"
                >
                  {faqs.map(({ question, answer }, index) => (
                    <AccordionItem
                      key={index}
                      value={`item-${index}`}
                      className="border-white/10 "
                    >
                      <AccordionTrigger className="text-white  hover:text-yellow-400 transition-colors py-4 px-6 text-sm sm:text-lg font-medium">
                        {question}
                      </AccordionTrigger>
                      <AccordionContent className="text-slate-200 px-6 pb-6 pt-2 leading-relaxed">
                        {answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
                <JsonLd id="faq-schema" data={faqLd('/ai-cv-builder', faqs)} />
                {/* HowTo schema for the “How it works” list */}
                <JsonLd
                  id="howto-ai-cv-builder"
                  data={{
                    '@context': 'https://schema.org',
                    '@type': 'HowTo',
                    name: 'Generate an ATS-friendly CV in 3 steps',
                    description:
                      'Learn how to paste a job ad, upload your CV, and instantly generate a keyword-optimised CV.',
                    step: [
                      {
                        '@type': 'HowToStep',
                        position: 1,
                        name: 'Paste your job link or description',
                        item: 'https://jobspaceai.com/ai-cv-builder#how-it-works'
                      },
                      {
                        '@type': 'HowToStep',
                        position: 2,
                        name: 'Upload or paste your current CV',
                        item: 'https://jobspaceai.com/ai-cv-builder#how-it-works'
                      },
                      {
                        '@type': 'HowToStep',
                        position: 3,
                        name: 'Generate & download your ATS-ready CV',
                        item: 'https://jobspaceai.com/ai-cv-builder#how-it-works'
                      }
                    ]
                  }}
                />

                {/* SoftwareApplication schema so Google knows this is a free web app */}
                <JsonLd
                  id="app-ai-cv-builder"
                  data={{
                    '@context': 'https://schema.org',
                    '@type': 'SoftwareApplication',
                    name: 'JobSpaceAI AI CV Builder',
                    url: 'https://jobspaceai.com/ai-cv-builder',
                    applicationCategory: 'BusinessApplication',
                    operatingSystem: 'Web',
                    offers: {
                      '@type': 'Offer',
                      price: '0',
                      priceCurrency: 'GBP'
                    }
                  }}
                />
              </section>
            );
          }
          return null;
        })}
      </div>
    </div>
  );
}
